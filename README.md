# 🎯 睿课云眸 AI学习监督系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)]()

## 📖 项目简介

睿课云眸AI学习监督系统基于算能BM1684X TPU微服务器构建，通过多模态融合技术实现智能学习监督。系统集成注意力监督、坐姿监测、情绪分析、AI拍照解题、番茄钟时间管理等功能，为学习者提供全方位的智能化学习支持。

### ✨ 核心特性

- **🧠 多模态融合**：三级分析架构，准确率提升14.7%
- **👁️ 三维智能监督**：注意力+坐姿+情绪全方位监测
- **📸 AI拍照解题**：双引擎OCR，12-20秒完整解答
- **🍅 番茄钟管理**：科学时间配置，专注时长提升35%
- **📊 学习分析**：实时监控，数据驱动的学习优化
- **🌐 边缘计算**：100Kbps弱网稳定运行，支持离线模式

## 🚀 快速启动

### 环境要求
- Python 3.8+
- 摄像头设备（内置或外接）
- 网络连接（可选，离线模式下功能受限）

### 安装与启动

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **启动系统**
   ```bash
   python main.py
   ```

3. **系统检查**（可选）
   ```bash
   python system_check.py
   ```

### 跨平台启动脚本
- Windows: `start_windows.bat`
- macOS: `start_macos.command`
- Linux: `start_linux.sh`

## 📚 完整文档

详细的技术文档、使用指南和API说明请参考：
- [睿课云眸AI学习监督系统-完整文档.md](睿课云眸AI学习监督系统-完整文档.md)

## 📋 系统要求

- **操作系统**: Windows 10+ / macOS 10.15+ / Ubuntu 18.04+
- **Python**: 3.8+
- **内存**: 4GB+ RAM（推荐8GB+）
- **摄像头**: 内置或外接摄像头
- **网络**: 可选（离线模式下功能受限）

## 🏗️ 核心组件

- **main.py**: 主启动入口
- **ruike_yunmou.py**: 系统核心模块
- **config.py**: 系统配置管理
- **enhanced_tts.py**: TTS语音合成模块
- **question_ocr.py**: OCR识别模块
- **paddleocr_config.py**: OCR配置模块
- **behavior_prompts.py**: 学习行为提示配置

## 🛠️ 配置说明

在 `config.py` 中配置API密钥：
```python
DEEPSEEK_API_KEY = "your_deepseek_api_key"
QWEN_API_KEY = "your_qwen_api_key"
```

## ❓ 常见问题

- **摄像头无法启动**: 检查摄像头权限和驱动程序
- **AI不回答问题**: 检查网络连接和API密钥配置
- **没有声音**: 检查音频设备和系统音量设置
- **程序运行缓慢**: 关闭其他占用资源的程序

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**更多详细信息请参考完整文档**
