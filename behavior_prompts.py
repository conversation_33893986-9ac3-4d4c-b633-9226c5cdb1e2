#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
行为提示词配置文件 - 中小学生学习监督版
包含各种学习行为的识别模式和对应的语音提示
"""

# 行为类型的语音提示文本 - 人性化优化版本
# 每种行为类型提供3-4个不同表达方式，支持随机选择和上下文适应
import random
from datetime import datetime

BEHAVIOR_VOICE_PROMPTS = {
    "1": [
        "太棒了！你现在的学习状态非常专注，这种投入的态度真的很棒，继续保持下去！",
        "真不错！看到你这么认真学习，我为你感到骄傲。专注的你最美丽，加油！",
        "很好！你正在全神贯注地学习，这种专注力值得称赞。保持这种状态，你一定会有很大收获！",
        "优秀！你的学习态度让人印象深刻，专心致志的样子真的很棒。继续努力，你做得很好！"
    ],
    "2": [
        "我注意到你的思绪好像飘走了一点，没关系，这很正常。深呼吸一下，让我们重新找回专注的感觉吧。",
        "看起来你的注意力有些分散了，这在学习中很常见。试着轻轻摇摇头，重新聚焦到学习内容上来。",
        "感觉你的心思有点不在这里，这完全可以理解。让我们一起调整一下状态，重新投入到学习中去。",
        "我发现你可能在思考其他事情，学习时偶尔走神是正常的。现在让我们把注意力慢慢拉回到课本上吧。"
    ],
    "3": [
        "很棒！你正在使用学习工具，这说明你很会利用资源来提高学习效率。善于使用工具的学生总是更容易成功！",
        "真聪明！看到你熟练地使用学习工具，这种学会借助外力的智慧值得表扬。工具用得好，学习效果会更棒！",
        "不错！你知道在合适的时候使用合适的工具，这是一个很好的学习习惯。继续保持这种灵活的学习方式！",
        "很好！你正在充分利用学习工具，这种主动寻求帮助的态度很值得鼓励。工具是我们学习路上的好伙伴！"
    ],
    "4": [
        "很好！适当的休息和补充水分对学习很重要，你做得很对。休息好了记得继续加油哦！",
        "真贴心！你知道照顾自己的身体，喝水休息是明智的选择。保持水分充足，大脑会更清醒！",
        "不错！你懂得劳逸结合的重要性，适当休息能让学习更有效率。休息片刻后我们继续努力吧！",
        "很棒！你正在给自己补充能量，这种自我关爱的意识很好。身体舒适了，学习状态会更佳！"
    ],
    "5": [
        "我理解手机很有吸引力，但现在是学习时间哦。试着把手机放到一边，让我们专心投入到学习中来吧。",
        "手机确实很有趣，不过现在让我们把注意力转回到学习上来。你的学习比手机更重要，相信你能做到！",
        "我知道放下手机不容易，但你的未来更值得关注。让我们一起克服这个小小的诱惑，专心学习吧！",
        "手机可以稍后再看，现在是属于学习的宝贵时光。相信你有足够的自制力，让我们重新专注起来！"
    ],
    "6": [
        "很好！你们正在进行学习讨论，这种互相交流的学习方式很棒。良好的讨论能让知识理解得更深刻！",
        "真不错！看到你们在交流学习内容，这种合作学习的精神值得鼓励。思维的碰撞会产生更多智慧火花！",
        "很棒！你们的学习讨论很有价值，这种分享和交流能让大家都受益。继续保持这种积极的学习氛围！",
        "优秀！你们正在通过讨论加深理解，这是一种很有效的学习方法。团队学习的力量是无穷的！"
    ],
    "7": [
        "我看到你有些疲倦了，这很正常，学习确实需要消耗很多精力。如果累了，不妨站起来伸展一下，或者做几个深呼吸。",
        "感觉你需要休息一下了，长时间学习确实会让人疲劳。试着活动活动身体，让精神重新振作起来吧！",
        "看起来你的身体在提醒你需要休息了，这是很正常的信号。稍微放松一下，恢复精力后学习效果会更好！",
        "我注意到你可能有点困倦，这说明你已经很努力了。适当休息是为了更好地学习，让我们调整一下状态吧！"
    ],
    "8": [
        "我发现你的注意力被其他事情吸引了，这在学习过程中很常见。让我们温和地把思绪拉回到学习内容上来吧。",
        "看起来有什么东西分散了你的注意力，这完全可以理解。现在让我们一起重新聚焦到学习任务上来。",
        "我注意到你在关注其他事情，偶尔分心是人之常情。深呼吸一下，让我们重新投入到学习中去。",
        "感觉你的心思飘到别处去了，这很正常。让我们慢慢调整注意力，重新专注到当前的学习内容上。"
    ],
    "9": [
        "我看到你在享受小零食，适当的能量补充也是需要的。吃完后记得整理一下桌面，保持学习环境的整洁哦！",
        "看起来你在补充一些能量，这也是照顾自己的一种方式。注意不要让食物影响到学习资料，保持桌面干净整洁！",
        "我理解学习时偶尔想吃点东西，这能帮助保持精力。记得吃完后清理桌面，整洁的环境有助于更好地学习！",
        "看到你在享用零食，适当的营养补充确实重要。请注意保护好学习资料，整洁的学习环境会让你更专注！"
    ],
    "10": [
        "我注意到你离开了座位，可能是有什么重要的事情。处理完后记得回到座位继续学习，保持良好的学习节奏哦！",
        "看起来你需要离开一下，这完全可以理解。等你回来后，我们继续一起努力学习吧！",
        "我发现你不在座位上，可能是去处理一些事情。记得尽快回到学习状态，保持学习的连续性很重要！",
        "看到你暂时离开了，希望一切都好。回来后让我们继续专注学习，保持良好的学习习惯！"
    ],
    "0": [
        "我正在努力了解你当前的状态，请给我一点时间。无论如何，记得保持良好的学习姿态哦！",
        "暂时还不能完全判断你的状态，这没关系。让我们继续观察，同时保持专注的学习态度！",
        "我需要更多信息来了解你的当前状态。不管怎样，继续保持积极的学习心态是最重要的！"
    ]
}

# 行为类型的匹配模式 (与主系统保持一致)
BEHAVIOR_PATTERNS = [
    # 格式：(匹配正则表达式, 行为编号, 行为描述)
    (r'认真学习|专注|做作业|阅读|看书|写字|笔记|听讲', "1", "认真学习"),
    (r'轻度走神|东张西望|发呆|思考|望向窗外', "2", "轻度走神"),
    (r'使用学习工具|尺子|计算器|铅笔盒|工具', "3", "使用学习工具"),
    (r'喝水休息|休息|水杯|喝水|短暂休息', "4", "喝水休息"),
    (r'玩手机分心|手机|玩游戏|社交媒体|分心设备', "5", "玩手机分心"),
    (r'嘴巴明显张开|明显张嘴|张嘴说话|开口说话|说话动作|口型变化|嘴部动作明显|嘴唇明显分开|说话姿态|交谈动作|嘴巴在动|口型明显|明显说话|嘴部有动作|张口说话|开口交流|说话状态|发声动作', "6", "与同学交流学习内容"),
    (r'睡觉或趴桌子|睡觉|趴着|疲倦|休息', "7", "睡觉或趴桌子"),
    (r'其他分心行为|玩耍|做与学习无关的事|分心|注意力不集中', "8", "其他分心行为"),
    (r'吃零食|吃东西|零食|食物|点心|饼干|糖果|巧克力|薯片', "9", "吃零食"),
    (r'不在座位上|离开|不在位置|不在画面中|看不到学生|离开座位|站起来|走动|无人', "10", "不在座位上")
]

# 行为关键词 (与主系统保持一致)
BEHAVIOR_KEYWORDS = {
    "1": ["认真学习", "专注", "做作业", "阅读", "看书", "写字", "笔记", "听讲"],
    "2": ["轻度走神", "东张西望", "发呆", "思考", "望向窗外"],
    "3": ["使用学习工具", "尺子", "计算器", "铅笔盒", "工具"],
    "4": ["喝水休息", "休息", "水杯", "喝水", "短暂休息"],
    "5": ["玩手机分心", "手机", "玩游戏", "社交媒体", "分心设备"],
    "6": ["嘴巴明显张开", "明显张嘴", "张嘴说话", "开口说话", "说话动作", "口型变化", "嘴部动作明显", "嘴唇明显分开", "说话姿态", "交谈动作", "嘴巴在动", "口型明显", "明显说话", "嘴部有动作", "张口说话", "开口交流", "说话状态", "发声动作"],
    "7": ["睡觉或趴桌子", "睡觉", "趴着", "疲倦", "休息"],
    "8": ["其他分心行为", "玩耍", "做与学习无关的事", "分心", "注意力不集中"],
    "9": ["吃零食", "吃东西", "零食", "食物", "点心", "饼干", "糖果", "巧克力", "薯片"],
    "10": ["不在座位上", "离开", "不在位置", "不在画面中", "看不到学生", "离开座位", "站起来", "走动", "无人"]
}

# 行为类型的描述表 (与主系统保持一致)
BEHAVIOR_DESCRIPTIONS = {
    "1": "认真学习",
    "2": "轻度走神",
    "3": "使用学习工具",
    "4": "喝水休息",
    "5": "玩手机分心",
    "6": "与同学交流学习内容",
    "7": "睡觉或趴桌子",
    "8": "其他分心行为",
    "9": "吃零食",
    "10": "不在座位上",
    "0": "未识别"
}

# 欢迎语音提示 - 人性化优化版本
WELCOME_MESSAGES = [
    "你好！我是你的学习伙伴，很高兴能陪伴你一起学习。我会温柔地关注你的状态，在需要的时候给你一些贴心的提醒。让我们一起创造美好的学习时光吧！",
    "嗨！欢迎来到学习时间！我是你的专属学习助手，会像朋友一样陪伴在你身边。我的目标是帮助你保持最佳的学习状态，让学习变得更加轻松愉快。",
    "很开心见到你！我是你的智能学习伙伴，会用最温暖的方式关心你的学习状态。无论遇到什么困难，记住我都在这里支持你。让我们一起努力，享受学习的乐趣！",
    "你好呀！我是你的贴心学习助手，就像一个关心你的朋友一样。我会观察你的学习状态，在合适的时候给你鼓励和建议。相信今天又会是充实而美好的学习时光！"
]

def get_welcome_message():
    """获取随机的欢迎消息"""
    current_hour = datetime.now().hour
    base_message = random.choice(WELCOME_MESSAGES)

    # 根据时间添加问候
    if current_hour < 12:
        greeting = "早上好！"
    elif current_hour < 18:
        greeting = "下午好！"
    else:
        greeting = "晚上好！"

    return f"{greeting} {base_message}"

# 启动时显示的介绍文本 (更新为完整的10种行为)
INTRO_MESSAGE = """学习助手已启动！

我会关注以下行为并给予适当提醒：
1. 认真学习 - 鼓励你继续保持专注
2. 轻度走神 - 提醒你重新集中注意力
3. 使用学习工具 - 鼓励善用工具提高效率
4. 喝水休息 - 适当休息有助于恢复精力
5. 玩手机分心 - 提醒你放下手机专心学习
6. 与同学交流学习内容 - 良好讨论加深理解
7. 睡觉或趴桌子 - 建议站起来活动一下
8. 其他分心行为 - 督促你回到学习状态
9. 吃零食 - 提醒注意学习环境整洁
10. 不在座位上 - 提醒回到座位继续学习

提示：家长可以点击"开始"按钮启动监控"""

# 坐姿相关的语音提示 - 人性化优化版本
# 提供温和、鼓励性的坐姿指导，避免命令式语言
POSTURE_VOICE_PROMPTS = {
    "1": [
        "你的坐姿很棒！保持这种端正的姿态对身体健康很有益，继续保持这个好习惯！",
        "真不错！你现在的坐姿很标准，这样的姿态既优雅又健康。你做得很好！",
        "很好！你的坐姿非常端正，这种良好的习惯会让你受益终生。继续保持！",
        "优秀！你的坐姿堪称典范，这样既能保护脊椎又能提升学习效率。真棒！"
    ],
    "2": [
        "我注意到你的头部稍微前倾了一点，试着轻轻抬起头部，让颈椎更舒适一些。这样学习会更轻松哦！",
        "感觉你的头部位置可以稍微调整一下，想象有一朵花在头顶，轻轻抬起头来闻花香。这样颈椎会更健康！",
        "你的头部稍微向前了一些，不妨试着抬起下巴，让颈部保持自然的曲线。这个小调整会让你更舒适！",
        "我发现你可能需要调整一下头部位置，轻柔地抬起头部，让视线更加平视。你的颈椎会感谢你的！"
    ],
    "3": [
        "我看到你的肩膀有点不平衡，试着轻轻耸耸肩，然后放松下来，让两边肩膀保持同样的高度。",
        "你的肩膀稍微有些倾斜，深呼吸一下，让肩膀自然放松，保持平衡的状态会更舒适。",
        "感觉你的肩膀需要调整一下，想象肩膀上各放着一本书，保持平衡不让书掉下来。这样会更健康！",
        "我注意到你的肩膀位置可以优化一下，轻轻转动肩膀，然后让它们自然地保持平衡状态。"
    ],
    "4": [
        "我发现你的背部稍微弯曲了，试着想象有一根温柔的丝线从头顶轻轻向上拉，让脊椎自然挺直。",
        "你的背部可以稍微挺直一些，深呼吸时让胸部自然挺起，这样既优雅又健康。",
        "感觉你需要调整一下背部姿态，想象自己是一棵挺拔的小树，让脊椎保持自然的S型曲线。",
        "我注意到你的背部稍微驼了一点，轻柔地挺直背部，让身体保持优美的线条。你会感觉更有精神！"
    ],
    "5": [
        "我看到你的身体稍微向一侧倾斜了，试着调整一下重心，让身体保持平衡的状态。这样会更稳定！",
        "你的身体姿态可以稍微调整一下，想象自己坐在云朵上，保持轻盈平衡的感觉。",
        "感觉你的身体重心有点偏移，轻轻调整一下坐姿，让身体回到中心位置。平衡的姿态更优美！",
        "我注意到你可能需要调整身体平衡，深呼吸一下，让身体自然地回到正中的位置。"
    ],
    "6": [
        "我发现你坐得有点太放松了，试着稍微挺直一些身体，保持适度的精神状态。这样学习效果会更好！",
        "你的坐姿稍微松懈了一点，想象自己是一位优雅的学者，保持端庄而放松的姿态。",
        "感觉你需要稍微调整一下身体的紧张度，不要太紧也不要太松，找到最舒适的平衡点。",
        "我注意到你可能坐得太随意了，轻轻调整一下姿态，让身体保持既放松又有精神的状态。"
    ]
}

# 情绪相关的语音提示 - 人性化优化版本
# 提供温暖、支持性的情绪关怀，注重心理健康
EMOTION_VOICE_PROMPTS = {
    "疲惫": {
        "强烈": [
            "我看到你很疲惫，这说明你已经很努力了。现在最重要的是好好休息，让身心都得到放松。",
            "你看起来真的很累了，辛苦了！不如休息5-10分钟，做一些轻松的伸展运动，让精神重新振作起来。",
            "感觉你需要好好休息一下了，长时间的专注学习确实很消耗精力。适当的休息是为了更好地前进。",
            "我注意到你的疲惫状态，这是身体在提醒你需要关爱自己。休息一下，喝点水，你值得被好好照顾。"
        ],
        "中等": [
            "看起来你有些疲惫了，这很正常。试着深呼吸几次，让身体稍微放松一下。",
            "我发现你可能需要稍微休息一下，适当的放松能让学习效果更好。",
            "感觉你有点累了，不妨站起来活动活动，让血液循环一下，精神会更好。",
            "你似乎有些疲劳，这说明你很认真在学习。记得劳逸结合，保持良好的学习节奏。"
        ]
    },
    "焦虑": [
        "我感觉到你可能有些紧张，这完全可以理解。深呼吸几次，告诉自己：我可以慢慢来，一步一步地解决问题。",
        "看起来你有些焦虑，学习中遇到挑战时这很正常。记住，每个人都有自己的节奏，不要给自己太大压力。",
        "我注意到你可能感到有些不安，试着放慢节奏，专注于当下这一刻。你比你想象的更有能力。",
        "感觉你有点紧张，这说明你很在意学习。放松一下肩膀，相信自己，你一定能够克服困难。"
    ],
    "沮丧": [
        "我看到你可能有些沮丧，学习路上遇到挫折是很正常的。每一次困难都是成长的机会，你很勇敢。",
        "感觉你心情有些低落，这完全可以理解。记住，暂时的困难不代表永远，你有能力度过这个阶段。",
        "我注意到你可能感到有些失落，这种感受很真实也很正常。给自己一些时间和耐心，你会重新找到动力的。",
        "看起来你遇到了一些挑战，这让你感到沮丧。但请记住，每个成功的人都经历过这样的时刻，你并不孤单。"
    ],
    "愤怒": [
        "我感觉到你可能有些情绪激动，这很正常。试着慢慢深呼吸，让心情平静下来，平和的心态更有利于学习。",
        "看起来你有些生气，可能是遇到了什么困难。先让自己冷静一下，然后我们一起面对问题。",
        "我注意到你的情绪有些波动，这说明你很在意。深呼吸几次，让内心重新找到平衡。",
        "感觉你可能有些烦躁，学习中遇到挫折时这很常见。给自己一点时间调整，你有能力处理好这些情绪。"
    ],
    "专注": [
        "太棒了！你现在的专注状态非常好，这种投入的学习态度真的很值得赞赏。继续保持！",
        "很好！你的专注力让人印象深刻，这种全神贯注的状态是学习成功的关键。你做得很棒！",
        "优秀！你现在的学习状态很理想，专注而投入。这种态度会带来很好的学习效果。",
        "真不错！看到你这么专注地学习，我为你感到骄傲。保持这种状态，你一定会有很大收获！"
    ]
}

# 时间相关的问候语和上下文适应
TIME_BASED_GREETINGS = {
    "morning": ["早上好", "上午好", "新的一天开始了"],
    "afternoon": ["下午好", "午后时光"],
    "evening": ["晚上好", "夜晚学习"]
}

# 鼓励性的正面强化语句
POSITIVE_REINFORCEMENTS = [
    "你真的很棒！",
    "做得很好！",
    "继续保持！",
    "你的努力我都看到了！",
    "真为你感到骄傲！",
    "你的进步很明显！"
]

# 温和的引导语句
GENTLE_GUIDANCE = [
    "让我们一起",
    "不妨试试",
    "或许可以",
    "建议你",
    "我们来"
]

def get_random_voice_prompt(behavior_num, context=None):
    """
    获取随机的语音提示，支持上下文适应

    Args:
        behavior_num: 行为编号
        context: 上下文信息，包含时间、频率等

    Returns:
        str: 个性化的语音提示
    """
    if behavior_num not in BEHAVIOR_VOICE_PROMPTS:
        return f"行为监测：未知行为{behavior_num}"

    prompts = BEHAVIOR_VOICE_PROMPTS[behavior_num]
    if not isinstance(prompts, list):
        return prompts

    # 基础随机选择
    base_prompt = random.choice(prompts)

    # 如果有上下文信息，进行适应性调整
    if context:
        base_prompt = adapt_prompt_to_context(base_prompt, context, behavior_num)

    return base_prompt

def adapt_prompt_to_context(prompt, context, behavior_num):
    """
    根据上下文调整语音提示

    Args:
        prompt: 基础提示语
        context: 上下文信息
        behavior_num: 行为编号

    Returns:
        str: 适应上下文的提示语
    """
    adapted_prompt = prompt

    # 时间适应
    current_hour = datetime.now().hour
    if current_hour < 12:
        time_greeting = random.choice(TIME_BASED_GREETINGS["morning"])
    elif current_hour < 18:
        time_greeting = random.choice(TIME_BASED_GREETINGS["afternoon"])
    else:
        time_greeting = random.choice(TIME_BASED_GREETINGS["evening"])

    # 频率适应 - 如果是重复行为，使用更温和的语言
    if context.get("frequency", 0) > 3:
        if behavior_num in ["2", "5", "7", "8"]:  # 需要纠正的行为
            gentle_start = random.choice(GENTLE_GUIDANCE)
            adapted_prompt = f"{gentle_start}{adapted_prompt.lower()}"

    # 持续时间适应 - 长时间学习后给予更多鼓励
    if context.get("duration_minutes", 0) > 30:
        if behavior_num == "1":  # 认真学习
            reinforcement = random.choice(POSITIVE_REINFORCEMENTS)
            adapted_prompt = f"{reinforcement} {adapted_prompt}"

    # 学习时段适应
    if current_hour >= 20:  # 晚上学习
        if behavior_num == "7":  # 疲倦
            adapted_prompt = adapted_prompt.replace("站起来活动", "适当休息，不要太晚睡觉")

    return adapted_prompt

def get_random_posture_prompt(posture_type, context=None):
    """
    获取随机的坐姿提示

    Args:
        posture_type: 坐姿类型
        context: 上下文信息

    Returns:
        str: 个性化的坐姿提示
    """
    if str(posture_type) not in POSTURE_VOICE_PROMPTS:
        return f"坐姿监测：未知坐姿类型{posture_type}"

    prompts = POSTURE_VOICE_PROMPTS[str(posture_type)]
    base_prompt = random.choice(prompts)

    # 根据时间调整语言强度
    current_hour = datetime.now().hour
    if current_hour >= 20:  # 晚上更温和
        base_prompt = base_prompt.replace("试着", "轻轻地").replace("不妨", "可以")

    return base_prompt

def get_random_emotion_prompt(emotion_type, intensity=None, context=None):
    """
    获取随机的情绪提示

    Args:
        emotion_type: 情绪类型
        intensity: 情绪强度
        context: 上下文信息

    Returns:
        str: 个性化的情绪提示
    """
    if emotion_type not in EMOTION_VOICE_PROMPTS:
        return f"情绪关怀：检测到{emotion_type}情绪"

    emotion_prompts = EMOTION_VOICE_PROMPTS[emotion_type]

    # 如果有强度信息且情绪支持强度分级
    if isinstance(emotion_prompts, dict) and intensity:
        if intensity in emotion_prompts:
            prompts = emotion_prompts[intensity]
        else:
            # 如果没有对应强度，使用中等强度或第一个可用的
            prompts = emotion_prompts.get("中等", list(emotion_prompts.values())[0])
    elif isinstance(emotion_prompts, list):
        prompts = emotion_prompts
    else:
        return f"情绪关怀：检测到{emotion_type}情绪"

    return random.choice(prompts)