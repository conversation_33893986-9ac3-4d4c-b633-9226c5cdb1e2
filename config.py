#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
睿课云眸系统配置文件
==================
集中管理系统的各种配置参数
"""

import os
from pathlib import Path

# 基础配置
class Config:
    """基础配置类"""
    
    # 应用信息
    APP_NAME = "睿课云眸 AI学习监督系统"
    APP_VERSION = "v3.0"
    APP_DESCRIPTION = "基于AI视觉识别和语音交互的学习监督系统"
    
    # 项目路径
    PROJECT_ROOT = Path(__file__).parent
    LOGS_DIR = PROJECT_ROOT / "logs"
    TEMP_DIR = PROJECT_ROOT / "temp"
    
    # 确保目录存在
    LOGS_DIR.mkdir(exist_ok=True)
    TEMP_DIR.mkdir(exist_ok=True)
    
    # 界面配置
    WINDOW_TITLE = f"{APP_NAME} {APP_VERSION}"
    WINDOW_SIZE = "1400x900"
    WINDOW_MIN_SIZE = (1200, 800)
    
    # 主题配置
    THEME_MODE = "light"  # "light" 或 "dark"
    THEME_COLOR = "#4A90E2"
    
    # 布局比例（左侧:右侧 = 7:3）
    LEFT_PANEL_WEIGHT = 7
    RIGHT_PANEL_WEIGHT = 3
    
    # 摄像头配置
    CAMERA_INDEX = 0
    CAMERA_WIDTH = 640
    CAMERA_HEIGHT = 480
    CAMERA_FPS = 30
    
    # 图像分析配置
    ANALYSIS_INTERVAL = 30  # 秒
    SCREENSHOT_COUNT = 5
    SCREENSHOT_INTERVAL = 0.2
    
    # API配置（请在实际使用时填入真实的API信息）
    DEEPSEEK_API_KEY = "your_deepseek_api_key_here"
    DEEPSEEK_BASE_URL = "https://api.deepseek.com"
    
    QWEN_API_KEY = "your_qwen_api_key_here"
    QWEN_BASE_URL = "https://dashscope.aliyuncs.com/api/v1"
    
    # OSS配置（阿里云对象存储）
    OSS_ACCESS_KEY_ID = "your_oss_access_key_id"
    OSS_ACCESS_KEY_SECRET = "your_oss_access_key_secret"
    OSS_BUCKET_NAME = "your_bucket_name"
    OSS_ENDPOINT = "oss-cn-beijing.aliyuncs.com"
    
    # TTS配置
    TTS_VOICE = "zh-CN-XiaoxiaoNeural"  # Edge TTS 声音
    TTS_RATE = "+0%"
    TTS_VOLUME = "+0%"
    
    # 内存管理
    MEMORY_THRESHOLD_MB = 300
    MEMORY_CHECK_INTERVAL = 60  # 秒
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = LOGS_DIR / "app.log"
    BEHAVIOR_LOG_FILE = "behavior_log.txt"
    
    # 行为类型配置
    BEHAVIOR_TYPES = {
        1: "认真学习",
        2: "轻度走神", 
        3: "严重走神",
        4: "打瞌睡",
        5: "使用手机",
        6: "离开座位",
        7: "与他人交谈",
        8: "其他分心行为"
    }
    
    # 行为提示语配置
    BEHAVIOR_PROMPTS = {
        1: "太棒了！同学正在认真学习，继续保持这种专注力。",
        2: "同学注意力有些分散，建议调整一下思绪，重新集中注意力。",
        3: "同学明显走神了，请尽快调整状态，回到学习中来。",
        4: "检测到同学可能困倦，建议稍作休息或调整坐姿保持清醒。",
        5: "请放下手机，专心学习。手机会分散你的注意力。",
        6: "请回到座位上继续学习，保持良好的学习习惯。",
        7: "请停止交谈，专心学习。可以在休息时间与同学交流。",
        8: "同学注意力分散在其他事情上，请尽快回到学习状态。"
    }

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = "DEBUG"

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = "INFO"

# 根据环境变量选择配置
def get_config():
    """获取当前配置"""
    env = os.getenv('RUIKE_ENV', 'development')
    if env == 'production':
        return ProductionConfig()
    else:
        return DevelopmentConfig()

# 默认配置实例
config = get_config() 