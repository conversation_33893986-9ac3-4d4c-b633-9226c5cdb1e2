#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版TTS模块 - 支持Edge TTS和系统TTS
可切换更加自然的声音，兼容Windows和macOS平台
"""

import os
import sys
import time
import asyncio
import pygame
import subprocess
from threading import Thread
import traceback
import requests
import platform

# 检查edge-tts库是否安装
EDGE_TTS_AVAILABLE = False
try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    print("注意: edge-tts库未安装，无法使用Microsoft Edge TTS服务")
    print("正在尝试自动安装edge-tts...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "edge-tts"])
        print("Edge TTS安装成功，正在导入...")
        import edge_tts
        EDGE_TTS_AVAILABLE = True
    except:
        print("自动安装失败，如需更好的语音效果，请手动运行: pip install edge-tts")

# Windows平台特有依赖
if platform.system() == 'Windows':
    try:
        import pyttsx3
        PYTTSX3_AVAILABLE = True
        print("Windows TTS引擎(pyttsx3)已加载")
    except ImportError:
        PYTTSX3_AVAILABLE = False
        print("注意: pyttsx3库未安装，Windows系统将使用替代方式进行语音合成")
        print("正在尝试自动安装pyttsx3...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyttsx3"])
            import pyttsx3
            PYTTSX3_AVAILABLE = True
            print("pyttsx3安装成功并已加载")
        except:
            print("自动安装pyttsx3失败，将使用备用方法")
        
    try:
        import comtypes
    except ImportError:
        print("正在安装comtypes库，用于Windows语音合成...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "comtypes"])
            print("comtypes安装成功")
        except:
            print("comtypes安装失败，部分Windows语音功能可能受限")

class EnhancedTTS:
    """增强版TTS类，支持多种语音引擎"""

    def __init__(self, voice_type="edge", system_voice="Meijia"):
        """
        初始化TTS系统
        voice_type: 语音类型，可选 "edge"(Edge TTS) 或 "system"(系统TTS)
        system_voice: 如果使用系统TTS，指定的声音名称
        """
        self.platform = platform.system().lower()  # 使用platform模块判断系统类型
        self.voice_type = "edge"  # 强制使用Edge TTS
        self.system_voice = system_voice  # 备用
        self.edge_voice = "zh-CN-XiaoxiaoNeural"  # 强制使用小小(女声)
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 1  # 重试间隔秒数
        
        # Windows TTS引擎初始化
        self.windows_engine = None
        if self.platform == 'windows' and PYTTSX3_AVAILABLE:
            try:
                self.windows_engine = pyttsx3.init()
                voices = self.windows_engine.getProperty('voices')
                # 尝试找到中文声音
                chinese_voice = None
                for voice in voices:
                    if "chinese" in voice.name.lower() or "zh-" in voice.id.lower():
                        chinese_voice = voice
                        break
                # 如果找到中文声音，设置为默认
                if chinese_voice:
                    self.windows_engine.setProperty('voice', chinese_voice.id)
                    print(f"Windows TTS使用中文声音: {chinese_voice.name}")
                # 设置语速和音量
                self.windows_engine.setProperty('rate', 180)    # 语速
                self.windows_engine.setProperty('volume', 1.0)  # 音量
            except Exception as e:
                print(f"Windows TTS引擎初始化失败: {e}")
                self.windows_engine = None
        
        # 检查网络连接
        self._check_network()
        
        # 检查edge-tts是否可用
        if EDGE_TTS_AVAILABLE:
            print(f"初始化增强版TTS，使用Edge TTS，声音: {self.edge_voice}")
        else:
            print(f"Edge TTS不可用，将使用系统TTS备用，声音: {self.system_voice}")
            
        # 初始化pygame用于播放
        try:
            pygame.mixer.init()
            self.pygame_available = True
            print("pygame音频系统初始化成功")
        except Exception as e:
            print(f"pygame初始化失败: {e}")
            self.pygame_available = False

        # 清理旧的临时文件
        self._cleanup_old_files()

    def _check_network(self):
        """增强的网络连接检查"""
        test_urls = [
            "https://speech.platform.bing.com",  # Edge TTS主要服务器
            "https://edge.microsoft.com",        # 备用检查
            "https://www.microsoft.com",         # 最后备用
        ]

        for url in test_urls:
            try:
                response = requests.get(url, timeout=5, verify=False)
                if response.status_code == 200:
                    self.network_available = True
                    print(f"✅ 网络连接正常 (通过 {url})")
                    return True
            except requests.exceptions.SSLError as ssl_error:
                print(f"🔒 SSL错误访问 {url}: {ssl_error}")
                continue
            except requests.exceptions.ConnectionError as conn_error:
                print(f"🌐 连接错误访问 {url}: {conn_error}")
                continue
            except requests.exceptions.Timeout:
                print(f"⏰ 访问 {url} 超时")
                continue
            except Exception as e:
                print(f"❌ 访问 {url} 失败: {e}")
                continue

        self.network_available = False
        print("⚠️ 警告: 所有网络连接测试均失败，Edge TTS可能无法正常工作")
        print("💡 建议检查网络连接或防火墙设置")
        return False

    async def _edge_synthesize(self, text, output_file):
        """使用Edge TTS合成语音 - 增强SSL连接处理"""
        try:
            # 创建SSL上下文，处理连接问题
            import ssl
            import aiohttp

            # 配置SSL上下文，允许更宽松的SSL验证
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # 配置连接器，增强连接稳定性
            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            # 创建超时配置
            timeout = aiohttp.ClientTimeout(total=30, connect=10)

            # 使用自定义连接器创建Communicate对象
            communicate = edge_tts.Communicate(text, self.edge_voice)

            # 保存语音文件
            await communicate.save(output_file)

            # 验证文件是否成功生成
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                print(f"✅ Edge TTS合成成功，文件大小: {os.path.getsize(output_file)} 字节")
                return True
            else:
                print("❌ Edge TTS合成失败：文件未生成或为空")
                return False

        except ssl.SSLError as ssl_error:
            print(f"🔒 SSL连接错误: {ssl_error}")
            return False
        except aiohttp.ClientConnectorError as conn_error:
            print(f"🌐 连接错误: {conn_error}")
            return False
        except asyncio.TimeoutError:
            print("⏰ Edge TTS连接超时")
            return False
        except Exception as e:
            error_msg = str(e)
            if "ssl" in error_msg.lower() or "certificate" in error_msg.lower():
                print(f"🔒 SSL证书相关错误: {e}")
            elif "connection" in error_msg.lower() or "reset" in error_msg.lower():
                print(f"🌐 网络连接重置错误: {e}")
            else:
                print(f"❌ Edge TTS合成异常: {e}")
            return False

    def _get_event_loop(self):
        """获取合适的事件循环"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            return loop, False
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop, True

    def _clean_file(self, output_file):
        """增强的临时文件清理"""
        if not output_file:
            return False

        try:
            if os.path.exists(output_file):
                # 检查文件是否被占用
                try:
                    # 尝试重命名文件来检查是否被占用
                    temp_name = f"{output_file}.tmp"
                    os.rename(output_file, temp_name)
                    os.rename(temp_name, output_file)
                except OSError:
                    print(f"⚠️ 文件被占用，稍后重试删除: {output_file}")
                    time.sleep(0.1)

                # 强制删除文件
                os.remove(output_file)
                print(f"🗑️ 临时文件已删除: {output_file}")
                return True
            else:
                print(f"📁 文件不存在，无需删除: {output_file}")
                return True

        except PermissionError:
            print(f"🔒 权限不足，无法删除文件: {output_file}")
        except FileNotFoundError:
            print(f"📁 文件已不存在: {output_file}")
            return True
        except Exception as e:
            print(f"❌ 删除临时文件失败: {output_file}, 错误: {e}")

        return False

    def _cleanup_old_files(self):
        """清理旧的临时文件"""
        try:
            current_time = time.time()
            for filename in os.listdir('.'):
                if filename.startswith('output_') and (filename.endswith('.mp3') or filename.endswith('.wav')):
                    file_path = os.path.join('.', filename)
                    try:
                        # 删除超过1小时的临时文件
                        if current_time - os.path.getctime(file_path) > 3600:
                            os.remove(file_path)
                            print(f"🗑️ 清理旧临时文件: {filename}")
                    except Exception as e:
                        print(f"⚠️ 清理文件失败 {filename}: {e}")
        except Exception as e:
            print(f"⚠️ 清理旧文件过程出错: {e}")

    def synthesize(self, text, output_file=None):
        """
        合成语音到文件
        text: 要合成的文本
        output_file: 输出文件路径
        """
        if output_file is None:
            output_file = f'output_{int(time.time())}.mp3'
            
        print(f"合成语音: {text[:30]}...")
        
        # 确保始终尝试使用Edge TTS
        if EDGE_TTS_AVAILABLE:
            # 增强的重试机制
            for attempt in range(self.max_retries):
                try:
                    if attempt > 0:
                        # 指数退避重试延迟
                        retry_delay = self.retry_delay * (2 ** (attempt - 1))
                        print(f"🔄 Edge TTS合成重试 #{attempt+1}/{self.max_retries}，等待 {retry_delay}s...")
                        time.sleep(retry_delay)

                        # 重新检查网络连接
                        self._check_network()

                    loop, new_loop = self._get_event_loop()

                    # 设置异步任务超时
                    try:
                        success = asyncio.wait_for(
                            self._edge_synthesize(text, output_file),
                            timeout=30.0  # 30秒超时
                        )
                        success = loop.run_until_complete(success)
                    except asyncio.TimeoutError:
                        print("⏰ Edge TTS合成超时")
                        success = False

                    if new_loop and not loop.is_closed():
                        loop.close()

                    if success:
                        print(f"✅ Edge TTS语音合成成功，保存至: {output_file}")
                        return output_file
                    else:
                        print(f"❌ Edge TTS语音合成失败，准备重试...")
                        self._clean_file(output_file)

                except ssl.SSLError as ssl_error:
                    print(f"🔒 SSL错误 (尝试 {attempt+1}/{self.max_retries}): {ssl_error}")
                    self._clean_file(output_file)
                    if attempt == self.max_retries - 1:
                        print("🔒 SSL连接持续失败，可能是网络环境限制")
                except ConnectionError as conn_error:
                    print(f"🌐 连接错误 (尝试 {attempt+1}/{self.max_retries}): {conn_error}")
                    self._clean_file(output_file)
                    self._check_network()
                except Exception as e:
                    error_msg = str(e)
                    print(f"❌ Edge TTS合成错误 (尝试 {attempt+1}/{self.max_retries}): {e}")
                    self._clean_file(output_file)

                    # 特殊处理不同类型的错误
                    if "ssl" in error_msg.lower() or "certificate" in error_msg.lower():
                        print("🔒 检测到SSL相关错误，可能需要检查网络环境")
                    elif "connection" in error_msg.lower() or "reset" in error_msg.lower():
                        print("🌐 检测到连接重置，重新检查网络状态")
                        self._check_network()
                    elif "timeout" in error_msg.lower():
                        print("⏰ 检测到超时错误，网络可能较慢")

            print(f"❌ Edge TTS合成多次尝试均失败 ({self.max_retries}次)，切换到系统TTS...")
        else:
            print("⚠️ Edge TTS不可用，直接使用系统TTS")
        
        # 如果Edge TTS不可用或多次尝试失败，则使用系统TTS备用
        if self.platform == 'darwin':  # macOS
            try:
                temp_output = f'output_{int(time.time())}.wav'
                cmd = f"say -v {self.system_voice} -o \"{temp_output}\" --data-format=LEI16@16000 \"{text}\""
                ret_code = os.system(cmd)
                
                success = ret_code == 0 and os.path.exists(temp_output) and os.path.getsize(temp_output) > 0
                
                if success:
                    print(f"系统TTS语音合成成功，保存至: {temp_output}")
                    return temp_output
                else:
                    print(f"系统TTS语音合成失败，返回码: {ret_code}")
                    self._clean_file(temp_output)
            except Exception as e:
                print(f"系统TTS合成错误: {e}")
                traceback.print_exc()
        elif self.platform == 'windows':  # Windows
            try:
                temp_output = f'output_{int(time.time())}.wav'
                
                # 首选方式: 使用pyttsx3
                if PYTTSX3_AVAILABLE and self.windows_engine:
                    try:
                        print(f"使用pyttsx3合成语音到: {temp_output}")
                        self.windows_engine.save_to_file(text, temp_output)
                        self.windows_engine.runAndWait()
                        if os.path.exists(temp_output) and os.path.getsize(temp_output) > 0:
                            print(f"Windows TTS语音合成成功，保存至: {temp_output}")
                            return temp_output
                        else:
                            print("Windows TTS合成文件无效，尝试备用方法")
                    except Exception as e:
                        print(f"pyttsx3合成出错: {e}")
                
                # 备用方式: 使用PowerShell命令
                try:
                    print("尝试使用PowerShell合成语音...")
                    # PowerShell命令使用System.Speech合成语音
                    ps_cmd = f'powershell -Command "Add-Type -AssemblyName System.Speech; ' \
                             f'$speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; ' \
                             f'$speak.SetOutputToWaveFile(\'{temp_output}\'); ' \
                             f'$speak.Speak(\'{text}\'); ' \
                             f'$speak.Dispose()"'
                    ret_code = os.system(ps_cmd)
                    
                    if ret_code == 0 and os.path.exists(temp_output) and os.path.getsize(temp_output) > 0:
                        print(f"PowerShell TTS语音合成成功，保存至: {temp_output}")
                        return temp_output
                    else:
                        print(f"PowerShell TTS语音合成失败")
                        self._clean_file(temp_output)
                except Exception as e:
                    print(f"PowerShell TTS合成错误: {e}")
                    traceback.print_exc()
                
                # 最后的备选: 直接合成并播放，不保存文件
                print("所有TTS合成方法失败，将直接播放语音而不保存文件")
                if PYTTSX3_AVAILABLE:
                    try:
                        # 直接使用pyttsx3播放
                        temp_engine = pyttsx3.init()
                        temp_engine.say(text)
                        temp_engine.runAndWait()
                        print("直接通过pyttsx3播放语音成功")
                        return None  # 返回None表示没有文件，但语音已播放
                    except Exception as e:
                        print(f"pyttsx3直接播放失败: {e}")
                        
            except Exception as e:
                print(f"Windows TTS处理错误: {e}")
                traceback.print_exc()
        else:
            print(f"不支持的平台: {self.platform}")
            
        return None

    def play(self, file_path):
        """播放音频文件"""
        if not file_path:
            print("没有有效的音频文件路径")
            return False
            
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
            
        if os.path.getsize(file_path) <= 0:
            print(f"音频文件为空: {file_path}")
            return False
            
        print(f"播放音频文件: {file_path}")
        
        # 获取文件扩展名
        ext = os.path.splitext(file_path)[1].lower()
        
        # 使用pygame播放
        if self.pygame_available and ext in ['.mp3', '.wav', '.ogg']:
            try:
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()
                
                # 等待播放完毕
                while pygame.mixer.music.get_busy():
                    pygame.time.Clock().tick(10)
                return True
            except Exception as e:
                print(f"pygame播放失败: {e}，尝试使用系统播放器")
        
        # 使用系统播放器
        try:
            if self.platform == 'darwin':
                ret = os.system(f"afplay \"{file_path}\"")
                return ret == 0
            elif self.platform == 'windows':
                # Windows下使用PowerShell播放
                try:
                    # 使用PowerShell和WindowsMediaPlayer播放
                    ps_cmd = f'powershell -Command "(New-Object Media.SoundPlayer \'{file_path}\').PlaySync();"'
                    ret = os.system(ps_cmd)
                    if ret == 0:
                        print("PowerShell成功播放音频")
                        return True
                    else:
                        print(f"PowerShell播放失败: {ret}")
                        
                        # 备选方式: 使用命令行播放
                        cmd_cmd = f'start /wait "" "{file_path}"'
                        ret = os.system(cmd_cmd)
                        if ret == 0:
                            print("CMD成功播放音频")
                            time.sleep(2)  # 简单等待一下，因为start命令可能立即返回
                            return True
                        else:
                            print("CMD播放也失败了")
                            return False
                except Exception as e:
                    print(f"Windows播放错误: {e}")
                    return False
            else:
                print(f"不支持的平台: {self.platform}")
        except Exception as e:
            print(f"系统播放器错误: {e}")
            
        return False

    def speak(self, text):
        """合成并播放语音"""
        if not text or text.strip() == "":
            print("文本为空，不进行语音合成")
            return False
            
        print(f"执行语音合成并播放: {text[:30]}...")
        output_file = self.synthesize(text)
        
        if output_file:
            success = self.play(output_file)
            self._clean_file(output_file)
            return success
        # Windows下，如果没有输出文件但使用了直接播放模式
        elif self.platform == 'windows' and not output_file:
            # 在这种情况下，我们已经在synthesize方法中直接播放了语音
            return True
        else:
            print("语音合成失败，无法播放")
            return False
        
    def set_voice(self, voice_type, voice_name=None):
        """
        设置语音类型和声音
        voice_type: "edge" 或 "system"
        voice_name: 对应类型的声音名称
        """
        # 强制使用Edge TTS和小小声音
        if voice_type == "edge" and voice_name and voice_name != "zh-CN-XiaoxiaoNeural":
            print(f"提示: 已设定强制使用小小声音，忽略设置 {voice_name}")
        
        # 记录系统声音以备不时之需
        if voice_type == "system" and voice_name:
            self.system_voice = voice_name
            print(f"已设置系统备用TTS声音: {voice_name}，但将优先使用Edge TTS小小声音")
            
            # 对于Windows，如果是pyttsx3引擎，则设置声音
            if self.platform == 'windows' and PYTTSX3_AVAILABLE and self.windows_engine:
                try:
                    voices = self.windows_engine.getProperty('voices')
                    # 尝试匹配声音名称或ID
                    for voice in voices:
                        if voice_name.lower() in voice.name.lower() or voice_name.lower() in voice.id.lower():
                            self.windows_engine.setProperty('voice', voice.id)
                            print(f"已设置Windows TTS声音为: {voice.name}")
                            break
                except Exception as e:
                    print(f"设置Windows TTS声音时出错: {e}")
        
        # 强制保持Edge TTS
        return True
        
    def list_voices(self):
        """列出可用的声音选项"""
        print("\n===== 可用的声音选项 =====")
        
        if EDGE_TTS_AVAILABLE:
            print("\nEdge TTS中文声音选项:")
            print("zh-CN-XiaoxiaoNeural - 小小(女声，明亮活泼) [当前使用]")
            print("使用方法: tts.set_voice(\"edge\", \"zh-CN-XiaohanNeural\")")
            
        print("\n系统TTS备用声音选项:")
        if self.platform == 'darwin':
            os.system("say -v '?' | grep 'zh'")
            print("使用方法: tts.set_voice(\"system\", \"Meijia\")")
        elif self.platform == 'windows':
            if PYTTSX3_AVAILABLE and self.windows_engine:
                try:
                    print("Windows TTS可用声音:")
                    voices = self.windows_engine.getProperty('voices')
                    for i, voice in enumerate(voices):
                        lang = voice.name.split()[-1].strip('()') if '(' in voice.name else "Unknown"
                        print(f"{i+1}. {voice.name} - {lang}")
                    print("使用方法: tts.set_voice(\"system\", \"声音名称\")")
                except Exception as e:
                    print(f"获取Windows TTS声音列表失败: {e}")
            else:
                print("pyttsx3未安装或初始化失败，无法获取Windows TTS声音列表")
                print("请安装pyttsx3: pip install pyttsx3")
        
    async def _list_edge_voices(self):
        """获取Edge TTS支持的所有声音"""
        if EDGE_TTS_AVAILABLE:
            try:
                voices = await edge_tts.list_voices()
                for voice in voices:
                    if "zh-CN" in voice["ShortName"]:
                        extra = " [当前使用]" if voice["ShortName"] == "zh-CN-XiaoxiaoNeural" else ""
                        print(f"{voice['ShortName']} - {voice['FriendlyName']}{extra}")
            except Exception as e:
                print(f"获取Edge TTS声音列表失败: {e}")

def install_edge_tts():
    """安装Edge TTS库"""
    print("正在安装Edge TTS...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "edge-tts"])
        print("Edge TTS安装成功，请重启程序以启用更好的语音效果")
        return True
    except Exception as e:
        print(f"安装失败: {e}")
        print("请手动运行: pip install edge-tts")
        return False

if __name__ == "__main__":
    # 测试不同的TTS效果
    test_text = "你好，我是你的学习小助手。我将监督你的学习，并给予鼓励。希望你能够专心学习，取得好成绩。"
    
    # 检查是否需要安装Edge TTS
    if not EDGE_TTS_AVAILABLE:
        print("检测到Edge TTS未安装")
        response = input("是否安装Edge TTS以获得更高质量的语音? (y/n): ")
        if response.lower() == 'y':
            install_edge_tts()
            print("请重新运行此脚本测试语音效果")
            sys.exit(0)
    
    # 创建TTS对象
    tts = EnhancedTTS(voice_type="edge")
    
    print("\n===== 测试语音效果 =====")
    
    print("\n测试Edge TTS小小声音:")
    tts.speak(test_text)
    
    # 显示所有可用声音
    tts.list_voices() 