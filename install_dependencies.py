#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
睿课云眸系统依赖安装脚本
=======================
自动安装运行睿课云眸系统所需的所有依赖库
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

class DependencyInstaller:
    """依赖安装器"""
    
    def __init__(self):
        self.system = platform.system()
        self.python_exec = sys.executable
        self.failed_packages = []
        self.success_packages = []
        
    def print_header(self):
        """打印安装开始信息"""
        print("=" * 60)
        print("🚀 睿课云眸系统依赖安装")
        print("=" * 60)
        print(f"Python版本: {sys.version}")
        print(f"操作系统: {self.system}")
        print(f"Python路径: {self.python_exec}")
        print("=" * 60)
        print()
        
    def upgrade_pip(self):
        """升级pip到最新版本"""
        print("🔧 升级pip...")
        try:
            subprocess.check_call([
                self.python_exec, "-m", "pip", "install", "--upgrade", "pip"
            ])
            print("✅ pip升级成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"⚠️ pip升级失败: {e}")
            return False
    
    def install_package(self, package_name, package_spec=None):
        """安装单个包"""
        spec = package_spec or package_name
        print(f"📦 安装 {package_name}...")
        
        try:
            # 尝试安装包
            result = subprocess.run([
                self.python_exec, "-m", "pip", "install", spec
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {package_name} 安装成功")
                self.success_packages.append(package_name)
                return True
            else:
                print(f"❌ {package_name} 安装失败")
                print(f"错误信息: {result.stderr}")
                self.failed_packages.append(package_name)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"❌ {package_name} 安装超时")
            self.failed_packages.append(package_name)
            return False
        except Exception as e:
            print(f"❌ {package_name} 安装失败: {e}")
            self.failed_packages.append(package_name)
            return False
    
    def install_core_packages(self):
        """安装核心依赖包"""
        print("🎯 安装核心依赖包...")
        
        core_packages = [
            ("CustomTkinter", "customtkinter>=5.2.0"),
            ("Pillow", "Pillow>=10.0.0"),
            ("Requests", "requests>=2.31.0"),
            ("OpenCV", "opencv-python>=4.8.0"),
            ("NumPy", "numpy>=1.24.0"),
            ("Pygame", "pygame>=2.5.0"),
            ("OSS2", "oss2>=2.17.0"),
            ("OpenAI", "openai>=1.0.0"),
            ("DashScope", "dashscope>=1.0.0"),
            ("PSUtil", "psutil>=5.9.0")
        ]
        
        for name, spec in core_packages:
            self.install_package(name, spec)
            
    def install_audio_packages(self):
        """安装音频相关包"""
        print("🔊 安装音频依赖包...")
        
        audio_packages = [
            ("Edge-TTS", "edge-tts>=6.1.0"),
            ("PyAudio", "pyaudio>=0.2.11"),
            ("Pydub", "pydub>=0.25.0")
        ]
        
        for name, spec in audio_packages:
            if name == "PyAudio" and self.system == "Darwin":  # macOS
                print("🍎 macOS系统检测到，尝试特殊安装PyAudio...")
                # 在macOS上，可能需要先安装portaudio
                subprocess.run(["brew", "install", "portaudio"], capture_output=True)
            
            self.install_package(name, spec)
    
    def install_optional_packages(self):
        """安装可选依赖包"""
        print("🔧 安装可选依赖包...")
        
        optional_packages = [
            ("HTTPX", "httpx>=0.24.0"),
            ("JSON5", "json5>=0.9.0"),
            ("DateUtil", "python-dateutil>=2.8.0"),
            ("Threading-Timeout", "threading-timeout>=0.2.0")
        ]
        
        for name, spec in optional_packages:
            self.install_package(name, spec)
    
    def verify_installations(self):
        """验证安装结果"""
        print("\n🔍 验证安装结果...")
        
        verification_imports = {
            "customtkinter": "CustomTkinter",
            "cv2": "OpenCV",
            "PIL": "Pillow", 
            "requests": "Requests",
            "numpy": "NumPy",
            "pygame": "Pygame",
            "oss2": "OSS2",
            "openai": "OpenAI",
            "edge_tts": "Edge-TTS",
            "psutil": "PSUtil"
        }
        
        verified = []
        failed = []
        
        for module, name in verification_imports.items():
            try:
                __import__(module)
                print(f"✅ {name} 验证成功")
                verified.append(name)
            except ImportError:
                print(f"❌ {name} 验证失败")
                failed.append(name)
        
        return verified, failed
    
    def create_logs_directory(self):
        """创建日志目录"""
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        print(f"📁 创建日志目录: {logs_dir.absolute()}")
        
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        print(f"📁 创建临时目录: {temp_dir.absolute()}")
    
    def print_summary(self):
        """打印安装总结"""
        print("\n" + "=" * 60)
        print("📊 安装总结")
        print("=" * 60)
        
        if self.success_packages:
            print(f"✅ 成功安装 ({len(self.success_packages)} 个):")
            for pkg in self.success_packages:
                print(f"   • {pkg}")
        
        if self.failed_packages:
            print(f"\n❌ 安装失败 ({len(self.failed_packages)} 个):")
            for pkg in self.failed_packages:
                print(f"   • {pkg}")
            print("\n💡 对于失败的包，您可以尝试:")
            print("   1. 手动安装: pip install <包名>")
            print("   2. 检查网络连接")
            print("   3. 更新pip: python -m pip install --upgrade pip")
        
        # 验证安装
        verified, failed_verify = self.verify_installations()
        
        if failed_verify:
            print(f"\n⚠️ 验证失败的包 ({len(failed_verify)} 个):")
            for pkg in failed_verify:
                print(f"   • {pkg}")
        
        if not self.failed_packages and not failed_verify:
            print("\n🎉 所有依赖安装完成！系统已准备就绪。")
            print("✨ 您现在可以运行: python ruike_yunmou.py")
        else:
            print("\n⚠️ 部分依赖安装失败，程序可能无法正常运行。")
            print("请根据上述提示解决依赖问题。")
    
    def run_installation(self):
        """运行完整安装流程"""
        self.print_header()
        
        # 升级pip
        self.upgrade_pip()
        print()
        
        # 创建目录
        self.create_logs_directory()
        print()
        
        # 安装依赖
        self.install_core_packages()
        print()
        self.install_audio_packages()
        print()
        self.install_optional_packages()
        
        # 打印总结
        self.print_summary()

def main():
    """主函数"""
    installer = DependencyInstaller()
    installer.run_installation()

if __name__ == "__main__":
    main() 