{"weekly_schedule": [{"id": "plan_0", "day": "周一", "day_index": 0, "start_time": "08:00", "end_time": "10:00", "duration": 120, "subject": "数学", "topic": "代数基础", "goals": "掌握基本概念", "priority": "高", "difficulty": "中等", "status": "已完成", "notes": "", "created_at": "2025-07-03T18:35:12.894137", "updated_at": "2025-07-03T18:35:12.894144"}, {"id": "plan_1", "day": "周二", "day_index": 1, "start_time": "08:00", "end_time": "10:00", "duration": 120, "subject": "语文", "topic": "阅读理解", "goals": "提高解题速度", "priority": "中", "difficulty": "简单", "status": "已完成", "notes": "", "created_at": "2025-07-03T18:35:12.894149", "updated_at": "2025-07-03T18:35:12.894151"}, {"id": "plan_2", "day": "周三", "day_index": 2, "start_time": "08:00", "end_time": "10:00", "duration": 120, "subject": "英语", "topic": "语法练习", "goals": "增强理解能力", "priority": "高", "difficulty": "困难", "status": "已完成", "notes": "", "created_at": "2025-07-03T18:35:12.894154", "updated_at": "2025-07-03T18:35:12.894156"}, {"id": "plan_3", "day": "周四", "day_index": 3, "start_time": "08:00", "end_time": "10:00", "duration": 120, "subject": "物理", "topic": "力学原理", "goals": "巩固知识点", "priority": "中", "difficulty": "中等", "status": "进行中", "notes": "", "created_at": "2025-07-03T18:35:12.894158", "updated_at": "2025-07-03T18:35:12.894160"}, {"id": "plan_4", "day": "周五", "day_index": 4, "start_time": "08:00", "end_time": "10:00", "duration": 120, "subject": "化学", "topic": "化学反应", "goals": "拓展思维", "priority": "低", "difficulty": "简单", "status": "计划中", "notes": "", "created_at": "2025-07-03T18:35:12.894162", "updated_at": "2025-07-03T18:35:12.894164"}, {"id": "plan_5", "day": "周六", "day_index": 5, "start_time": "08:00", "end_time": "10:00", "duration": 120, "subject": "生物", "topic": "细胞结构", "goals": "实践应用", "priority": "中", "difficulty": "中等", "status": "计划中", "notes": "", "created_at": "2025-07-03T18:35:12.894166", "updated_at": "2025-07-03T18:35:12.894168"}, {"id": "plan_6", "day": "周日", "day_index": 6, "start_time": "08:00", "end_time": "10:00", "duration": 120, "subject": "历史", "topic": "古代史", "goals": "综合复习", "priority": "高", "difficulty": "困难", "status": "计划中", "notes": "", "created_at": "2025-07-03T18:35:12.894170", "updated_at": "2025-07-03T18:35:12.894171"}], "templates": {"daily_routine": {"name": "日常学习模板", "sessions": [{"time": "08:00-10:00", "subject": "数学", "type": "核心学习"}, {"time": "10:30-12:00", "subject": "语文", "type": "阅读理解"}, {"time": "14:00-16:00", "subject": "英语", "type": "听说练习"}, {"time": "19:00-21:00", "subject": "复习", "type": "知识巩固"}]}, "exam_preparation": {"name": "考试准备模板", "sessions": [{"time": "07:00-09:00", "subject": "重点科目", "type": "强化训练"}, {"time": "09:30-11:30", "subject": "薄弱环节", "type": "专项突破"}, {"time": "14:00-17:00", "subject": "模拟测试", "type": "实战演练"}, {"time": "19:00-21:00", "subject": "错题整理", "type": "查漏补缺"}]}}, "settings": {"auto_save": true, "conflict_detection": true, "smart_suggestions": true, "backup_enabled": true}, "goals": {"daily_time": "21", "focus_goal": "100", "weekly_days": "21", "weekly_time": "90", "updated_at": "2025-07-15T13:00:15.245260"}}