#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
睿课云眸 AI学习监督系统
====================

这是一个基于AI视觉识别和语音交互的学习监督系统，能够：
- 实时监控学习状态
- 智能行为分析
- 语音提醒和问答
- 学习记录统计

作者: 睿课云眸团队
版本: v3.0
更新时间: 2025-01-04
"""

import sys
import os
import threading
import time
from datetime import datetime

def main():
    """主程序入口"""
    try:
        print("=" * 60)
        print("🎯 睿课云眸 AI学习监督系统 v3.0")
        print("=" * 60)
        print("⚡ 正在启动系统...")
        
        # 导入主程序模块
        from ruike_yunmou import MultimediaAssistantApp, main as app_main
        
        # 启动应用
        app_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖模块都已正确安装")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 