#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR优化配置模块
==================

用于优化PaddleOCR的初始化和运行性能，减少警告信息，提升用户体验。

作者: 睿课云眸团队
版本: v1.0
更新时间: 2025-01-13
"""

import os
import sys
import warnings
from pathlib import Path

def setup_paddleocr_environment():
    """设置PaddleOCR优化环境变量"""
    
    # 禁用PaddlePaddle的信号处理器，避免与主程序冲突
    os.environ['PADDLE_DISABLE_SIGNAL_HANDLER'] = '1'
    
    # 设置内存分配策略，提升性能
    os.environ['FLAGS_allocator_strategy'] = 'naive_best_fit'
    
    # 限制OpenMP线程数，避免资源竞争
    os.environ['OMP_NUM_THREADS'] = '1'
    
    # 禁用CUDA警告（如果没有GPU）
    os.environ['CUDA_VISIBLE_DEVICES'] = ''
    
    # 设置日志级别，减少冗余输出
    os.environ['GLOG_minloglevel'] = '2'
    
    # 禁用MKL-DNN警告
    os.environ['MKLDNN_VERBOSE'] = '0'
    
    # 设置PaddlePaddle的工作目录
    paddle_home = Path.home() / '.paddleocr'
    paddle_home.mkdir(exist_ok=True)
    os.environ['PADDLE_HOME'] = str(paddle_home)
    
    print("🔧 PaddleOCR环境优化配置已应用")

def suppress_paddleocr_warnings():
    """抑制PaddleOCR相关的警告信息"""
    
    # 抑制特定的警告类型
    warnings.filterwarnings('ignore', category=UserWarning, module='paddle')
    warnings.filterwarnings('ignore', category=FutureWarning, module='paddle')
    warnings.filterwarnings('ignore', category=DeprecationWarning, module='paddle')
    
    # 抑制ccache相关警告
    warnings.filterwarnings('ignore', message='.*ccache.*')
    
    # 抑制MKL-DNN相关警告
    warnings.filterwarnings('ignore', message='.*MKL-DNN.*')
    
    print("🔇 PaddleOCR警告信息已抑制")

def optimize_paddleocr_performance():
    """优化PaddleOCR性能设置"""
    
    try:
        import paddle
        
        # 设置设备为CPU（避免GPU相关警告）
        paddle.set_device('cpu')
        
        # 禁用动态图模式的某些优化，提升稳定性
        paddle.disable_static()
        
        print("⚡ PaddleOCR性能优化已应用")
        
    except ImportError:
        print("⚠️ PaddlePaddle未安装，跳过性能优化")
    except Exception as e:
        print(f"⚠️ PaddleOCR性能优化失败: {e}")

def check_paddleocr_models():
    """检查PaddleOCR模型文件状态"""
    
    paddle_home = Path.home() / '.paddleocr'
    models_dir = paddle_home / 'whl'
    
    if models_dir.exists():
        model_files = list(models_dir.rglob('*.pdmodel'))
        if model_files:
            print(f"✅ 检测到 {len(model_files)} 个PaddleOCR模型文件")
            return True
        else:
            print("📥 PaddleOCR模型文件未找到，首次使用将自动下载")
            return False
    else:
        print("📥 PaddleOCR模型目录不存在，首次使用将自动创建并下载模型")
        return False

def install_paddleocr_with_optimization():
    """安装PaddleOCR并应用优化配置"""
    
    import subprocess
    import sys
    
    print("🔄 开始安装PaddleOCR...")
    
    try:
        # 设置优化环境
        setup_paddleocr_environment()
        
        # 安装PaddleOCR
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'paddleocr',
            '--quiet', '--no-warn-script-location'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PaddleOCR安装成功")
            
            # 预热PaddleOCR，触发模型下载
            print("🔥 正在预热PaddleOCR并下载模型...")
            try:
                suppress_paddleocr_warnings()
                from paddleocr import PaddleOCR
                
                # 创建一个临时的OCR实例来触发模型下载
                ocr = PaddleOCR(lang='ch', show_log=False)
                print("✅ PaddleOCR模型下载完成")
                
                # 清理实例
                del ocr
                
            except Exception as e:
                print(f"⚠️ PaddleOCR预热失败: {e}")
                print("💡 模型将在首次使用时自动下载")
            
            return True
        else:
            print(f"❌ PaddleOCR安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ PaddleOCR安装过程出错: {e}")
        return False

def get_optimized_paddleocr_config():
    """获取优化的PaddleOCR配置参数"""
    
    config = {
        'lang': 'ch',  # 中文识别
        'show_log': False,  # 不显示日志
        'use_gpu': False,  # 强制使用CPU
        'enable_mkldnn': False,  # 禁用MKL-DNN
        'cpu_threads': 1,  # 限制CPU线程数
    }
    
    return config

def main():
    """主函数 - 用于测试配置"""
    
    print("🧪 测试PaddleOCR优化配置...")
    
    # 应用环境优化
    setup_paddleocr_environment()
    suppress_paddleocr_warnings()
    
    # 检查模型状态
    check_paddleocr_models()
    
    # 测试PaddleOCR导入
    try:
        optimize_paddleocr_performance()
        from paddleocr import PaddleOCR
        
        config = get_optimized_paddleocr_config()
        ocr = PaddleOCR(**config)
        
        print("✅ PaddleOCR优化配置测试成功")
        
    except ImportError:
        print("⚠️ PaddleOCR未安装")
    except Exception as e:
        print(f"❌ PaddleOCR测试失败: {e}")

if __name__ == "__main__":
    main()
