#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题目拍照识别与分步讲解模块
==========================

功能：
- OCR文字识别
- 题目内容解析
- 分步讲解生成
- 与现有问答系统集成

作者: 睿课云眸团队
版本: v1.0
更新时间: 2025-01-07
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import re
import time
import threading
from typing import Optional, Tuple, Dict, List
import logging

# OCR库导入 - 优先使用PaddleOCR，因为下载更稳定
OCR_AVAILABLE = False
OCR_ENGINE = None

# 导入PaddleOCR优化配置
try:
    from paddleocr_config import (
        setup_paddleocr_environment,
        suppress_paddleocr_warnings,
        get_optimized_paddleocr_config
    )
    PADDLEOCR_CONFIG_AVAILABLE = True
except ImportError:
    PADDLEOCR_CONFIG_AVAILABLE = False

# 优先尝试PaddleOCR
try:
    # 应用PaddleOCR优化配置
    if PADDLEOCR_CONFIG_AVAILABLE:
        setup_paddleocr_environment()
        suppress_paddleocr_warnings()

    import paddleocr
    OCR_ENGINE = 'paddleocr'
    OCR_AVAILABLE = True
    print("✅ PaddleOCR导入成功（推荐）")
except ImportError:
    # 备选EasyOCR
    try:
        import easyocr
        OCR_ENGINE = 'easyocr'
        OCR_AVAILABLE = True
        print("✅ EasyOCR导入成功")
    except ImportError:
        print("⚠️ OCR库未安装，题目识别功能将不可用")
        print("推荐安装: pip install paddleocr")
        print("或者安装: pip install easyocr")

class QuestionOCR:
    """题目OCR识别器"""

    # 类级别的OCR引擎实例，确保全局只初始化一次
    _ocr_reader_instance = None
    _is_initialized = False

    def __init__(self):
        """初始化OCR识别器"""
        self.supported_languages = ['ch_sim', 'en']  # 中文简体和英文

        # 使用类级别的OCR引擎实例
        if not QuestionOCR._is_initialized:
            self._initialize_ocr()

        self.ocr_reader = QuestionOCR._ocr_reader_instance
        self.is_initialized = QuestionOCR._is_initialized
    
    def _initialize_ocr(self):
        """初始化OCR引擎 - 类级别单例模式"""
        if not OCR_AVAILABLE:
            print("⚠️ OCR库不可用，无法初始化题目识别功能")
            return

        if QuestionOCR._is_initialized:
            print("✅ OCR引擎已初始化，跳过重复初始化")
            return

        try:
            print("🔧 正在初始化OCR引擎（全局单例）...")

            if OCR_ENGINE == 'paddleocr':
                print("🔧 正在初始化PaddleOCR...")
                print("📥 首次使用可能需要下载模型文件...")

                from paddleocr import PaddleOCR

                # 使用优化配置初始化PaddleOCR
                try:
                    if PADDLEOCR_CONFIG_AVAILABLE:
                        # 使用优化配置
                        config = get_optimized_paddleocr_config()
                        print("🔧 使用优化配置初始化PaddleOCR...")
                        QuestionOCR._ocr_reader_instance = PaddleOCR(**config)
                        print("✅ PaddleOCR初始化成功（优化配置）")
                    else:
                        # 使用基础配置
                        print("🔧 使用基础配置初始化PaddleOCR...")
                        QuestionOCR._ocr_reader_instance = PaddleOCR(lang='ch', show_log=False)
                        print("✅ PaddleOCR初始化成功（基础配置）")
                except Exception as e:
                    print(f"⚠️ 优化配置初始化失败: {e}")
                    # 尝试最简配置作为备选
                    try:
                        print("🔧 尝试使用最简配置初始化PaddleOCR...")
                        QuestionOCR._ocr_reader_instance = PaddleOCR()
                        print("✅ PaddleOCR初始化成功（最简配置）")
                    except Exception as e2:
                        print(f"❌ PaddleOCR初始化完全失败: {e2}")
                        raise e2
                print("✅ PaddleOCR初始化完成")

            elif OCR_ENGINE == 'easyocr':
                print("🔧 正在初始化EasyOCR...")
                print("📥 首次使用需要下载模型文件，请确保网络连接正常...")

                # 配置清华源镜像加速下载
                import os
                self._setup_china_mirror()

                try:
                    import easyocr
                    QuestionOCR._ocr_reader_instance = easyocr.Reader(
                        self.supported_languages,
                        gpu=False,
                        download_enabled=True,
                        verbose=False
                    )
                    print("✅ EasyOCR初始化成功")
                except Exception as download_error:
                    print(f"⚠️ EasyOCR模型下载失败: {download_error}")
                    print("🔄 尝试使用PaddleOCR作为备选方案...")
                    return self._try_paddleocr_fallback()

            QuestionOCR._is_initialized = True
            print("✅ OCR引擎全局初始化完成")

        except Exception as e:
            print(f"❌ OCR引擎初始化失败: {e}")
            print("🔄 尝试降级处理...")
            QuestionOCR._is_initialized = False

            # 尝试切换到另一个OCR引擎
            self._try_fallback_ocr()

    def _setup_china_mirror(self):
        """配置中国镜像源以加速模型下载"""
        import os

        # 设置EasyOCR模型下载路径
        easyocr_path = os.path.expanduser('~/.EasyOCR')
        os.environ['EASYOCR_MODULE_PATH'] = easyocr_path

        # 创建目录
        os.makedirs(easyocr_path, exist_ok=True)

        # 设置下载镜像
        try:
            # 设置Hugging Face镜像源
            os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
            print("✅ 已配置Hugging Face清华镜像源")

            # 设置GitHub镜像源（如果需要）
            os.environ['GITHUB_MIRROR'] = 'https://ghproxy.com/'

            # 设置pip镜像源环境变量
            os.environ['PIP_INDEX_URL'] = 'https://pypi.tuna.tsinghua.edu.cn/simple'

        except Exception as e:
            print(f"⚠️ 镜像源配置失败: {e}")

    def _try_paddleocr_fallback(self):
        """尝试使用PaddleOCR作为备选方案"""
        try:
            print("🔄 正在尝试PaddleOCR备选方案...")

            # 检查是否已安装PaddleOCR
            try:
                import paddleocr
                from paddleocr import PaddleOCR

                # 简化的PaddleOCR初始化
                try:
                    QuestionOCR._ocr_reader_instance = PaddleOCR(lang='ch')
                except Exception:
                    QuestionOCR._ocr_reader_instance = PaddleOCR()

                # 更新全局OCR引擎标识
                global OCR_ENGINE
                OCR_ENGINE = 'paddleocr'

                QuestionOCR._is_initialized = True
                print("✅ PaddleOCR备选方案初始化成功")
                return True

            except ImportError:
                print("❌ PaddleOCR未安装，请运行: pip install paddleocr")
                return False

        except Exception as e:
            print(f"❌ PaddleOCR备选方案失败: {e}")
            return False

    def _try_fallback_ocr(self):
        """尝试备选OCR方案"""
        return self._try_paddleocr_fallback()

    def preprocess_image(self, image: Image.Image, save_debug=True) -> Image.Image:
        """
        图像预处理，提高OCR识别准确率

        Args:
            image: 输入的PIL图像
            save_debug: 是否保存调试图像

        Returns:
            处理后的PIL图像
        """
        try:
            print(f"🔧 开始图像预处理，原始尺寸: {image.size}, 模式: {image.mode}")

            # 保存原始图像用于调试
            if save_debug:
                try:
                    image.save("debug_original.png")
                    print("💾 原始图像已保存为 debug_original.png")
                except Exception as save_error:
                    print(f"⚠️ 保存原始图像失败: {save_error}")

            # 转换为RGB模式
            if image.mode != 'RGB':
                print(f"🔄 转换图像模式从 {image.mode} 到 RGB")
                image = image.convert('RGB')

            # 调整图像大小 - 确保足够的分辨率，但不要过大
            width, height = image.size
            print(f"📏 原始尺寸: {width}x{height}")

            # 安全的目标尺寸：防止内存溢出，限制最大尺寸
            min_size = 600  # 降低最小尺寸要求
            max_size = 1200  # 降低最大尺寸限制，防止段错误
            max_pixels = 1000000  # 1M像素限制

            current_pixels = width * height
            print(f"📊 当前像素数: {current_pixels:,}")

            # 首先检查是否超过像素限制
            if current_pixels > max_pixels:
                scale_factor = (max_pixels / current_pixels) ** 0.5
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                print(f"⚠️ 像素数过多，缩放到: {new_width}x{new_height} (缩放因子: {scale_factor:.3f})")
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                width, height = new_width, new_height

            # 然后检查尺寸限制
            if min(width, height) < min_size and max(width, height) <= max_size:
                scale_factor = min_size / min(width, height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)

                # 确保缩放后不超过最大尺寸
                if max(new_width, new_height) > max_size:
                    scale_factor = max_size / max(new_width, new_height)
                    new_width = int(new_width * scale_factor)
                    new_height = int(new_height * scale_factor)

                print(f"📈 放大图像到: {new_width}x{new_height} (缩放因子: {scale_factor:.3f})")
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            elif max(width, height) > max_size:
                scale_factor = max_size / max(width, height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                print(f"📉 缩小图像到: {new_width}x{new_height} (缩放因子: {scale_factor:.3f})")
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            else:
                print("✅ 图像尺寸合适，无需调整")

            # 保存调整尺寸后的图像
            if save_debug:
                try:
                    image.save("debug_resized.png")
                    print("💾 调整尺寸后图像已保存为 debug_resized.png")
                except Exception as save_error:
                    print(f"⚠️ 保存调整尺寸图像失败: {save_error}")

            # 增强对比度 - 更保守的增强
            print("🎨 增强对比度...")
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.3)  # 稍微增加对比度

            # 增强锐度 - 更保守的增强
            print("🔍 增强锐度...")
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)  # 稍微增加锐度

            # 可选：转换为灰度图像进行OCR（有时效果更好）
            # gray_image = image.convert('L')
            # 然后再转回RGB: image = gray_image.convert('RGB')

            # 保存最终预处理图像
            if save_debug:
                try:
                    image.save("debug_processed.png")
                    print("💾 最终预处理图像已保存为 debug_processed.png")
                except Exception as save_error:
                    print(f"⚠️ 保存预处理图像失败: {save_error}")

            print(f"✅ 图像预处理完成，最终尺寸: {image.size}")
            return image

        except Exception as e:
            print(f"❌ 图像预处理失败: {e}")
            import traceback
            traceback.print_exc()
            return image
    
    def extract_text(self, image: Image.Image) -> str:
        """
        从图像中提取文字

        Args:
            image: 输入的PIL图像

        Returns:
            识别出的文字内容
        """
        # 在方法最开始就初始化时间变量，防止作用域错误
        start_time = time.time()
        ocr_start_time = start_time
        preprocess_time = 0.0
        ocr_time = 0.0

        print("🔍 开始OCR文字提取...")

        if not QuestionOCR._is_initialized or not QuestionOCR._ocr_reader_instance:
            error_msg = "OCR引擎未初始化，无法识别文字"
            print(f"❌ {error_msg}")
            return error_msg

        # 输入验证
        if image is None:
            error_msg = "输入图像为空"
            print(f"❌ {error_msg}")
            return error_msg

        if not isinstance(image, Image.Image):
            error_msg = "输入必须是PIL图像对象"
            print(f"❌ {error_msg}")
            return error_msg

        # 检查图像尺寸
        print(f"📏 图像尺寸: {image.size}")
        if image.size[0] < 50 or image.size[1] < 50:
            error_msg = f"图像尺寸过小({image.size})，无法进行OCR识别"
            print(f"❌ {error_msg}")
            return error_msg

        try:
            # 预处理图像
            print("🔧 开始图像预处理...")
            preprocess_start = time.time()
            processed_image = self.preprocess_image(image, save_debug=True)
            preprocess_time = time.time() - preprocess_start
            print(f"✅ 图像预处理完成，耗时: {preprocess_time:.3f}s")

            # 转换为numpy数组
            print("🔄 转换图像格式...")
            img_array = np.array(processed_image)
            print(f"📊 图像数组形状: {img_array.shape}")
            print(f"📊 图像数据类型: {img_array.dtype}")
            print(f"📊 图像数值范围: [{img_array.min()}, {img_array.max()}]")

            # 保存numpy数组图像用于调试
            try:
                import cv2
                cv2.imwrite("debug_numpy_array.png", cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR))
                print("💾 numpy数组图像已保存为 debug_numpy_array.png")
            except Exception as save_error:
                print(f"⚠️ 保存numpy数组图像失败: {save_error}")
            
            # 根据不同的OCR引擎进行识别
            ocr_start_time = time.time()

            if OCR_ENGINE == 'easyocr':
                results = QuestionOCR._ocr_reader_instance.readtext(img_array)
                # 提取文字内容
                text_lines = []
                for (bbox, text, confidence) in results:
                    if confidence > 0.5:  # 置信度阈值
                        text_lines.append(text)
                extracted_text = '\n'.join(text_lines)

            elif OCR_ENGINE == 'paddleocr':
                # PaddleOCR标准API使用ocr方法 - 增强内存管理版本
                try:
                    print("🔍 开始PaddleOCR识别...")
                    print(f"📊 输入图像信息: 形状={img_array.shape}, 类型={img_array.dtype}")

                    # 检查图像大小，防止内存溢出
                    height, width = img_array.shape[:2]
                    total_pixels = height * width
                    print(f"📏 图像尺寸: {width}x{height}, 总像素: {total_pixels:,}")

                    # 如果图像过大，进行安全缩放
                    max_pixels = 1500000  # 1.5M像素限制
                    if total_pixels > max_pixels:
                        scale_factor = (max_pixels / total_pixels) ** 0.5
                        new_width = int(width * scale_factor)
                        new_height = int(height * scale_factor)
                        print(f"⚠️ 图像过大，缩放到: {new_width}x{new_height} (缩放因子: {scale_factor:.3f})")

                        # 使用PIL进行安全缩放
                        pil_image = Image.fromarray(img_array)
                        pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                        img_array = np.array(pil_image)
                        print(f"✅ 图像缩放完成，新尺寸: {img_array.shape}")

                    # 检查内存使用情况
                    import psutil
                    import gc

                    # 强制垃圾回收
                    gc.collect()

                    # 获取当前内存使用
                    process = psutil.Process()
                    memory_before = process.memory_info().rss / 1024 / 1024  # MB
                    print(f"💾 OCR前内存使用: {memory_before:.1f} MB")

                    # 直接使用标准OCR调用，添加异常保护
                    print("🔧 使用标准OCR配置进行识别...")

                    # macOS兼容性增强：使用多层保护的OCR调用
                    import platform
                    import signal
                    import os

                    # 检测macOS系统
                    is_macos = platform.system() == 'Darwin'
                    if is_macos:
                        print("🍎 检测到macOS系统，启用兼容性保护...")

                        # 设置环境变量，减少Objective-C运行时冲突
                        os.environ['OBJC_DISABLE_INITIALIZE_FORK_SAFETY'] = 'YES'
                        os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'

                    # 使用线程安全的OCR调用（带超时保护）
                    import concurrent.futures

                    results = None
                    max_retries = 2

                    for retry in range(max_retries + 1):
                        try:
                            print(f"🔄 OCR调用尝试 {retry + 1}/{max_retries + 1}")

                            # 定义OCR任务函数
                            def ocr_task():
                                return QuestionOCR._ocr_reader_instance.ocr(img_array)

                            # 使用线程池执行器实现超时
                            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                                future = executor.submit(ocr_task)
                                try:
                                    # 60秒超时
                                    results = future.result(timeout=60)
                                    print(f"✅ OCR调用完成 (尝试 {retry + 1})")
                                except concurrent.futures.TimeoutError:
                                    print(f"⏰ OCR调用超时 (尝试 {retry + 1})")
                                    future.cancel()
                                    if retry < max_retries:
                                        continue
                                    else:
                                        raise TimeoutError("OCR处理超时")

                            # 检查OCR后内存使用
                            memory_after = process.memory_info().rss / 1024 / 1024  # MB
                            memory_diff = memory_after - memory_before
                            print(f"💾 OCR后内存使用: {memory_after:.1f} MB (增加: {memory_diff:.1f} MB)")

                            # 如果成功获得结果，跳出重试循环
                            if results:
                                print(f"✅ OCR调用成功 (尝试 {retry + 1})")
                                break
                            else:
                                print(f"⚠️ OCR返回空结果 (尝试 {retry + 1})")

                        except Exception as ocr_error:
                            print(f"❌ PaddleOCR调用失败 (尝试 {retry + 1}): {ocr_error}")

                            # 如果是最后一次尝试，进行特殊处理
                            if retry == max_retries:
                                print("🔄 最后尝试：释放内存并缩小图像...")
                                gc.collect()

                                # 进一步缩小图像重试
                                if img_array.shape[0] > 400 or img_array.shape[1] > 600:
                                    pil_image = Image.fromarray(img_array)
                                    pil_image = pil_image.resize((600, 400), Image.Resampling.LANCZOS)
                                    img_array = np.array(pil_image)
                                    print(f"🔄 使用最小尺寸重试: {img_array.shape}")

                                    try:
                                        results = QuestionOCR._ocr_reader_instance.ocr(img_array)
                                        print("✅ 最小尺寸重试成功")
                                        break
                                    except Exception as final_error:
                                        print(f"❌ 最小尺寸重试也失败: {final_error}")
                                        raise final_error
                            else:
                                # 中间重试：稍微缩小图像
                                if img_array.shape[0] > 600 or img_array.shape[1] > 800:
                                    scale = 0.8  # 每次重试缩小20%
                                    new_height = int(img_array.shape[0] * scale)
                                    new_width = int(img_array.shape[1] * scale)
                                    pil_image = Image.fromarray(img_array)
                                    pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                                    img_array = np.array(pil_image)
                                    print(f"🔄 缩小图像重试: {img_array.shape}")

                                # 释放内存
                                gc.collect()

                                # 短暂等待
                                time.sleep(1)

                    if results and any(page for page in results if page):
                        print("✅ OCR识别成功获得结果")
                    else:
                        print("⚠️ OCR未获得有效结果")

                    text_lines = []

                    print(f"📊 PaddleOCR返回结果类型: {type(results)}")
                    print(f"📊 PaddleOCR返回结果长度: {len(results) if results else 0}")
                    print(f"📊 PaddleOCR原始结果: {results}")

                    # 保存OCR可视化结果
                    try:
                        from ocr_debug_tools import visualize_ocr_results, save_debug_image

                        # 可视化OCR检测结果
                        visualize_ocr_results(processed_image, results, "debug_ocr_visualization.png")

                        # 保存原始结果到文件
                        with open("debug_ocr_raw_results.txt", "w", encoding="utf-8") as f:
                            f.write(f"OCR原始结果:\n{results}\n\n")
                            f.write(f"结果类型: {type(results)}\n")
                            f.write(f"结果长度: {len(results) if results else 0}\n")
                        print("💾 OCR原始结果已保存到 debug_ocr_raw_results.txt")

                    except Exception as viz_error:
                        print(f"⚠️ 保存OCR可视化结果失败: {viz_error}")

                    # 解析PaddleOCR新版本输出格式
                    if results and isinstance(results, list):
                        print(f"🔍 开始解析 {len(results)} 页结果...")

                        for page_idx, page_result in enumerate(results):
                            print(f"📄 处理第 {page_idx + 1} 页，类型: {type(page_result)}")

                            # 检查是否是新版本的OCRResult对象
                            if hasattr(page_result, 'rec_texts') and hasattr(page_result, 'rec_scores'):
                                print("✅ 检测到新版本PaddleOCR结果格式")

                                rec_texts = page_result.rec_texts
                                rec_scores = page_result.rec_scores

                                print(f"📝 识别到 {len(rec_texts)} 个文本")

                                for i, text in enumerate(rec_texts):
                                    if i < len(rec_scores):
                                        confidence = rec_scores[i]
                                        print(f"✅ 识别文本: '{text}' (置信度: {confidence:.3f})")

                                        if confidence > 0.3:  # 置信度阈值
                                            text_lines.append(text)
                                            print(f"✅ 文本已添加到结果列表")
                                        else:
                                            print(f"⚠️ 置信度过低，跳过: {confidence:.3f}")
                                    else:
                                        # 没有置信度信息，直接添加
                                        text_lines.append(text)
                                        print(f"✅ 识别文本（无置信度）: '{text}'")

                            # 检查是否是字典格式
                            elif isinstance(page_result, dict):
                                print("✅ 检测到字典格式结果")

                                if 'rec_texts' in page_result and 'rec_scores' in page_result:
                                    rec_texts = page_result['rec_texts']
                                    rec_scores = page_result['rec_scores']

                                    print(f"📝 识别到 {len(rec_texts)} 个文本")

                                    for i, text in enumerate(rec_texts):
                                        if i < len(rec_scores):
                                            confidence = rec_scores[i]
                                            print(f"✅ 识别文本: '{text}' (置信度: {confidence:.3f})")

                                            if confidence > 0.3:
                                                text_lines.append(text)
                                                print(f"✅ 文本已添加到结果列表")
                                            else:
                                                print(f"⚠️ 置信度过低，跳过: {confidence:.3f}")
                                        else:
                                            text_lines.append(text)
                                            print(f"✅ 识别文本（无置信度）: '{text}'")
                                else:
                                    print(f"⚠️ 字典格式不包含预期的键: {page_result.keys()}")

                            # 检查是否是传统列表格式
                            elif isinstance(page_result, list):
                                print("✅ 检测到传统列表格式结果")
                                print(f"📝 第 {page_idx + 1} 页包含 {len(page_result)} 个文本框")

                                # 传统格式解析逻辑
                                for line_idx, line_result in enumerate(page_result):
                                    if line_result and isinstance(line_result, list) and len(line_result) >= 2:
                                        bbox = line_result[0]
                                        text_info = line_result[1]

                                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                            text = text_info[0]
                                            confidence = text_info[1]

                                            print(f"✅ 识别文本: '{text}' (置信度: {confidence:.3f})")

                                            if confidence > 0.3:
                                                text_lines.append(text)
                                                print(f"✅ 文本已添加到结果列表")
                                        elif isinstance(text_info, str):
                                            text_lines.append(text_info)
                                            print(f"✅ 识别文本（字符串）: '{text_info}'")
                            else:
                                print(f"⚠️ 未知的结果格式: {type(page_result)}")
                                # 尝试直接访问属性
                                try:
                                    if hasattr(page_result, '__dict__'):
                                        print(f"📊 对象属性: {list(page_result.__dict__.keys())}")
                                except:
                                    pass
                    else:
                        print("❌ PaddleOCR返回结果为空或格式异常")

                    extracted_text = '\n'.join(text_lines)
                    print(f"✅ PaddleOCR识别完成，共识别 {len(text_lines)} 行文本")
                    print(f"📝 最终提取文本: '{extracted_text}'")

                except Exception as paddle_error:
                    print(f"❌ PaddleOCR识别失败: {paddle_error}")
                    import traceback
                    traceback.print_exc()
                    extracted_text = f"PaddleOCR识别失败: {paddle_error}"
            else:
                extracted_text = "不支持的OCR引擎"

            # 安全计算时间，防止变量未定义错误
            try:
                ocr_time = time.time() - ocr_start_time
                total_time = time.time() - start_time
            except NameError as time_error:
                print(f"⚠️ 时间计算错误: {time_error}")
                ocr_time = 0.0
                total_time = time.time() - start_time if 'start_time' in locals() else 0.0

            # 性能日志
            print(f"OCR识别完成 - 文字长度: {len(extracted_text)} 字符, "
                  f"预处理: {preprocess_time:.3f}s, OCR: {ocr_time:.3f}s, 总计: {total_time:.3f}s")

            # 结果验证
            if not extracted_text or extracted_text.isspace():
                return "未能从图像中识别到文字内容"

            return extracted_text.strip()
            
        except Exception as e:
            error_msg = f"OCR文字识别失败: {e}"
            print(error_msg)
            return error_msg

    def parse_question(self, text: str) -> Dict[str, str]:
        """
        解析题目内容，识别题目类型和关键信息

        Args:
            text: OCR识别的文字内容

        Returns:
            包含题目信息的字典
        """
        # 输入验证
        if not text or not isinstance(text, str):
            return {
                "原始文本": "",
                "题目类型": "输入无效",
                "错误信息": "输入文本为空或格式不正确"
            }

        try:
            # 清理文本
            cleaned_text = re.sub(r'\s+', ' ', text).strip()

            # 长度检查
            if len(cleaned_text) < 5:
                return {
                    "原始文本": cleaned_text,
                    "题目类型": "文本过短",
                    "错误信息": "识别的文本内容过短，可能不是完整题目"
                }

            # 题目类型识别 - 改进识别逻辑，按优先级判断
            question_type = "未知类型"

            # 优先级1：选择题（明确的选项标识）
            if any(keyword in cleaned_text for keyword in ['选择', 'A.', 'B.', 'C.', 'D.', '（A）', '（B）', '(A)', '(B)']):
                question_type = "选择题"
            # 优先级2：填空题（明确的空白标识）
            elif any(keyword in cleaned_text for keyword in ['填空', '______', '___', '（）', '()', '____']):
                question_type = "填空题"
            # 优先级3：翻译题（明确的翻译标识）
            elif any(keyword in cleaned_text for keyword in ['翻译', 'translate', '英译汉', '汉译英', 'Translate']):
                question_type = "翻译题"
            # 优先级4：简答题（明确的简答标识）
            elif any(keyword in cleaned_text for keyword in ['简答', '论述', '分析', '说明', '解释', '阐述']):
                question_type = "简答题"
            # 优先级5：数学计算题（数学符号和关键词）
            elif any(keyword in cleaned_text for keyword in ['计算', '求', '解', '=', '+', '-', '×', '÷', '²', '³']):
                question_type = "数学计算题"
            # 默认：根据内容特征推断
            else:
                # 如果包含数字和运算符，可能是数学题
                if re.search(r'\d+.*[+\-×÷=].*\d+', cleaned_text):
                    question_type = "数学计算题"
                # 如果包含问号，可能是简答题
                elif '?' in cleaned_text or '？' in cleaned_text:
                    question_type = "简答题"

            # 提取关键信息
            key_info = {
                "原始文本": cleaned_text,
                "题目类型": question_type,
                "文字长度": len(cleaned_text),
                "识别时间": time.strftime("%Y-%m-%d %H:%M:%S")
            }

            return key_info

        except Exception as e:
            print(f"题目解析失败: {e}")
            return {
                "原始文本": text,
                "题目类型": "解析失败",
                "错误信息": str(e)
            }

class QuestionSolver:
    """题目分步讲解器"""

    def __init__(self, deepseek_client=None):
        """
        初始化题目讲解器

        Args:
            deepseek_client: DeepSeek API客户端
        """
        self.deepseek_client = deepseek_client
        self.is_available = deepseek_client is not None

    def generate_step_by_step_solution(self, question_info: Dict[str, str]) -> str:
        """
        生成分步讲解

        Args:
            question_info: 题目信息字典

        Returns:
            分步讲解内容
        """
        if not self.is_available:
            return "AI讲解服务暂时不可用，请检查网络连接或稍后重试。"

        # 输入验证
        if not question_info or not isinstance(question_info, dict):
            return "题目信息格式错误，无法生成讲解。"

        try:
            print("🤖 开始生成AI分步讲解...")
            question_text = question_info.get("原始文本", "")
            question_type = question_info.get("题目类型", "未知类型")

            print(f"📝 题目文本: {question_text[:100]}...")
            print(f"📊 题目类型: {question_type}")

            if not question_text or not question_text.strip():
                error_msg = "未能识别到有效的题目内容，请确保图像清晰并重新拍照。"
                print(f"❌ {error_msg}")
                return error_msg

            # 检查题目类型是否有效
            if question_type in ["输入无效", "文本过短", "解析失败"]:
                error_msg = f"题目解析失败：{question_info.get('错误信息', '未知错误')}，请重新拍照。"
                print(f"❌ {error_msg}")
                return error_msg

            # 构建专门的题目讲解提示词
            system_prompt = f"""你是睿课云眸AI学习助手的题目讲解专家。请为学生提供详细的分步讲解。

要求：
1. 首先分析题目类型和考查知识点
2. 提供清晰的解题步骤，每步都要详细说明
3. 解释每步的原理和思路
4. 如果是计算题，要展示完整的计算过程
5. 最后总结解题方法和注意事项
6. 语言要通俗易懂，适合学生理解

题目类型：{question_type}
"""

            user_prompt = f"""请为以下题目提供详细的分步讲解：

{question_text}

请按照以下格式回答：
【题目分析】
【解题步骤】
【详细讲解】
【答案】
【总结】"""

            # 调用DeepSeek API
            print("🔗 准备调用DeepSeek API...")
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            print(f"📤 API请求消息数量: {len(messages)}")
            print(f"📝 用户提示词长度: {len(user_prompt)} 字符")

            # 检查DeepSeek客户端
            if not self.deepseek_client:
                error_msg = "DeepSeek客户端未初始化"
                print(f"❌ {error_msg}")
                return error_msg

            # 添加超时和重试机制
            max_retries = 2
            for attempt in range(max_retries + 1):
                # 初始化时间变量，防止作用域错误
                api_start_time = time.time()
                api_time = 0.0

                try:
                    print(f"🔄 DeepSeek API调用尝试 {attempt + 1}/{max_retries + 1}")

                    response = self.deepseek_client.chat_completion_with_timeout(
                        model="deepseek-chat",
                        messages=messages,
                        temperature=0.3,  # 较低的温度确保回答准确性
                        max_tokens=2000,
                        stream=False
                    )
                    api_time = time.time() - api_start_time
                    print(f"📡 API响应耗时: {api_time:.3f}s")

                    if not response or not response.choices:
                        print("❌ API响应为空或无choices")
                        if attempt < max_retries:
                            continue
                        else:
                            return "API响应异常，请重新尝试。"

                    solution = response.choices[0].message.content.strip()
                    print(f"📝 API返回内容长度: {len(solution)} 字符")

                    if solution and len(solution) > 10:  # 确保回答有实质内容
                        print(f"✅ DeepSeek API调用成功，耗时: {api_time:.3f}s")
                        return solution
                    else:
                        print(f"⚠️ DeepSeek返回内容过短: {solution}")
                        if attempt < max_retries:
                            print("🔄 内容过短，准备重试...")
                            continue
                        else:
                            return "抱歉，AI生成的讲解内容不完整，请重新尝试。"

                except TimeoutError:
                    # 安全计算超时时间
                    try:
                        timeout_time = time.time() - api_start_time
                        print(f"⚠️ DeepSeek API超时 (尝试 {attempt + 1}/{max_retries + 1}, 耗时: {timeout_time:.3f}s)")
                    except NameError:
                        print(f"⚠️ DeepSeek API超时 (尝试 {attempt + 1}/{max_retries + 1})")

                    if attempt < max_retries:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        return "请求超时，请检查网络连接后重试。"

                except Exception as api_error:
                    # 安全计算错误时间
                    try:
                        error_time = time.time() - api_start_time
                        print(f"⚠️ DeepSeek API调用失败 (尝试 {attempt + 1}/{max_retries + 1}, 耗时: {error_time:.3f}s): {api_error}")
                    except NameError:
                        print(f"⚠️ DeepSeek API调用失败 (尝试 {attempt + 1}/{max_retries + 1}): {api_error}")

                    if attempt < max_retries:
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        return f"AI服务暂时不可用：{str(api_error)[:100]}，请稍后重试。"

        except Exception as e:
            error_msg = f"生成分步讲解时出错: {e}"
            print(error_msg)
            return "抱歉，题目讲解服务遇到未知错误，请稍后重试。"

class QuestionRecognitionSystem:
    """题目识别系统 - 主要接口类"""

    def __init__(self, deepseek_client=None):
        """
        初始化题目识别系统

        Args:
            deepseek_client: DeepSeek API客户端
        """
        self.ocr = QuestionOCR()
        self.solver = QuestionSolver(deepseek_client)
        self.is_available = OCR_AVAILABLE

    def process_question_image(self, image: Image.Image) -> Tuple[str, Dict[str, str]]:
        """
        处理题目图像，返回识别结果和讲解

        Args:
            image: 输入的PIL图像

        Returns:
            (讲解内容, 题目信息)
        """
        if not self.is_available:
            return "题目识别功能不可用，请安装OCR依赖库（paddleocr或easyocr）", {}

        # 输入验证
        if image is None:
            return "输入图像为空，请重新拍照", {}

        if not isinstance(image, Image.Image):
            return "输入格式错误，需要PIL图像对象", {}

        # 初始化时间变量，防止作用域错误
        total_start_time = time.time()

        try:
            # 1. OCR文字识别
            print("🔍 开始OCR文字识别...")
            extracted_text = self.ocr.extract_text(image)

            if not extracted_text or "失败" in extracted_text or "错误" in extracted_text:
                return f"OCR识别失败：{extracted_text}，请确保图像清晰且包含文字", {}

            # 2. 题目解析
            print("📝 开始解析题目...")
            question_info = self.ocr.parse_question(extracted_text)

            # 检查解析结果
            if question_info.get("题目类型") in ["输入无效", "文本过短", "解析失败"]:
                error_info = question_info.get("错误信息", "未知错误")
                return f"题目解析失败：{error_info}", question_info

            # 3. 生成分步讲解
            print("🤖 开始生成AI讲解...")
            solution = self.solver.generate_step_by_step_solution(question_info)

            # 安全计算总时间
            try:
                total_time = time.time() - total_start_time
                print(f"✅ 题目处理完成，总耗时: {total_time:.3f}s")
            except NameError:
                print("✅ 题目处理完成")

            return solution, question_info

        except Exception as e:
            # 安全计算错误时的耗时
            try:
                error_time = time.time() - total_start_time
                error_msg = f"题目处理过程中发生错误: {str(e)[:200]} (耗时: {error_time:.3f}s)"
            except NameError:
                error_msg = f"题目处理过程中发生错误: {str(e)[:200]}"

            print(error_msg)
            import traceback
            traceback.print_exc()
            return error_msg, {}

# 全局实例 - 延迟初始化
_question_system = None

def get_question_system(deepseek_client=None):
    """获取题目识别系统实例"""
    global _question_system
    if _question_system is None:
        _question_system = QuestionRecognitionSystem(deepseek_client)
    elif deepseek_client and not _question_system.solver.is_available:
        # 更新DeepSeek客户端
        _question_system.solver = QuestionSolver(deepseek_client)
    return _question_system

def is_ocr_available():
    """检查OCR功能是否可用"""
    return OCR_AVAILABLE

if __name__ == "__main__":
    # 测试代码
    print("题目识别模块测试")
    print(f"OCR可用性: {is_ocr_available()}")
    print(f"OCR引擎: {OCR_ENGINE}")
