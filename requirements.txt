# 睿课云眸 AI学习监督系统 - 依赖库列表
# ===============================================
# 优化版本：兼容conda环境，减少版本冲突

# 核心界面相关（必需）
customtkinter>=5.2.0
Pillow>=9.0.0

# 网络请求（必需）
requests>=2.28.0

# 图像处理（必需）
opencv-python>=4.6.0
numpy>=1.21.0

# 音频处理（必需）
pygame>=2.1.0
edge-tts>=6.1.0

# 系统工具（必需）
psutil>=5.8.0

# AI相关（必需）
openai>=1.0.0

# 时间处理（必需）
python-dateutil>=2.8.0

# 可选依赖（不强制安装，避免版本冲突）
# 以下依赖如果安装失败不会影响核心功能

# 界面增强（可选）
# tkinter-dark

# 网络增强（可选）
# httpx>=0.24.0

# 音频增强（可选）
# pydub>=0.25.0
# pyaudio>=0.2.11

# 云存储（可选）
# oss2>=2.17.0

# JSON增强（可选）
# json5>=0.9.0

# 多线程增强（可选）
# threading-timeout>=0.2.0

# OCR功能（可选，大文件）
# paddleocr

# 数据可视化（可选）
# matplotlib>=3.5.0