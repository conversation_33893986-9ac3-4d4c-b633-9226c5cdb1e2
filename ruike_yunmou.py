import os
import cv2
import time
import io
import threading
import queue
import numpy as np
import asyncio
import functools
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
import customtkinter as ctk
from PIL import Image, ImageTk, ImageDraw, ImageFont
import oss2
from openai import OpenAI
import dashscope
from datetime import datetime
import re
import logging
import pyaudio
import wave
from pydub import AudioSegment
from pydub.playback import play
import pygame
import sys
import gc  # 导入垃圾回收模块
import psutil  # 如果已安装psutil库
import socket
import weakref  # 用于弱引用，避免内存泄漏
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from enhanced_tts import EnhancedTTS  # 使用增强版TTS
from behavior_prompts import *  # 导入行为提示词配置
import signal  # 添加信号处理以捕获segmentation fault
import json  # 用于学习计划数据持久化
from pathlib import Path  # 用于文件路径处理

# 题目识别相关导入
try:
    from question_ocr import get_question_system, is_ocr_available
    QUESTION_OCR_AVAILABLE = True
    print("✅ 题目识别模块导入成功")
except ImportError as e:
    print(f"⚠️ 题目识别模块导入失败: {e}")
    QUESTION_OCR_AVAILABLE = False

# 新增：导入数据可视化和分析相关库 - 添加安全防护避免segfault
try:
    # 设置matplotlib后端为非GUI模式，避免与CustomTkinter冲突
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端

    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    import seaborn as sns
    import pandas as pd

    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 延迟导入GUI后端，仅在需要时使用
    def get_figure_canvas():
        try:
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            return FigureCanvasTkAgg
        except ImportError:
            return None

    VISUALIZATION_AVAILABLE = True
    print("✅ 数据可视化库导入成功（安全模式）")
except ImportError as e:
    VISUALIZATION_AVAILABLE = False
    print(f"⚠️ 数据可视化库导入失败: {e}")
    print("   将使用文本形式的分析报告")

# ===================== 安全防护配置 =====================
def signal_handler(signum, frame):
    """处理系统信号，特别是segmentation fault"""
    print(f"\n❌ 接收到系统信号 {signum}")
    if signum == signal.SIGSEGV:
        print("💥 检测到段错误(Segmentation Fault)!")
        print("🔍 可能的原因:")
        print("   - OpenCV摄像头初始化问题")
        print("   - CustomTkinter与系统GUI冲突")
        print("   - 内存访问违规")
        print("   - 第三方库兼容性问题")
        print("🛠️  建议解决方案:")
        print("   - 检查摄像头连接")
        print("   - 更新Python库版本")
        print("   - 重启系统")

    # 尝试优雅退出
    try:
        import gc
        gc.collect()
        print("🧹 执行内存清理")
    except:
        pass

    sys.exit(1)

# 注册信号处理器
try:
    signal.signal(signal.SIGSEGV, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    print("✅ 信号处理器注册成功")
except Exception as signal_error:
    print(f"⚠️ 信号处理器注册失败: {signal_error}")

# ===================== 番茄钟组件 =====================
class PomodoroTimer:
    """番茄钟计时器组件 - 支持工作和休息时间管理"""

    def __init__(self, parent_app):
        self.parent_app = parent_app

        # 时间设置（分钟）
        self.work_duration = 25  # 工作时间默认25分钟
        self.break_duration = 5  # 休息时间默认5分钟
        self.long_break_duration = 15  # 长休息时间15分钟

        # 状态管理
        self.is_running = False
        self.is_paused = False
        self.current_mode = "work"  # "work", "break", "long_break"
        self.sessions_completed = 0  # 完成的工作会话数

        # 时间管理
        self.remaining_seconds = self.work_duration * 60
        self.timer_thread = None
        self.timer_active = False

        # UI组件引用 - 移除停止按钮引用
        self.container_frame = None
        self.time_display = None
        self.status_label = None
        self.start_pause_button = None
        self.reset_button = None
        self.settings_button = None

        # 番茄钟橙色/红色主题配色 - 番茄主题色彩
        self.colors = {
            'primary': "#fed7aa",      # 橙色主要背景色 - 番茄色调
            'secondary': "#fef3c7",    # 浅橙色次要背景色
            'accent': "#dc2626",       # 红色强调色 - 番茄红
            'text_primary': "#7c2d12", # 深棕红色文字 - 番茄茎色
            'text_secondary': "#92400e", # 次要文字色
            'success': "#10b981",      # 成功色（绿色）
            'warning': "#f59e0b",      # 警告色（橙色）
            'error': "#ef4444",        # 错误色（红色）
            'white': "#ffffff",        # 白色
            'border': "#ea580c",       # 橙红色边框 - 番茄边缘色
            # 番茄主题配色
            'card_bg': "#fff7ed",      # 卡片背景色（柔和橙色）
            'shadow': "#dc2626",       # 阴影色（番茄红）
            'gray_100': "#f3f4f6",     # 浅灰色
            'gray_200': "#e5e7eb"      # 中灰色
        }

    def create_ui(self, parent):
        """创建番茄钟UI组件 - 参照学习报告设计风格"""
        try:
            # 主容器框架 - 番茄钟橙色主题设计
            self.container_frame = ctk.CTkFrame(
                parent,
                corner_radius=12,  # 保持圆角设计
                fg_color=self.colors['primary'],  # 使用橙色主背景色
                border_width=3,    # 增加边框宽度与学习目标框一致
                border_color=self.colors['border'],  # 使用橙红色边框
                width=380,  # 适当宽度确保内容显示
                height=85   # 与学习目标框高度一致
            )

            # 内容容器 - 优化内边距确保文字显示空间充足
            content_frame = ctk.CTkFrame(
                self.container_frame,
                corner_radius=0,
                fg_color="transparent"
            )
            content_frame.pack(fill="both", expand=True, padx=8, pady=8)  # 增加垂直内边距

            # 左侧：时间显示和状态 - 增大显示区域
            left_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
            left_frame.pack(side="left", fill="both", expand=True, padx=(0, 12))

            # 时间显示 - 优化字体大小和布局以平衡显示空间
            self.time_display = ctk.CTkLabel(
                left_frame,
                text=self._format_time(self.remaining_seconds),
                font=ctk.CTkFont(size=28, weight="bold", family="Monaco"),  # 适度减小字体为状态文字留空间
                text_color=self.colors['text_primary'],
                width=150,  # 增加宽度适应更大字体
                height=38   # 适度减小高度为状态标签留出空间
            )
            self.time_display.pack(anchor="center", pady=(6, 1))  # 增加上边距，减少下边距

            # 状态显示 - 优化字体大小和间距以确保清晰显示
            self.status_label = ctk.CTkLabel(
                left_frame,
                text="🍅 工作时间",
                font=ctk.CTkFont(size=12, weight="normal"),  # 增大状态文字以提高可读性
                text_color=self.colors['text_secondary'],
                width=150,   # 与时间显示保持一致
                height=20    # 明确设置高度确保文字有足够显示空间
            )
            self.status_label.pack(anchor="center", pady=(2, 6))  # 增加上下边距确保不被遮挡

            # 右侧：控制按钮 - 改为水平布局避免重叠
            right_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
            right_frame.pack(side="right", fill="y", padx=(8, 8))

            # 按钮容器 - 水平排列三个按钮，优化垂直居中
            button_container = ctk.CTkFrame(right_frame, fg_color="transparent")
            button_container.pack(expand=True, fill="both", pady=8)  # 减少容器边距，为按钮留更多空间

            # 开始/暂停按钮 - 简洁透明设计，优化logo显示
            self.start_pause_button = ctk.CTkButton(
                button_container,
                text="▶️",
                width=40,  # 增加宽度确保logo完整显示
                height=36, # 增加高度避免logo被遮挡
                font=ctk.CTkFont(size=16),  # 放大字体使logo更清晰
                fg_color="transparent",  # 透明背景，简洁美观
                hover_color=self.colors['secondary'],  # 悬停时浅橙色反馈
                border_width=0,  # 移除边框
                command=self.toggle_timer
            )
            self.start_pause_button.pack(side="left", padx=(3, 2), pady=6)  # 增加垂直间距

            # 重置按钮 - 简洁透明设计，优化logo显示
            self.reset_button = ctk.CTkButton(
                button_container,
                text="🔄",
                width=40,  # 增加宽度确保logo完整显示
                height=36, # 增加高度避免logo被遮挡
                font=ctk.CTkFont(size=16),  # 放大字体使logo更清晰
                fg_color="transparent",  # 透明背景，简洁美观
                hover_color=self.colors['secondary'],  # 悬停时浅橙色反馈
                border_width=0,  # 移除边框
                command=self.reset_timer
            )
            self.reset_button.pack(side="left", padx=2, pady=6)  # 增加垂直间距

            # 设置按钮 - 简洁透明设计，优化logo显示
            self.settings_button = ctk.CTkButton(
                button_container,
                text="⚙️",
                width=40,  # 增加宽度确保logo完整显示
                height=36, # 增加高度避免logo被遮挡
                font=ctk.CTkFont(size=16),  # 放大字体使logo更清晰
                fg_color="transparent",  # 透明背景，简洁美观
                hover_color=self.colors['secondary'],  # 悬停时浅橙色反馈
                border_width=0,  # 移除边框
                command=self.open_settings
            )
            self.settings_button.pack(side="left", padx=(2, 3), pady=6)  # 增加垂直间距

            return self.container_frame

        except Exception as e:
            print(f"创建番茄钟UI失败: {e}")
            return None

    def _format_time(self, seconds):
        """格式化时间显示为MM:SS"""
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"

    def _darken_color(self, color):
        """将颜色变暗用于hover效果"""
        # 简单的颜色变暗逻辑
        if color == self.colors['success']:
            return "#059669"
        elif color == self.colors['warning']:
            return "#d97706"
        elif color == self.colors['error']:
            return "#dc2626"
        elif color == self.colors['accent']:
            return "#4f46e5"
        return color

    def toggle_timer(self):
        """切换计时器开始/暂停状态"""
        try:
            if not self.is_running:
                self.start_timer()
            else:
                if self.is_paused:
                    self.resume_timer()
                else:
                    self.pause_timer()
        except Exception as e:
            print(f"切换计时器状态失败: {e}")

    def start_timer(self):
        """开始计时器"""
        try:
            if not self.timer_active:
                self.is_running = True
                self.is_paused = False
                self.timer_active = True

                # 更新按钮状态
                self.start_pause_button.configure(text="⏸️")

                # 启动计时器线程
                self.timer_thread = threading.Thread(target=self._timer_loop, daemon=True)
                self.timer_thread.start()

                print(f"番茄钟开始: {self.current_mode}模式, {self._format_time(self.remaining_seconds)}")

        except Exception as e:
            print(f"启动计时器失败: {e}")

    def pause_timer(self):
        """暂停计时器"""
        try:
            self.is_paused = True
            self.start_pause_button.configure(text="▶️")
            print("番茄钟已暂停")
        except Exception as e:
            print(f"暂停计时器失败: {e}")

    def resume_timer(self):
        """恢复计时器"""
        try:
            self.is_paused = False
            self.start_pause_button.configure(text="⏸️")
            print("番茄钟已恢复")
        except Exception as e:
            print(f"恢复计时器失败: {e}")

    def reset_timer(self):
        """重置计时器"""
        try:
            self.stop_timer()

            # 重置时间
            if self.current_mode == "work":
                self.remaining_seconds = self.work_duration * 60
            elif self.current_mode == "break":
                self.remaining_seconds = self.break_duration * 60
            else:  # long_break
                self.remaining_seconds = self.long_break_duration * 60

            # 更新显示
            self._update_display()
            print("番茄钟已重置")

        except Exception as e:
            print(f"重置计时器失败: {e}")

    def stop_timer(self):
        """停止计时器"""
        try:
            self.is_running = False
            self.is_paused = False
            self.timer_active = False

            # 更新按钮状态
            self.start_pause_button.configure(text="▶️")

            print("番茄钟已停止")

        except Exception as e:
            print(f"停止计时器失败: {e}")

    def _timer_loop(self):
        """计时器主循环"""
        try:
            while self.timer_active and self.remaining_seconds > 0:
                if not self.is_paused:
                    # 更新显示
                    if hasattr(self.parent_app, 'after'):
                        self.parent_app.after(0, self._update_display)

                    time.sleep(1)
                    self.remaining_seconds -= 1
                else:
                    # 暂停状态下短暂休眠
                    time.sleep(0.1)

            # 时间到达
            if self.timer_active and self.remaining_seconds <= 0:
                if hasattr(self.parent_app, 'after'):
                    self.parent_app.after(0, self._on_timer_complete)

        except Exception as e:
            print(f"计时器循环出错: {e}")

    def _update_display(self):
        """更新时间显示"""
        try:
            if self.time_display:
                self.time_display.configure(text=self._format_time(self.remaining_seconds))

            # 更新状态显示
            if self.status_label:
                if self.current_mode == "work":
                    status_text = "🍅 工作时间"
                    if self.is_paused:
                        status_text += " (已暂停)"
                elif self.current_mode == "break":
                    status_text = "☕ 短休息"
                    if self.is_paused:
                        status_text += " (已暂停)"
                else:  # long_break
                    status_text = "🌟 长休息"
                    if self.is_paused:
                        status_text += " (已暂停)"

                self.status_label.configure(text=status_text)

        except Exception as e:
            print(f"更新显示失败: {e}")

    def _on_timer_complete(self):
        """计时器完成时的处理"""
        try:
            self.stop_timer()

            # 播放提醒
            self._play_notification()

            # 切换模式
            if self.current_mode == "work":
                self.sessions_completed += 1
                # 每4个工作会话后进行长休息
                if self.sessions_completed % 4 == 0:
                    self.current_mode = "long_break"
                    self.remaining_seconds = self.long_break_duration * 60
                else:
                    self.current_mode = "break"
                    self.remaining_seconds = self.break_duration * 60
            else:  # break or long_break
                self.current_mode = "work"
                self.remaining_seconds = self.work_duration * 60

            # 更新显示
            self._update_display()

            print(f"番茄钟完成! 切换到{self.current_mode}模式")

        except Exception as e:
            print(f"处理计时器完成失败: {e}")

    def _play_notification(self):
        """播放完成提醒"""
        try:
            # 视觉提醒 - 改变组件颜色
            self._flash_notification()

            # 音频提醒 - 与现有TTS系统集成
            if hasattr(self.parent_app, 'audio_player') and self.parent_app.audio_player:
                if self.current_mode == "work":
                    message = "🍅 番茄钟提醒：工作时间结束，该休息一下了！建议站起来活动活动，喝点水，让眼睛休息一下。"
                    # 添加系统通知到聊天界面
                    self.parent_app.add_ai_message(
                        "🍅 工作时间结束！\n\n恭喜完成一个番茄钟工作周期！现在是休息时间，建议：\n• 站起来活动一下\n• 喝点水补充水分\n• 让眼睛远眺放松\n• 做几个深呼吸",
                        is_system_notification=True
                    )
                else:
                    message = "⏰ 番茄钟提醒：休息时间结束，开始新的工作会话吧！保持专注，你可以做到的！"
                    # 添加系统通知到聊天界面
                    self.parent_app.add_ai_message(
                        "⏰ 休息时间结束！\n\n准备开始新的工作会话：\n• 整理好桌面和学习资料\n• 确定这个番茄钟的学习目标\n• 关闭干扰源（手机静音等）\n• 保持专注，全力以赴！",
                        is_system_notification=True
                    )

                # 添加到TTS队列，高优先级
                if hasattr(self.parent_app.audio_player, 'add_to_queue'):
                    self.parent_app.audio_player.add_to_queue(message, priority=True)
                elif hasattr(self.parent_app.audio_player, 'play_text'):
                    self.parent_app.audio_player.play_text(message, priority=1)
            else:
                print("番茄钟提醒: 时间到!")

        except Exception as e:
            print(f"播放提醒失败: {e}")

    def _flash_notification(self):
        """视觉闪烁提醒 - 使用更明显的橙色闪烁"""
        try:
            if self.container_frame:
                original_color = self.container_frame.cget("fg_color")
                flash_color = "#ff6b35"  # 更亮的橙色用于闪烁

                # 闪烁4次，更明显的提醒
                def flash_sequence(count=0):
                    if count < 8:  # 4次闪烁 = 8次颜色变化
                        new_color = flash_color if count % 2 == 0 else original_color
                        self.container_frame.configure(fg_color=new_color)
                        # 稍微加快闪烁速度
                        self.parent_app.after(250, lambda: flash_sequence(count + 1))
                    else:
                        # 恢复原色
                        self.container_frame.configure(fg_color=original_color)

                flash_sequence()

        except Exception as e:
            print(f"视觉提醒失败: {e}")

    def open_settings(self):
        """打开番茄钟设置窗口 - 参照学习报告设计风格"""
        try:
            # 创建设置窗口 - 采用现代化设计
            settings_win = ctk.CTkToplevel(self.parent_app)
            settings_win.title("🍅 番茄钟设置")
            settings_win.geometry("450x400")  # 稍微增大窗口
            settings_win.configure(fg_color=self.colors['white'])
            settings_win.transient(self.parent_app)
            settings_win.grab_set()

            # 居中显示
            settings_win.update_idletasks()
            x = (settings_win.winfo_screenwidth() // 2) - (450 // 2)
            y = (settings_win.winfo_screenheight() // 2) - (400 // 2)
            settings_win.geometry(f"450x400+{x}+{y}")

            # 标题区域 - 参照学习报告的标题设计
            title_frame = ctk.CTkFrame(
                settings_win,
                corner_radius=12,
                fg_color=self.colors['gray_100'],
                height=70
            )
            title_frame.pack(fill="x", padx=20, pady=(20, 15))
            title_frame.pack_propagate(False)

            title_label = ctk.CTkLabel(
                title_frame,
                text="🍅 番茄钟设置",
                font=ctk.CTkFont(size=22, weight="bold"),
                text_color=self.colors['text_primary']
            )
            title_label.pack(expand=True)

            # 设置容器 - 采用卡片式设计
            settings_frame = ctk.CTkFrame(
                settings_win,
                corner_radius=12,  # 参照学习报告的圆角
                fg_color=self.colors['card_bg'],  # 使用柔和的卡片背景
                border_width=1,    # 减少边框宽度
                border_color=self.colors['gray_200']  # 使用柔和边框色
            )
            settings_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

            # 工作时间设置
            work_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
            work_frame.pack(fill="x", padx=20, pady=15)

            ctk.CTkLabel(
                work_frame,
                text="🍅 工作时间 (分钟):",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['text_primary']
            ).pack(side="left")

            work_entry = ctk.CTkEntry(
                work_frame,
                width=80,
                font=ctk.CTkFont(size=14),
                fg_color=self.colors['white'],
                border_color=self.colors['border']
            )
            work_entry.pack(side="right")
            work_entry.insert(0, str(self.work_duration))

            # 短休息时间设置
            break_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
            break_frame.pack(fill="x", padx=20, pady=15)

            ctk.CTkLabel(
                break_frame,
                text="☕ 短休息时间 (分钟):",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['text_primary']
            ).pack(side="left")

            break_entry = ctk.CTkEntry(
                break_frame,
                width=80,
                font=ctk.CTkFont(size=14),
                fg_color=self.colors['white'],
                border_color=self.colors['border']
            )
            break_entry.pack(side="right")
            break_entry.insert(0, str(self.break_duration))

            # 长休息时间设置
            long_break_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
            long_break_frame.pack(fill="x", padx=20, pady=15)

            ctk.CTkLabel(
                long_break_frame,
                text="🌟 长休息时间 (分钟):",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.colors['text_primary']
            ).pack(side="left")

            long_break_entry = ctk.CTkEntry(
                long_break_frame,
                width=80,
                font=ctk.CTkFont(size=14),
                fg_color=self.colors['white'],
                border_color=self.colors['border']
            )
            long_break_entry.pack(side="right")
            long_break_entry.insert(0, str(self.long_break_duration))

            # 按钮区域
            button_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
            button_frame.pack(fill="x", padx=20, pady=20)

            def save_settings():
                try:
                    # 验证输入
                    work_time = int(work_entry.get())
                    break_time = int(break_entry.get())
                    long_break_time = int(long_break_entry.get())

                    if work_time < 1 or break_time < 1 or long_break_time < 1:
                        raise ValueError("时间必须大于0分钟")

                    if work_time > 120 or break_time > 60 or long_break_time > 120:
                        raise ValueError("时间设置过长")

                    # 保存设置
                    self.work_duration = work_time
                    self.break_duration = break_time
                    self.long_break_duration = long_break_time

                    # 立即更新显示时间（无论是否正在运行）
                    if not self.is_running:
                        # 如果没有运行，根据当前模式更新剩余时间
                        if self.current_mode == "work":
                            self.remaining_seconds = self.work_duration * 60
                        elif self.current_mode == "break":
                            self.remaining_seconds = self.break_duration * 60
                        else:
                            self.remaining_seconds = self.long_break_duration * 60

                    # 强制更新显示，确保新设置立即生效
                    self._update_display()

                    # 如果有父应用，通知更新
                    if hasattr(self, 'parent_app') and hasattr(self.parent_app, 'update_pomodoro_display'):
                        self.parent_app.update_pomodoro_display()

                    settings_win.destroy()
                    print(f"✅ 番茄钟设置已保存并同步: 工作{work_time}分钟, 短休息{break_time}分钟, 长休息{long_break_time}分钟")

                except ValueError as e:
                    # 显示错误消息
                    error_label = ctk.CTkLabel(
                        settings_frame,
                        text=f"❌ 输入错误: {str(e)}",
                        font=ctk.CTkFont(size=12),
                        text_color=self.colors['error']
                    )
                    error_label.pack(pady=5)
                    # 3秒后移除错误消息
                    settings_win.after(3000, error_label.destroy)

            # 保存按钮 - 保持适度的颜色用于重要操作
            save_button = ctk.CTkButton(
                button_frame,
                text="💾 保存设置",
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=self.colors['success'],
                hover_color=self._darken_color(self.colors['success']),
                border_width=0,
                corner_radius=8,
                command=save_settings
            )
            save_button.pack(side="left", padx=(0, 10))

            # 取消按钮 - 使用更柔和的样式
            cancel_button = ctk.CTkButton(
                button_frame,
                text="❌ 取消",
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=self.colors['secondary'],
                hover_color=self.colors['border'],
                text_color=self.colors['text_primary'],
                border_width=1,
                border_color=self.colors['border'],
                corner_radius=8,
                command=settings_win.destroy
            )
            cancel_button.pack(side="right")

        except Exception as e:
            print(f"打开设置窗口失败: {e}")

    def get_status_info(self):
        """获取番茄钟状态信息"""
        return {
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'current_mode': self.current_mode,
            'remaining_time': self._format_time(self.remaining_seconds),
            'sessions_completed': self.sessions_completed
        }

# ===================== 学习计划数据管理 =====================
class LearningPlanManager:
    """学习计划数据管理器 - 处理计划的创建、编辑、保存和加载"""

    def __init__(self):
        self.data_file = Path("learning_plans.json")
        self.backup_file = Path("learning_plans_backup.json")
        self.default_plans = self._create_default_plans()
        self.current_plans = self.load_plans()

    def _create_default_plans(self):
        """创建默认学习计划模板"""
        # 定义数据数组
        subjects = ["数学", "语文", "英语", "物理", "化学", "生物", "历史"]
        topics = ["代数基础", "阅读理解", "语法练习", "力学原理", "化学反应", "细胞结构", "古代史"]
        goals = ["掌握基本概念", "提高解题速度", "增强理解能力", "巩固知识点", "拓展思维", "实践应用", "综合复习"]
        priorities = ["高", "中", "高", "中", "低", "中", "高"]
        difficulties = ["中等", "简单", "困难", "中等", "简单", "中等", "困难"]
        statuses = ["已完成", "已完成", "已完成", "进行中", "计划中", "计划中", "计划中"]

        return {
            "weekly_schedule": [
                {
                    "id": f"plan_{i}",
                    "day": day,
                    "day_index": i,
                    "start_time": "08:00",
                    "end_time": "10:00",
                    "duration": 120,
                    "subject": subjects[i % len(subjects)],
                    "topic": topics[i % len(topics)],
                    "goals": goals[i % len(goals)],
                    "priority": priorities[i % len(priorities)],
                    "difficulty": difficulties[i % len(difficulties)],
                    "status": statuses[i % len(statuses)],
                    "notes": "",
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
                for i, day in enumerate(["周一", "周二", "周三", "周四", "周五", "周六", "周日"])
            ],
            "templates": {
                "daily_routine": {
                    "name": "日常学习模板",
                    "sessions": [
                        {"time": "08:00-10:00", "subject": "数学", "type": "核心学习"},
                        {"time": "10:30-12:00", "subject": "语文", "type": "阅读理解"},
                        {"time": "14:00-16:00", "subject": "英语", "type": "听说练习"},
                        {"time": "19:00-21:00", "subject": "复习", "type": "知识巩固"}
                    ]
                },
                "exam_preparation": {
                    "name": "考试准备模板",
                    "sessions": [
                        {"time": "07:00-09:00", "subject": "重点科目", "type": "强化训练"},
                        {"time": "09:30-11:30", "subject": "薄弱环节", "type": "专项突破"},
                        {"time": "14:00-17:00", "subject": "模拟测试", "type": "实战演练"},
                        {"time": "19:00-21:00", "subject": "错题整理", "type": "查漏补缺"}
                    ]
                }
            },
            "settings": {
                "auto_save": True,
                "conflict_detection": True,
                "smart_suggestions": True,
                "backup_enabled": True
            }
        }

    def load_plans(self):
        """从文件加载学习计划"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print("✅ 学习计划数据加载成功")
                    return data
            else:
                print("📝 创建新的学习计划数据")
                return self.default_plans.copy()
        except Exception as e:
            print(f"⚠️ 加载学习计划失败: {e}")
            return self.default_plans.copy()

    def save_plans(self, backup=True):
        """保存学习计划到文件"""
        try:
            # 创建备份
            if backup and self.data_file.exists():
                import shutil
                shutil.copy2(self.data_file, self.backup_file)

            # 保存当前数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_plans, f, ensure_ascii=False, indent=2)

            print("✅ 学习计划保存成功")
            return True
        except Exception as e:
            print(f"❌ 保存学习计划失败: {e}")
            return False

# ===================== 性能优化配置 =====================
API_TIMEOUT = 45  # API超时时间（秒）- 增加到45秒用于图像分析
RETRY_ATTEMPTS = 2  # 重试次数
MAX_WORKERS = 3  # 线程池大小

# ===================== 学习状态分析数据管理类 =====================
class LearningAnalytics:
    """学习状态分析和统计管理类"""
    
    def __init__(self):
        self.session_start = datetime.now()
        self.behavior_records = []  # 行为记录
        self.attention_history = []  # 注意力历史
        self.concentration_scores = []  # 专注度分数历史
        self.learning_sessions = []  # 学习会话记录
        self.warning_history = []  # 警告历史
        self.break_periods = []  # 休息时段
        
        # 行为统计
        self.behavior_counts = {
            "1": 0,  # 认真学习
            "2": 0,  # 轻度走神
            "3": 0,  # 使用学习工具
            "4": 0,  # 喝水休息
            "5": 0,  # 玩手机分心
            "6": 0,  # 与同学交流学习内容
            "7": 0,  # 睡觉或趴桌子
            "8": 0,  # 其他分心行为
            "9": 0,  # 吃零食
            "10": 0  # 不在座位上
        }
        
        # 内存管理
        self.max_records = 1000  # 最大记录数，防止内存溢出
        self.cleanup_threshold = 800  # 清理阈值
        
    def add_behavior_record(self, behavior_num, behavior_desc, concentration_score, analysis_text=""):
        """添加行为记录"""
        try:
            record = {
                "timestamp": datetime.now(),
                "behavior_num": str(behavior_num),
                "behavior_desc": behavior_desc,
                "concentration_score": concentration_score,
                "analysis_text": analysis_text,
                "attention_level": self._calculate_attention_level(concentration_score)
            }
            
            self.behavior_records.append(record)
            self.behavior_counts[str(behavior_num)] = self.behavior_counts.get(str(behavior_num), 0) + 1
            self.concentration_scores.append({
                "timestamp": record["timestamp"],
                "score": concentration_score
            })
            
            # 内存管理：超过阈值时清理旧数据
            if len(self.behavior_records) > self.max_records:
                self._cleanup_old_data()
                
        except Exception as e:
            print(f"添加行为记录失败: {e}")
    
    def _calculate_attention_level(self, concentration_score):
        """根据专注度分数计算注意力水平"""
        if concentration_score >= 85:
            return "高度集中"
        elif concentration_score >= 70:
            return "较为集中"
        elif concentration_score >= 50:
            return "轻微分散"
        else:
            return "注意力分散"
    
    def _cleanup_old_data(self):
        """清理旧数据，保持内存使用合理"""
        try:
            # 保留最近的数据
            keep_count = self.cleanup_threshold
            self.behavior_records = self.behavior_records[-keep_count:]
            self.concentration_scores = self.concentration_scores[-keep_count:]
            gc.collect()  # 强制垃圾回收
        except Exception as e:
            print(f"数据清理失败: {e}")
    
    def get_session_summary(self):
        """获取本次学习会话摘要"""
        if not self.behavior_records:
            return {
                "duration": 0,
                "total_behaviors": 0,
                "avg_concentration": 0,
                "dominant_behavior": "暂无数据"
            }
        
        try:
            duration = (datetime.now() - self.session_start).total_seconds() / 60
            total_behaviors = len(self.behavior_records)
            avg_concentration = sum(record["concentration_score"] for record in self.behavior_records) / total_behaviors
            
            # 找出主要行为
            if self.behavior_counts:
                dominant_behavior_num = max(self.behavior_counts.items(), key=lambda x: x[1])[0]
                behavior_names = {
                    "1": "认真学习", "2": "轻度走神", "3": "使用学习工具", "4": "喝水休息",
                    "5": "玩手机分心", "6": "与同学交流学习内容", "7": "睡觉或趴桌子", "8": "其他分心行为",
                    "9": "吃零食", "10": "不在座位上"
                }
                dominant_behavior = behavior_names.get(dominant_behavior_num, "未知行为")
            else:
                dominant_behavior = "暂无数据"
            
            return {
                "duration": round(duration, 1),
                "total_behaviors": total_behaviors,
                "avg_concentration": round(avg_concentration, 1),
                "dominant_behavior": dominant_behavior
            }
        except Exception as e:
            print(f"获取会话摘要失败: {e}")
            return {
                "duration": 0,
                "total_behaviors": 0,
                "avg_concentration": 0,
                "dominant_behavior": "数据错误"
            }
    
    def get_behavior_distribution(self):
        """获取行为分布统计"""
        behavior_names = {
            "1": "认真学习", "2": "轻度走神", "3": "使用学习工具", "4": "喝水休息",
            "5": "玩手机分心", "6": "与同学交流学习内容", "7": "睡觉或趴桌子", "8": "其他分心行为",
            "9": "吃零食", "10": "不在座位上"
        }
        
        distribution = {}
        total = sum(self.behavior_counts.values())
        
        if total > 0:
            for num, count in self.behavior_counts.items():
                name = behavior_names.get(num, f"行为{num}")
                percentage = (count / total) * 100
                distribution[name] = {
                    "count": count,
                    "percentage": round(percentage, 1)
                }
        
        return distribution
    
    def get_concentration_trend(self):
        """获取专注度趋势数据"""
        if len(self.concentration_scores) < 2:
            return {"trend": "无足够数据", "recent_avg": 0, "change": 0}
        
        try:
            # 计算最近的平均专注度
            recent_scores = self.concentration_scores[-10:]  # 最近10次
            recent_avg = sum(score["score"] for score in recent_scores) / len(recent_scores)
            
            # 计算趋势
            earlier_scores = self.concentration_scores[-20:-10] if len(self.concentration_scores) >= 20 else self.concentration_scores[:-10]
            if earlier_scores:
                earlier_avg = sum(score["score"] for score in earlier_scores) / len(earlier_scores)
                change = recent_avg - earlier_avg
                
                if change > 5:
                    trend = "显著上升"
                elif change > 2:
                    trend = "轻微上升"
                elif change < -5:
                    trend = "显著下降"
                elif change < -2:
                    trend = "轻微下降"
                else:
                    trend = "基本稳定"
            else:
                trend = "数据不足"
                change = 0
            
            return {
                "trend": trend,
                "recent_avg": round(recent_avg, 1),
                "change": round(change, 1)
            }
        except Exception as e:
            print(f"计算专注度趋势失败: {e}")
            return {"trend": "计算错误", "recent_avg": 0, "change": 0}

# ===================== 增强错误处理装饰器 =====================
def retry_on_failure(max_attempts=2, delay=1, exceptions=(Exception,)):
    """重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_attempts - 1:
                        raise e
                    print(f"重试 {func.__name__} (第{attempt+1}次): {e}")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

def handle_exceptions(default_return=None):
    """异常处理装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                print(f"错误在 {func.__name__}: {e}")
                return default_return
        return wrapper
    return decorator

# 导入增强版DeepSeek聊天模块
# 注释掉不存在的模块导入
# try:
#     from enhanced_deepseek_chat import EnhancedDeepSeekChat
#     DEEPSEEK_CHAT_AVAILABLE = True
#     print("增强版DeepSeek聊天模块导入成功")
# except ImportError as e:
#     print(f"增强版DeepSeek聊天模块导入失败: {e}")
#     DEEPSEEK_CHAT_AVAILABLE = False

# 增强版DeepSeek聊天功能暂未实现
DEEPSEEK_CHAT_AVAILABLE = False

# ---------------- Configuration ----------------
# OSS Configuration  
OSS_ACCESS_KEY_ID = 'LTAI5tJeEXJh5dDCaGNKAE2Y'
OSS_ACCESS_KEY_SECRET = '******************************'
OSS_ENDPOINT = 'oss-cn-beijing.aliyuncs.com'
OSS_BUCKET = 'lijiale001'

# 学习行为关键词定义
BEHAVIOR_KEYWORDS = {
    "1": ["认真学习", "专注", "做作业", "阅读", "看书", "写字", "笔记", "听讲", "学习", "作业", "专心", "独自学习", "安静学习", "一个人", "专注于自己"],
    "2": ["轻度走神", "东张西望", "发呆", "思考", "望向窗外", "分散", "不够专注", "注意力分散"],
    "3": ["使用学习工具", "尺子", "计算器", "铅笔盒", "工具", "测量", "计算", "文具"],
    "4": ["喝水休息", "休息", "水杯", "喝水", "短暂休息", "喝饮料"],
    "5": ["玩手机分心", "手机", "玩游戏", "社交媒体", "分心设备", "玩手机", "看手机", "偷偷看手机"],
    "6": ["嘴巴明显张开", "明显张嘴", "张嘴说话", "开口说话", "说话动作", "口型变化", "嘴部动作明显", "嘴唇明显分开", "说话姿态", "交谈动作", "嘴巴在动", "口型明显", "明显说话", "嘴部有动作", "张口说话", "开口交流", "说话状态", "发声动作"],
    "7": ["睡觉或趴桌子", "睡觉", "趴着", "疲倦", "趴在桌子上", "趴桌", "睡着"],
    "8": ["其他分心行为", "玩耍", "做与学习无关的事", "分心", "注意力不集中", "无关", "分心行为"],
    "9": ["吃零食", "吃东西", "零食", "食物", "点心", "饼干", "糖果", "巧克力", "薯片", "包装袋"],
    "10": ["不在座位上", "离开", "不在位置", "不在画面中", "看不到学生", "离开座位", "站起来", "走动", "无人", "座位是空的", "画面中看不到"]
}

# 学习行为描述
BEHAVIOR_DESCRIPTIONS = {
    "1": "认真学习",
    "2": "轻度走神",
    "3": "使用学习工具",
    "4": "喝水休息",
    "5": "玩手机分心",
    "6": "与同学交流学习内容",
    "7": "睡觉或趴桌子",
    "8": "其他分心行为",
    "9": "吃零食",
    "10": "不在座位上",
    "0": "未识别"
}

# 学习行为匹配模式
BEHAVIOR_PATTERNS = [
    (r'认真学习|专注|做作业|阅读|看书|写字|笔记|听讲', "1", "认真学习"),
    (r'轻度走神|东张西望|发呆|思考|望向窗外', "2", "轻度走神"),
    (r'使用学习工具|尺子|计算器|铅笔盒|工具', "3", "使用学习工具"),
    (r'喝水休息|休息|水杯|喝水|短暂休息', "4", "喝水休息"),
    (r'玩手机分心|手机|玩游戏|社交媒体|分心设备', "5", "玩手机分心"),
    (r'嘴巴明显张开|明显张嘴|张嘴说话|开口说话|说话动作|口型变化|嘴部动作明显|嘴唇明显分开|说话姿态|交谈动作|嘴巴在动|口型明显|明显说话|嘴部有动作|张口说话|开口交流|说话状态|发声动作', "6", "与同学交流学习内容"),
    (r'睡觉或趴桌子|睡觉|趴着|疲倦|休息', "7", "睡觉或趴桌子"),
    (r'其他分心行为|玩耍|做与学习无关的事|分心|注意力不集中', "8", "其他分心行为"),
    (r'吃零食|吃东西|零食|食物|点心|饼干|糖果|巧克力|薯片', "9", "吃零食"),
    (r'不在座位上|离开|不在位置|不在画面中|看不到学生|离开座位|站起来|走动|无人', "10", "不在座位上")
]

# 学习行为语音提示
BEHAVIOR_VOICE_PROMPTS = {
    "1": "太棒了！同学正在认真学习，继续保持这种专注力。",
    "2": "同学注意力有些分散，建议调整一下思绪，重新集中注意力。",
    "3": "同学正在使用学习工具，很好，善用工具可以提高学习效率。",
    "4": "同学正在休息喝水，适当休息有助于恢复精力，别忘了之后继续学习哦。",
    "5": "注意，同学正在玩手机分心。请放下手机，回到学习状态。",
    "6": "同学正在与他人交流学习内容，良好的讨论可以加深理解。",
    "7": "同学似乎有些疲倦，趴在桌子上了。如果太累，建议站起来活动一下。",
    "8": "同学注意力分散在其他事情上，请尽快回到学习状态。",
    "9": "同学正在吃零食。请注意不要弄脏学习资料，吃完后记得清理桌面，继续专注学习。",
    "10": "同学目前不在座位上。请尽快回到座位继续学习，养成良好的学习习惯很重要。",
    "0": "未能判断同学的当前状态，请继续观察。"
}

# 欢迎信息和介绍信息
WELCOME_MESSAGE = "你好，我是睿课云眸，我会帮助你监督学习状态，保持良好的学习习惯。"
INTRO_MESSAGE = "睿课云眸已启动。我会通过摄像头观察你的学习状态，给予适当的提醒和鼓励。专注学习时，我会给予积极反馈；分心时，我会友善提醒。希望能帮助你养成良好的学习习惯！"

# 算能BM1684X TPU服务器配置
USE_TPU_SERVER = True  # 是否使用TPU服务器
TPU_SERVER_HOST = "***********"  # TPU服务器主机
QWEN_TPU_PORT = 18888  # 通义千问模型TPU端口
DEEPSEEK_TPU_PORT = 18889  # DeepSeek模型TPU端口

# DeepSeek API Configuration for Behavior Analysis
DEEPSEEK_API_KEY = "***********************************"  # 用于行为分析
DEEPSEEK_BASE_URL = "https://api.deepseek.com"

# DeepSeek Chat配置（用于问答聊天）- 新的API密钥
DEEPSEEK_CHAT_API_KEY = "***********************************"  # 用于问答聊天
DEEPSEEK_CHAT_BASE_URL = "https://api.deepseek.com"

# DeepSeek Chat API Configuration (for Q&A) - 更新配置
DEEPSEEK_NEW_CHAT_API_KEY = "***********************************"  # 新的DeepSeek API
DEEPSEEK_NEW_CHAT_BASE_URL = "https://api.deepseek.com"

# Qwen-VL API Configuration
QWEN_API_KEY = "sk-61cb842a339844c0916659e8e9f0c651"
QWEN_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"

# TTS Configuration
TTS_MODEL = "iic/speech_sambert-hifigan_tts_zh-cn_16k"
TTS_VOICE = "zhitian_emo"

# Audio Recording Configuration
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000
CHUNK = 1024
WAVE_OUTPUT_FILENAME = "output.wav"

# Logging Configuration
LOG_FILE = "behavior_log.txt"
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# ---------------- API Clients Initialization ----------------
# ===================== 增强的API客户端 =====================
class OptimizedAPIClient:
    """优化的API客户端，支持超时和重试"""
    
    def __init__(self, api_key, base_url, timeout=API_TIMEOUT):
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout,
            max_retries=RETRY_ATTEMPTS
        )
        self.timeout = timeout
        self.executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)
    
    @retry_on_failure(max_attempts=RETRY_ATTEMPTS, delay=0.5)
    def chat_completion_with_timeout(self, **kwargs):
        """带超时的聊天完成"""
        future = self.executor.submit(self.client.chat.completions.create, **kwargs)
        try:
            result = future.result(timeout=self.timeout)
            return result
        except FutureTimeoutError:
            future.cancel()
            raise TimeoutError(f"API调用超时（{self.timeout}秒）")

# 初始化DeepSeek客户端
try:
    # 检查是否使用TPU服务器并且TPU服务器可用
    use_tpu_for_deepseek = False
    if USE_TPU_SERVER:
        try:
            use_tpu_for_deepseek = check_tpu_server(TPU_SERVER_HOST, DEEPSEEK_TPU_PORT)
        except Exception as e:
            print(f"检查DeepSeek TPU服务器时出错: {e}")
            use_tpu_for_deepseek = False
    
    if use_tpu_for_deepseek:
        deepseek_tpu_base_url = f"http://{TPU_SERVER_HOST}:{DEEPSEEK_TPU_PORT}/v1"
        deepseek_client = OptimizedAPIClient(
            api_key="dummy_key",
            base_url=deepseek_tpu_base_url,
            timeout=API_TIMEOUT
        )
        print(f"成功连接DeepSeek TPU服务器: {deepseek_tpu_base_url}")
    else:
        # 如果TPU服务器不可用或未启用，则使用云API
        if USE_TPU_SERVER and not use_tpu_for_deepseek:
            print(f"警告: DeepSeek TPU服务器 {TPU_SERVER_HOST}:{DEEPSEEK_TPU_PORT} 不可用，切换到云API")
        deepseek_client = OptimizedAPIClient(
            api_key=DEEPSEEK_API_KEY,
            base_url=DEEPSEEK_BASE_URL,
            timeout=API_TIMEOUT
        )
        print(f"成功连接DeepSeek云API服务器")
except Exception as e:
    print(f"连接DeepSeek API失败: {e}")
    deepseek_client = None
    
# 初始化Qwen-VL客户端
try:
    # 检查是否使用TPU服务器并且TPU服务器可用
    use_tpu_for_qwen = False
    if USE_TPU_SERVER:
        try:
            use_tpu_for_qwen = check_tpu_server(TPU_SERVER_HOST, QWEN_TPU_PORT)
        except Exception as e:
            print(f"检查通义千问TPU服务器时出错: {e}")
            use_tpu_for_qwen = False
    
    if use_tpu_for_qwen:
        qwen_tpu_base_url = f"http://{TPU_SERVER_HOST}:{QWEN_TPU_PORT}/v1"
        qwen_client = OptimizedAPIClient(
            api_key="dummy_key",
            base_url=qwen_tpu_base_url,
            timeout=API_TIMEOUT
        )
        print(f"成功连接通义千问TPU服务器: {qwen_tpu_base_url}")
    else:
        # 如果TPU服务器不可用或未启用，则使用云API
        if USE_TPU_SERVER and not use_tpu_for_qwen:
            print(f"警告: 通义千问TPU服务器 {TPU_SERVER_HOST}:{QWEN_TPU_PORT} 不可用，切换到云API")
        qwen_client = OptimizedAPIClient(
            api_key=QWEN_API_KEY,
            base_url=QWEN_BASE_URL,
            timeout=API_TIMEOUT
        )
        print(f"成功连接通义千问云API服务器")
except Exception as e:
    error_msg = f"连接通义千问API失败: {e}"
    print(error_msg)
    qwen_client = None

# ---------------- Utility Functions ----------------
def extract_language_emotion_content(text):
    """Extract clean content from ASR output"""
    # Extract language
    language_start = text.find("|") + 1
    language_end = text.find("|", language_start)
    language = text[language_start:language_end]
    
    # Extract emotion
    emotion_start = text.find("|", language_end + 1) + 1
    emotion_end = text.find("|", emotion_start)
    emotion = text[emotion_start:emotion_end]
    
    # Extract content
    content_start = text.find(">", emotion_end) + 1
    content = text[content_start:]
    
    # Clean up any remaining tags
    while content.startswith("<|"):
        end_tag = content.find(">", 2) + 1
        content = content[end_tag:]
    
    return content.strip()

def get_memory_usage():
    """获取当前程序内存使用情况"""
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
        return memory_mb
    except Exception as e:
        print(f"获取内存信息失败: {e}")
        return 0

@handle_exceptions(default_return=0)
def perform_memory_cleanup():
    """执行内存清理操作"""
    try:
        # 主动调用垃圾回收
        collected = gc.collect()
        
        # 清理弱引用
        import weakref
        weakref.getweakrefs(object)
        
        memory_mb = get_memory_usage()
        print(f"内存清理完成: 回收了 {collected} 个对象, 当前内存使用: {memory_mb:.1f} MB")
        return memory_mb
        
    except Exception as e:
        print(f"内存清理失败: {e}")
        return 0

def check_tpu_server(host, port, timeout=2):
    """检查TPU服务器是否可用
    
    Args:
        host: 服务器主机名或IP
        port: 服务器端口
        timeout: 连接超时时间(秒)
        
    Returns:
        bool: 服务器是否可用
    """
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0  # 0表示端口开放
    except Exception as e:
        print(f"检查TPU服务器时出错: {e}")
        return False

def extract_behavior_type(analysis_text):
    """
    从分析文本中提取行为类型
    返回: (行为编号, 行为描述)
    改进版：修复【行为判断】格式解析和多行为优先级处理，增强文本编码处理
    """
    if not analysis_text:
        return "0", "未识别"

    # 预处理：确保文本编码正确
    try:
        # 确保文本是字符串类型
        if not isinstance(analysis_text, str):
            analysis_text = str(analysis_text)

        # 处理可能的字节序列
        if isinstance(analysis_text, bytes):
            analysis_text = analysis_text.decode('utf-8', errors='replace')

        # 清理文本，移除控制字符但保留中文
        import re
        analysis_text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', analysis_text)

        # 确保UTF-8编码
        analysis_text = analysis_text.encode('utf-8', errors='replace').decode('utf-8')
        analysis_text = analysis_text.strip()

    except Exception as e:
        print(f"⚠️ 行为分析文本编码处理失败: {e}")
        analysis_text = str(analysis_text).strip() if analysis_text else ""

    if not analysis_text:
        return "0", "未识别"

    # 第一步：尝试从【行为判断】格式中提取（最高优先级）
    behavior_num, behavior_desc = _extract_from_judgment_format(analysis_text)
    if behavior_num != "0":
        # 确保返回的描述也经过编码处理
        behavior_desc = _safe_encode_behavior_desc(behavior_desc)
        return behavior_num, behavior_desc

    # 第二步：使用关键词匹配和优先级判断
    behavior_num, behavior_desc = _extract_from_keywords_with_priority(analysis_text)
    if behavior_num != "0":
        behavior_desc = _safe_encode_behavior_desc(behavior_desc)
        return behavior_num, behavior_desc

    # 第三步：使用配置文件中定义的匹配模式
    for pattern, num, desc in BEHAVIOR_PATTERNS:
        if re.search(pattern, analysis_text, re.IGNORECASE):
            desc = _safe_encode_behavior_desc(desc)
            return num, desc

    return "0", "未识别"

def _safe_encode_behavior_desc(desc):
    """安全编码行为描述文本"""
    if not desc:
        return "未识别"

    try:
        # 确保是字符串
        if not isinstance(desc, str):
            desc = str(desc)

        # 处理字节序列
        if isinstance(desc, bytes):
            desc = desc.decode('utf-8', errors='replace')

        # 清理控制字符
        import re
        desc = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', desc)

        # 确保UTF-8编码
        desc = desc.encode('utf-8', errors='replace').decode('utf-8')
        desc = desc.strip()

        return desc if desc else "未识别"

    except Exception as e:
        print(f"⚠️ 行为描述编码处理失败: {e}")
        return str(desc) if desc else "未识别"

def _extract_from_judgment_format(analysis_text):
    """
    从【行为判断】格式中提取行为信息
    支持多种格式变体
    """
    # 改进的【行为判断】格式正则表达式
    judgment_patterns = [
        # 标准格式：【行为判断】：数字-描述
        r'【行为判断】\s*[：:]\s*(\d+)\s*[-\-]\s*([^，。\n【】]+)',
        # 简化格式：行为判断：数字-描述
        r'行为判断\s*[：:]\s*(\d+)\s*[-\-]\s*([^，。\n【】]+)',
        # 无冒号格式：【行为判断】数字-描述
        r'【行为判断】\s*(\d+)\s*[-\-]\s*([^，。\n【】]+)',
        # 宽松格式：包含数字-描述的任何地方
        r'(\d+)\s*[-\-]\s*(认真学习|轻度走神|使用学习工具|喝水休息|玩手机分心|与同学交流学习内容|睡觉或趴桌子|其他分心行为|吃零食|不在座位上)'
    ]

    for pattern in judgment_patterns:
        match = re.search(pattern, analysis_text)
        if match:
            behavior_num = match.group(1).strip()
            behavior_desc = match.group(2).strip()

            # 验证编号和描述是否匹配
            if behavior_num in BEHAVIOR_DESCRIPTIONS and BEHAVIOR_DESCRIPTIONS[behavior_num] == behavior_desc:
                return behavior_num, behavior_desc

            # 如果描述不匹配，但编号有效，使用正确的描述
            if behavior_num in BEHAVIOR_DESCRIPTIONS:
                return behavior_num, BEHAVIOR_DESCRIPTIONS[behavior_num]

    return "0", "未识别"

def _extract_from_keywords_with_priority(analysis_text):
    """
    使用关键词匹配和优先级判断提取行为
    处理多种行为同时出现的情况
    """
    # 计算每个行为的匹配分数
    behavior_scores = {}

    for num, keywords in BEHAVIOR_KEYWORDS.items():
        score = 0
        for keyword in keywords:
            if keyword in analysis_text:
                # 根据关键词重要性给予不同权重
                if keyword in ['玩手机', '手机', '玩游戏', '看手机', '偷偷看手机']:  # 高优先级：分心行为
                    score += 3
                elif keyword in ['睡觉', '趴着', '不在座位上', '离开', '趴桌', '睡着']:  # 高优先级：严重问题
                    score += 3
                elif keyword in ['尺子', '计算器', '工具', '测量', '文具']:  # 学习工具特定权重
                    score += 3
                elif keyword in ['吃零食', '零食', '吃东西', '包装袋']:  # 中等优先级
                    score += 2
                elif keyword in ['东张西望', '发呆', '分心', '注意力不集中', '注意力分散']:  # 中等优先级
                    score += 2
                elif keyword in ['嘴巴明显张开', '明显张嘴', '张嘴说话', '开口说话', '说话动作', '口型变化']:  # 高优先级：明显说话动作
                    score += 3
                elif keyword in ['嘴部动作明显', '嘴唇明显分开', '说话姿态', '交谈动作', '嘴巴在动']:  # 中等优先级：说话相关
                    score += 2
                elif keyword in ['认真学习', '专注', '学习', '作业']:  # 降低通用学习词汇权重
                    score += 1
                else:  # 普通权重
                    score += 1

        if score > 0:
            behavior_scores[num] = score

    # 特殊处理：检查是否为"没有明显行为"的情况
    no_behavior_keywords = ['没有明显行为', '无明显动作', '静坐', '保持静止', '无特殊行为', '正常坐着']
    for keyword in no_behavior_keywords:
        if keyword in analysis_text:
            # 如果明确提到没有明显行为，且没有其他强烈的行为指示，返回认真学习
            if not behavior_scores or max(behavior_scores.values()) <= 1:
                return "1", "认真学习"

    if not behavior_scores:
        return "0", "未识别"

    # 特殊处理"与他人交流"行为，简化判断条件，主要关注说话动作
    if "6" in behavior_scores:
        # 检查是否有明显的说话动作指示词（核心判断标准）
        speaking_action_keywords = [
            '嘴巴明显张开', '嘴巴张开', '明显张嘴', '张嘴说话', '开口说话',
            '说话动作', '口型变化', '嘴部动作明显', '嘴唇明显分开',
            '说话姿态', '交谈动作', '嘴巴在动', '口型明显', '明显说话',
            '嘴部有动作', '张口说话', '开口交流', '说话状态', '发声动作'
        ]
        has_speaking_action = any(keyword in analysis_text for keyword in speaking_action_keywords)

        # 检查是否有排除说话的指示词（非交流性嘴部动作）
        non_speaking_keywords = [
            '打哈欠', '哈欠', '轻微张嘴', '微微张开', '静态张嘴', '嘴唇微分',
            '喝水', '吃东西', '咀嚼', '吞咽', '深呼吸', '叹气', '嘴唇微张',
            '轻微开口', '静止状态', '无声张嘴', '嘴部静态', '微张状态',
            '轻微动作', '没有说话动作', '无说话动作', '静默', '安静',
            '轻微', '微微', '稍微', '略微', '轻度'
        ]
        has_non_speaking = any(keyword in analysis_text for keyword in non_speaking_keywords)

        # 简化的判断逻辑：主要基于说话动作
        # 1. 如果有非说话性的嘴部动作，排除交流行为
        if has_non_speaking:
            behavior_scores["6"] = 0
            print("🔍 行为优化：检测到非说话性嘴部动作（如打哈欠、喝水等），排除交流行为")

        # 2. 如果有明显的说话动作，确认为交流行为
        elif has_speaking_action:
            # 有说话动作的情况下，给予适当加分
            if behavior_scores["6"] >= 1:
                behavior_scores["6"] = min(5, behavior_scores["6"] + 2)  # 给予加分
                print(f"🔍 行为优化：检测到明显说话动作，确认交流行为，分数: {behavior_scores['6']}")
            else:
                behavior_scores["6"] = 2  # 设置基础分数
                print(f"🔍 行为优化：检测到说话动作，设置交流行为基础分数: {behavior_scores['6']}")

        # 3. 没有明显说话动作的情况，降低交流权重
        else:
            if behavior_scores["6"] <= 2:
                behavior_scores["6"] = 0  # 直接排除
                print("🔍 行为优化：缺乏明显说话动作，排除交流行为")
            else:
                behavior_scores["6"] = max(1, behavior_scores["6"] - 2)  # 降低权重
                print(f"🔍 行为优化：说话动作不明显，降低交流权重: {behavior_scores['6']}")

    # 找出得分最高的行为
    max_score = max(behavior_scores.values())
    best_behaviors = [num for num, score in behavior_scores.items() if score == max_score]

    # 如果有多个相同得分的行为，按优先级选择
    # 调整优先级：降低"与他人交流"的优先级，避免误判
    priority_order = ["5", "7", "10", "9", "8", "2", "4", "3", "6", "1"]  # 问题行为优先，交流行为降级

    for priority_num in priority_order:
        if priority_num in best_behaviors:
            return priority_num, BEHAVIOR_DESCRIPTIONS[priority_num]

    # 如果没有在优先级列表中，返回第一个
    selected_num = best_behaviors[0]
    return selected_num, BEHAVIOR_DESCRIPTIONS[selected_num]


def extract_posture_and_emotion_analysis(analysis_text):
    """
    从增强的AI分析文本中提取姿态和情绪信息
    返回结构化的姿态和情绪数据
    优化版本：提高解析性能
    """
    if not analysis_text or len(analysis_text.strip()) < 10:
        return None, None

    # 性能优化：预编译正则表达式
    if not hasattr(extract_posture_and_emotion_analysis, '_compiled_patterns'):
        extract_posture_and_emotion_analysis._compiled_patterns = {
            'posture': re.compile(r'【姿态描述】[：:]\s*(.*?)(?=【|$)', re.DOTALL),
            'emotion': re.compile(r'【情绪状态】[：:]\s*(.*?)(?=【|$)', re.DOTALL)
        }

    # 初始化结果字典
    posture_data = {
        "head_position": "未识别",
        "neck_status": "未识别",
        "shoulder_status": "未识别",
        "body_posture": "未识别",
        "overall_evaluation": "未识别",
        "raw_description": ""
    }

    emotion_data = {
        "primary_emotion": "未识别",
        "emotion_intensity": "未识别",
        "facial_expression": "未识别",
        "mental_state": "未识别",
        "raw_description": ""
    }

    try:
        # 使用预编译的正则表达式提取姿态描述部分
        patterns = extract_posture_and_emotion_analysis._compiled_patterns
        posture_match = patterns['posture'].search(analysis_text)
        if posture_match:
            posture_text = posture_match.group(1).strip()
            posture_data["raw_description"] = posture_text

            # 解析头部位置
            head_patterns = {
                "正直": r'头部.*?正直|正直.*?头部',
                "前倾": r'头部.*?前倾|前倾.*?头部',
                "后仰": r'头部.*?后仰|后仰.*?头部',
                "左偏": r'头部.*?左偏|左偏.*?头部',
                "右偏": r'头部.*?右偏|右偏.*?头部',
                "低头": r'低头|头部.*?向下',
                "抬头": r'抬头|头部.*?向上'
            }
            for position, pattern in head_patterns.items():
                if re.search(pattern, posture_text):
                    posture_data["head_position"] = position
                    break

            # 解析颈部状态
            neck_patterns = {
                "挺直": r'颈部.*?挺直|挺直.*?颈部',
                "前伸": r'颈部.*?前伸|前伸.*?颈部',
                "侧弯": r'颈部.*?侧弯|侧弯.*?颈部',
                "紧张": r'颈部.*?紧张|紧张.*?颈部',
                "放松": r'颈部.*?放松|放松.*?颈部'
            }
            for status, pattern in neck_patterns.items():
                if re.search(pattern, posture_text):
                    posture_data["neck_status"] = status
                    break

            # 解析肩膀状态 - 注意：高低不平要在平衡之前匹配
            shoulder_patterns = {
                "高低不平": r'肩膀.*?高低不平|高低不平.*?肩膀|肩膀.*?不平|不平.*?肩膀',
                "平衡": r'肩膀.*?平衡|平衡.*?肩膀',
                "耸肩": r'耸肩|肩膀.*?耸起',
                "含胸": r'含胸|肩膀.*?内收',
                "挺胸": r'挺胸|肩膀.*?展开'
            }
            for status, pattern in shoulder_patterns.items():
                if re.search(pattern, posture_text):
                    posture_data["shoulder_status"] = status
                    break

            # 解析身体姿势
            body_patterns = {
                "端正坐姿": r'端正坐姿|坐姿端正',
                "弯腰驼背": r'弯腰驼背|驼背|弯腰',
                "侧身": r'侧身|身体.*?侧向',
                "趴桌": r'趴桌|趴在桌子',
                "站立": r'站立|站着',
                "其他": r'其他姿势'
            }
            for posture, pattern in body_patterns.items():
                if re.search(pattern, posture_text):
                    posture_data["body_posture"] = posture
                    break

            # 解析整体评价
            evaluation_patterns = {
                "良好姿态": r'良好姿态|姿态良好',
                "轻微不良": r'轻微不良|稍有不良',
                "明显不良": r'明显不良|姿态不良',
                "需要纠正": r'需要纠正|需要调整'
            }
            for evaluation, pattern in evaluation_patterns.items():
                if re.search(pattern, posture_text):
                    posture_data["overall_evaluation"] = evaluation
                    break

        # 使用预编译的正则表达式提取情绪状态部分
        emotion_match = patterns['emotion'].search(analysis_text)
        if emotion_match:
            emotion_text = emotion_match.group(1).strip()
            emotion_data["raw_description"] = emotion_text

            # 解析主要情绪
            emotion_patterns = {
                "快乐": r'快乐|开心|愉悦|高兴',
                "平静": r'平静|安静|淡定|冷静',
                "专注": r'专注|集中|认真|投入',
                "疲惫": r'疲惫|疲劳|累|困倦',
                "焦虑": r'焦虑|紧张|担心|不安',
                "沮丧": r'沮丧|失落|低落|难过',
                "愤怒": r'愤怒|生气|恼怒|烦躁',
                "困惑": r'困惑|迷惑|不解|疑惑',
                "兴奋": r'兴奋|激动|活跃|精神',
                "无聊": r'无聊|乏味|厌倦|没兴趣'
            }
            for emotion, pattern in emotion_patterns.items():
                if re.search(pattern, emotion_text):
                    emotion_data["primary_emotion"] = emotion
                    break

            # 解析情绪强度
            intensity_patterns = {
                "轻微": r'轻微|稍微|一点',
                "中等": r'中等|适中|一般',
                "强烈": r'强烈|明显|很|非常'
            }
            for intensity, pattern in intensity_patterns.items():
                if re.search(pattern, emotion_text):
                    emotion_data["emotion_intensity"] = intensity
                    break

            # 解析面部表情
            expression_patterns = {
                "微笑": r'微笑|笑容|笑',
                "皱眉": r'皱眉|眉头紧锁',
                "专注": r'专注.*?表情|表情.*?专注',
                "疲惫": r'疲惫.*?表情|表情.*?疲惫',
                "困惑": r'困惑.*?表情|表情.*?困惑',
                "其他": r'其他.*?表情|表情.*?其他'
            }
            for expression, pattern in expression_patterns.items():
                if re.search(pattern, emotion_text):
                    emotion_data["facial_expression"] = expression
                    break

            # 解析精神状态
            mental_patterns = {
                "精神饱满": r'精神饱满|精神很好|状态很好',
                "一般": r'精神.*?一般|状态.*?一般',
                "疲惫": r'精神.*?疲惫|状态.*?疲惫',
                "困倦": r'精神.*?困倦|状态.*?困倦|犯困'
            }
            for mental, pattern in mental_patterns.items():
                if re.search(pattern, emotion_text):
                    emotion_data["mental_state"] = mental
                    break

        return posture_data, emotion_data

    except Exception as e:
        print(f"解析姿态和情绪数据时出错: {e}")
        return posture_data, emotion_data


def generate_posture_emotion_interventions(posture_data, emotion_data):
    """
    基于姿态和情绪数据生成干预建议
    返回文本提醒和语音提醒
    优化版本：添加缓存机制提高性能
    """
    # 性能优化：如果数据为空，直接返回
    if not posture_data and not emotion_data:
        return [], []

    text_reminders = []
    voice_reminders = []

    # 姿态干预建议
    if posture_data:
        head_position = posture_data.get("head_position", "")
        neck_status = posture_data.get("neck_status", "")
        shoulder_status = posture_data.get("shoulder_status", "")
        body_posture = posture_data.get("body_posture", "")
        overall_evaluation = posture_data.get("overall_evaluation", "")

        # 头部位置纠正
        if head_position in ["前倾", "低头"]:
            text_reminders.append("💡 姿态提醒：请抬起头部，保持颈部挺直")
            voice_reminders.append("注意姿态，请抬起头部，避免长时间低头，这样有助于保护颈椎健康")
        elif head_position == "后仰":
            text_reminders.append("💡 姿态提醒：头部过度后仰，请调整到自然位置")
            voice_reminders.append("请调整头部位置，避免过度后仰，保持自然舒适的姿态")

        # 颈部状态纠正
        if neck_status == "前伸":
            text_reminders.append("💡 姿态提醒：颈部前伸，请向后收回保持挺直")
            voice_reminders.append("颈部姿态需要调整，请将颈部向后收回，保持挺直状态")
        elif neck_status == "紧张":
            text_reminders.append("💡 姿态提醒：颈部紧张，建议适当放松")
            voice_reminders.append("检测到颈部紧张，建议做一些颈部放松运动，缓解疲劳")

        # 肩膀状态纠正
        if shoulder_status == "高低不平":
            text_reminders.append("💡 姿态提醒：肩膀高低不平，请调整到平衡状态")
            voice_reminders.append("肩膀高低不平，请调整坐姿，让两边肩膀保持平衡")
        elif shoulder_status == "耸肩":
            text_reminders.append("💡 姿态提醒：肩膀耸起，请放松下沉")
            voice_reminders.append("肩膀过于紧张，请放松肩膀，让它们自然下沉")
        elif shoulder_status == "含胸":
            text_reminders.append("💡 姿态提醒：含胸驼背，请挺胸展肩")
            voice_reminders.append("检测到含胸姿态，请挺起胸部，展开肩膀，保持良好体态")

        # 身体姿势纠正
        if body_posture == "弯腰驼背":
            text_reminders.append("💡 姿态提醒：弯腰驼背，请挺直腰背")
            voice_reminders.append("发现弯腰驼背，请挺直腰背，保持端正的坐姿，这样有利于脊椎健康")
        elif body_posture == "趴桌":
            text_reminders.append("💡 姿态提醒：请不要趴在桌子上，坐直身体")
            voice_reminders.append("请不要趴在桌子上学习，这样对脊椎不好，请坐直身体")
        elif body_posture == "侧身":
            text_reminders.append("💡 姿态提醒：身体侧向，请面向正前方")
            voice_reminders.append("身体姿势偏向一侧，请调整到面向正前方的位置")

        # 整体评价建议
        if overall_evaluation == "明显不良":
            text_reminders.append("⚠️ 姿态警告：姿态明显不良，请立即调整")
            voice_reminders.append("您的坐姿需要立即调整，长期不良姿态会影响身体健康，请保持端正坐姿")
        elif overall_evaluation == "需要纠正":
            text_reminders.append("💡 姿态建议：姿态需要纠正，请注意调整")
            voice_reminders.append("建议调整您的坐姿，保持头正、肩平、背直的良好姿态")

    # 情绪干预建议
    if emotion_data:
        primary_emotion = emotion_data.get("primary_emotion", "")
        emotion_intensity = emotion_data.get("emotion_intensity", "")
        mental_state = emotion_data.get("mental_state", "")

        # 负面情绪支持
        if primary_emotion == "疲惫":
            if emotion_intensity == "强烈":
                text_reminders.append("😴 情绪关怀：您看起来很疲惫，建议休息一下")
                voice_reminders.append("您看起来很疲惫，建议休息5-10分钟，或者做一些眼部运动来缓解疲劳")
            else:
                text_reminders.append("😌 情绪关怀：有点疲惫，注意劳逸结合")
                voice_reminders.append("检测到您有些疲惫，记得适当休息，保持良好的学习状态")

        elif primary_emotion == "焦虑":
            text_reminders.append("😰 情绪关怀：感到焦虑时，深呼吸可以帮助放松")
            voice_reminders.append("检测到您有些焦虑，建议深呼吸放松，学习要循序渐进，不要给自己太大压力")

        elif primary_emotion == "沮丧":
            text_reminders.append("😔 情绪关怀：遇到困难很正常，坚持就是胜利")
            voice_reminders.append("学习中遇到困难是正常的，请不要沮丧，坚持下去就会有收获")

        elif primary_emotion == "愤怒":
            text_reminders.append("😤 情绪关怀：情绪激动时，先冷静一下再继续")
            voice_reminders.append("检测到您情绪有些激动，建议先冷静一下，平和的心态更有利于学习")

        elif primary_emotion == "困惑":
            text_reminders.append("🤔 情绪关怀：遇到困惑可以寻求帮助")
            voice_reminders.append("学习中有困惑是正常的，可以向老师或同学寻求帮助，或者换个角度思考问题")

        elif primary_emotion == "无聊":
            text_reminders.append("😑 情绪关怀：学习感到无聊时，可以调整学习方法")
            voice_reminders.append("学习感到无聊时，可以尝试不同的学习方法，或者设定小目标来增加成就感")

        # 正面情绪鼓励
        elif primary_emotion == "快乐":
            text_reminders.append("😊 情绪鼓励：保持愉快的心情，学习效果会更好")
            voice_reminders.append("很高兴看到您心情愉快，保持这种积极的状态，学习效果会更好")

        elif primary_emotion == "专注":
            text_reminders.append("🎯 情绪鼓励：专注状态很棒，继续保持")
            voice_reminders.append("您现在的专注状态很棒，继续保持这种投入的学习态度")

        elif primary_emotion == "兴奋":
            text_reminders.append("🎉 情绪鼓励：学习热情很高，很棒！")
            voice_reminders.append("您的学习热情很高，这种积极的态度值得鼓励，继续加油")

        # 精神状态建议
        if mental_state == "困倦":
            text_reminders.append("😴 状态提醒：精神困倦，建议适当休息")
            voice_reminders.append("您看起来有些困倦，建议休息一下或者做一些提神的活动")
        elif mental_state == "疲惫":
            text_reminders.append("😓 状态提醒：精神疲惫，注意调节")
            voice_reminders.append("精神状态有些疲惫，建议调整学习节奏，适当放松")

    return text_reminders, voice_reminders

# ---------------- Camera Display Window ----------------
class CameraWindow:
    def __init__(self, app):
        self.app = app
        self.is_closed = False
        self.current_image = None
        # 新UI使用preview_label直接显示，不需要单独窗口
    
    def update_frame(self, img):
        """更新摄像头画面 - 使用主界面的update_preview方法以保持一致性"""
        if self.is_closed:
            return
            
        try:
            if img and hasattr(self.app, 'update_preview'):
                # 直接调用主界面的update_preview方法，这样可以使用我们优化过的尺寸和缩放逻辑
                self.app.update_preview(img)
                    
        except Exception as e:
            print(f"更新摄像头画面时出错: {e}")
    
    def on_closing(self):
        """处理窗口关闭事件"""
        self.is_closed = True

# ---------------- Core Functionality Classes ----------------
class AudioRecorder:
    def __init__(self, app):
        self.app = app
        self.recording = False
        self.stop_recording_flag = False
        self.audio_thread = None
        
    def start_recording(self):
        """Begin audio recording when 'r' key is pressed"""
        if not self.recording:
            self.recording = True
            self.stop_recording_flag = False
            self.audio_thread = threading.Thread(target=self._record_audio)
            self.audio_thread.daemon = True
            self.audio_thread.start()
            self.app.update_status("Recording...")
    
    def stop_recording(self):
        """Stop audio recording when 's' key is pressed"""
        if self.recording:
            self.stop_recording_flag = True
            self.recording = False
            if self.audio_thread and self.audio_thread.is_alive():
                self.audio_thread.join(timeout=1.0)
            self.app.update_status("Processing audio...")
    
    def _record_audio(self):
        """Record audio from microphone"""
        p = pyaudio.PyAudio()
        stream = p.open(format=FORMAT,
                      channels=CHANNELS,
                      rate=RATE,
                      input=True,
                      frames_per_buffer=CHUNK)
        
        frames = []
        
        while self.recording and not self.stop_recording_flag:
            try:
                data = stream.read(CHUNK)
                frames.append(data)
            except Exception as e:
                self.app.update_status(f"Error recording audio: {e}")
                break
        
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        if frames:
            try:
                wf = wave.open(WAVE_OUTPUT_FILENAME, 'wb')
                wf.setnchannels(CHANNELS)
                wf.setsampwidth(p.get_sample_size(FORMAT))
                wf.setframerate(RATE)
                wf.writeframes(b''.join(frames))
                wf.close()
                
                self.app.transcribe_audio(WAVE_OUTPUT_FILENAME)
            except Exception as e:
                self.app.update_status(f"Error saving audio: {e}")

class VoiceActivityDetector:
    def __init__(self, app):
        self.app = app
        self.running = False
        self.listening_thread = None
        self.detection_thread = None
        
        # Voice activity detection parameters - MUCH lower threshold
        self.energy_threshold = 80  # Further reduced for better sensitivity
        self.dynamic_threshold = True  # Dynamically adjust threshold based on environment noise
        self.silence_threshold = 0.8  # Seconds of silence to consider speech ended
        self.min_speech_duration = 0.3  # Shorter minimum duration to catch brief utterances
        self.max_speech_duration = 30.0  # Maximum speech duration
        
        # Speech detection state
        self.is_speaking = False
        self.speech_started = 0
        self.silence_started = 0
        self.speech_frames = []
        
        # For dynamic threshold adjustment
        self.noise_levels = []
        self.max_noise_levels = 100
        
        # Audio stream
        self.audio = None
        self.stream = None
        
        # Debug mode
        self.debug = True  # Set to True to enable energy level debugging
        
        # Add a calibration phase
        self.is_calibrating = True
        self.calibration_duration = 3  # seconds
        self.calibration_start_time = 0
    
    def start_monitoring(self):
        """Begin continuous voice monitoring"""
        if not self.running:
            self.running = True
            self.listening_thread = threading.Thread(target=self._monitor_audio)
            self.listening_thread.daemon = True
            self.listening_thread.start()
            self.app.update_status("语音监测启动中... 正在校准麦克风")
    
    def stop_monitoring(self):
        """Stop voice monitoring"""
        self.running = False
        if self.listening_thread and self.listening_thread.is_alive():
            self.listening_thread.join(timeout=1.0)
        if self.audio and self.stream:
            self.stream.stop_stream()
            self.stream.close()
            self.audio.terminate()
            self.audio = None
            self.stream = None
    
    def _get_energy(self, audio_data):
        """Calculate audio energy level"""
        try:
            # Convert bytes to numpy array
            data = np.frombuffer(audio_data, dtype=np.int16)
            
            # Ensure we have valid data
            if len(data) == 0 or np.all(data == 0):
                return 0.0
                
            # Calculate RMS energy
            # Use np.mean(np.abs(data)) as it's more robust than squaring
            energy = np.mean(np.abs(data))
            return energy
        except Exception as e:
            print(f"Error calculating energy: {e}")
            return 0.0
    
    def _is_speech(self, audio_data, energy=None):
        """Detect if audio chunk contains speech based on energy level"""
        try:
            # Skip speech detection if audio is playing
            if hasattr(self.app, 'is_playing_audio') and self.app.is_playing_audio:
                if self.debug and time.time() % 2 < 0.1:
                    print("语音监测暂停中 - 正在播放系统语音")
                return False
            
            # Use provided energy or calculate it
            if energy is None:
                energy = self._get_energy(audio_data)
            
            # If we're calibrating, just collect noise levels
            if self.is_calibrating:
                self.noise_levels.append(energy)
                return False
            
            # Adjust threshold dynamically if enabled
            threshold = self.energy_threshold
            if self.dynamic_threshold and len(self.noise_levels) > 0:
                # Set threshold to be 2.5x the average noise level
                noise_avg = sum(self.noise_levels) / len(self.noise_levels)
                dynamic_threshold = noise_avg * 2.5
                threshold = max(threshold, dynamic_threshold)
            
            # Debug output for energy levels
            if self.debug and time.time() % 1 < 0.1:  # Print every second
                print(f"能量: {energy:.1f}, 阈值: {threshold:.1f}, " + 
                      f"平均噪音: {sum(self.noise_levels) / max(1, len(self.noise_levels)):.1f}")
            
            # Detect speech when energy is above threshold
            return energy > threshold
        except Exception as e:
            print(f"Error in speech detection: {e}")
            return False
    
    def _calibrate_microphone(self):
        """Calibrate microphone by measuring background noise"""
        try:
            self.calibration_start_time = time.time()
            self.is_calibrating = True
            self.noise_levels = []
            
            print("开始麦克风校准...")
            self.app.update_status("校准麦克风中，请保持安静...")
            
            # Wait for calibration to complete
            while self.is_calibrating and time.time() - self.calibration_start_time < self.calibration_duration:
                time.sleep(0.1)
            
            # Calculate noise threshold
            if len(self.noise_levels) > 0:
                avg_noise = sum(self.noise_levels) / len(self.noise_levels)
                self.energy_threshold = max(100, avg_noise * 2.5)  # Set threshold to 2.5x average noise
                
                print(f"麦克风校准完成: 平均噪音级别 {avg_noise:.1f}, 阈值设为 {self.energy_threshold:.1f}")
                self.app.update_status(f"语音监测已启动 (阈值: {self.energy_threshold:.1f})")
            else:
                print("校准失败: 没有收集到噪音样本")
                self.app.update_status("语音监测已启动，但校准失败")
            
            self.is_calibrating = False
        except Exception as e:
            print(f"麦克风校准错误: {e}")
            self.is_calibrating = False
            self.app.update_status("语音监测已启动，但校准出错")
    
    def _monitor_audio(self):
        """Continuously monitor audio for speech"""
        try:
            self.audio = pyaudio.PyAudio()
            self.stream = self.audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                frames_per_buffer=CHUNK
            )
            
            # Perform initial calibration
            self._calibrate_microphone()
            
            # Continuous audio analysis loop
            while self.running:
                try:
                    # Read audio chunk
                    audio_data = self.stream.read(CHUNK, exception_on_overflow=False)
                    
                    # Calculate energy once to avoid duplicate work
                    energy = self._get_energy(audio_data)
                    
                    # Update noise level (only when not speaking)
                    if not self.is_speaking and len(self.noise_levels) < self.max_noise_levels:
                        self.noise_levels.append(energy)
                        if len(self.noise_levels) > self.max_noise_levels:
                            self.noise_levels.pop(0)  # Keep the list size limited
                    
                    # Check if it's speech
                    if self._is_speech(audio_data, energy):
                        # If we weren't already speaking, mark the start
                        if not self.is_speaking:
                            self.is_speaking = True
                            self.speech_started = time.time()
                            self.speech_frames = []
                            # Show visual feedback immediately
                            print("语音开始检测中...")
                            self.app.after(0, lambda: self.app.update_status("检测到语音输入..."))
                        
                        # Reset silence counter
                        self.silence_started = 0
                        
                        # Add frame to speech buffer
                        self.speech_frames.append(audio_data)
                        
                        # Check if we've exceeded max duration
                        if time.time() - self.speech_started > self.max_speech_duration:
                            print(f"达到最大语音长度 ({self.max_speech_duration}s)，开始处理")
                            self._process_speech()
                    
                    elif self.is_speaking:
                        # If we were speaking, but now detected silence
                        if self.silence_started == 0:
                            self.silence_started = time.time()
                            print(f"检测到语音之后的静音")
                        
                        # Add the silent frame (for smoother audio)
                        self.speech_frames.append(audio_data)
                        
                        # If silence continues for threshold duration, process the speech
                        silence_duration = time.time() - self.silence_started
                        if silence_duration > self.silence_threshold:
                            print(f"静音时长达到阈值 ({silence_duration:.2f}s > {self.silence_threshold}s)，开始处理语音")
                            self._process_speech()
                    
                    time.sleep(0.01)  # Small sleep to reduce CPU usage
                    
                except Exception as e:
                    error_msg = f"音频监测错误: {e}"
                    print(error_msg)
                    self.app.update_status(error_msg)
                    time.sleep(0.5)  # Sleep before retry
                    
        except Exception as e:
            error_msg = f"语音监测失败: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
        finally:
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            if self.audio:
                self.audio.terminate()
    
    def _process_speech(self):
        """Process detected speech segment"""
        speech_duration = time.time() - self.speech_started
        
        # Only process if speech is long enough and has frames
        if speech_duration >= self.min_speech_duration and len(self.speech_frames) > 0:
            print(f"处理语音片段: {speech_duration:.2f}秒, {len(self.speech_frames)} 帧")
            
            # Reset speech state
            is_speaking_was = self.is_speaking
            self.is_speaking = False
            self.silence_started = 0
            
            # Save a copy of speech frames before resetting
            frames_copy = self.speech_frames.copy()
            self.speech_frames = []
            
            # Check if we truly had meaningful speech
            if is_speaking_was and speech_duration > 0.5:  # Additional validation
                # Process in a separate thread to not block monitoring
                self.detection_thread = threading.Thread(
                    target=self._save_and_transcribe, 
                    args=(frames_copy,)
                )
                self.detection_thread.daemon = True
                self.detection_thread.start()
            else:
                print(f"语音太短或者无效: {speech_duration:.2f}秒")
                self.app.update_status("Ready")
        else:
            # Too short, reset without processing
            print(f"语音太短 ({speech_duration:.2f}秒 < {self.min_speech_duration}秒)，忽略")
            self.is_speaking = False
            self.silence_started = 0
            self.speech_frames = []
            self.app.update_status("Ready")
    
    def _save_and_transcribe(self, frames):
        """Save speech frames to file and start transcription"""
        try:
            temp_filename = f"speech_{int(time.time())}.wav"
            print(f"保存语音到 {temp_filename}")
            
            # Ensure the audio object exists
            if not self.audio:
                print("错误: 音频对象不存在，无法保存语音")
                return
            
            # Check if we have frames
            if not frames or len(frames) == 0:
                print("错误: 没有语音帧可以保存")
                return
            
            # Save frames to WAV file
            wf = wave.open(temp_filename, 'wb')
            wf.setnchannels(CHANNELS)
            wf.setsampwidth(self.audio.get_sample_size(FORMAT))
            wf.setframerate(RATE)
            wf.writeframes(b''.join(frames))
            wf.close()
            
            # Verify the file was saved
            if os.path.exists(temp_filename) and os.path.getsize(temp_filename) > 0:
                print(f"语音文件已保存: {temp_filename}, 大小: {os.path.getsize(temp_filename)} 字节")
            else:
                print(f"保存语音文件失败: {temp_filename}")
                return
            
            # 不再创建占位符，直接发送进行转录
            # 确保UI响应完成后再进入繁重的语音处理
            self.app.after(100, lambda: self._send_for_transcription(temp_filename))
            
        except Exception as e:
            error_msg = f"处理语音出错: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
    
    def _send_for_transcription(self, audio_file):
        """Send audio file for transcription after UI is updated"""
        try:
            print(f"发送语音文件进行转写: {audio_file}")
            # Send for transcription - without placeholder ID
            self.app.transcribe_audio(audio_file, priority=True)
        except Exception as e:
            error_msg = f"发送转写请求时出错: {e}"
            print(error_msg)
            self.app.update_status(error_msg)

class WebcamHandler:
    def __init__(self, app):
        self.app = app
        self.running = False
        self.paused = False  # Flag to indicate if analysis is paused
        self.processing = False  # Flag to indicate if analysis is in progress
        self.cap = None
        self.webcam_thread = None
        self.last_webcam_image = None  # Store the most recent webcam image
        self.debug = True  # Set to True to enable debugging output
        
        # 摄像头模拟模式
        self.simulation_mode = False  # 是否使用模拟模式
        self.simulation_images = []  # 模拟的图像列表
        
        # Sequential processing control
        self.analysis_running = False
        
        # Camera window
        self.camera_window = None
    
    def start(self):
        """Start webcam capture process"""
        if not self.running:
            try:
                print("尝试打开摄像头...")
                self.app.update_status("正在打开摄像头...")
                
                # 尝试多次初始化摄像头
                max_attempts = 3
                for attempt in range(max_attempts):
                    print(f"摄像头初始化尝试 {attempt+1}/{max_attempts}")
                    
                    # 在每次尝试前释放之前可能的资源
                    if hasattr(self, 'cap') and self.cap is not None:
                        try:
                            self.cap.release()
                            print("释放之前的摄像头资源")
                        except:
                            pass
                    
                    # 方法1: 尝试标准方式打开摄像头
                    try:
                        print("尝试标准方式打开摄像头...")
                        time.sleep(0.5)  # 在尝试前增加延迟
                        self.cap = cv2.VideoCapture(0)
                        if self.cap.isOpened():
                            print("标准方式成功")
                            break
                    except Exception as e:
                        print(f"标准方式失败: {e}")
                    
                    # 方法2: 尝试使用AVFoundation后端 (macOS专用)
                    try:
                        print("尝试使用AVFoundation打开摄像头...")
                        time.sleep(0.5)  # 在尝试前增加延迟
                        self.cap = cv2.VideoCapture(0, cv2.CAP_AVFOUNDATION)
                        if self.cap.isOpened():
                            print("AVFoundation方式成功")
                            break
                    except Exception as e:
                        print(f"AVFoundation尝试失败: {e}")
                    
                    # 方法3: 尝试使用不同的设备ID
                    try:
                        print("尝试使用设备ID 1...")
                        time.sleep(0.5)  # 在尝试前增加延迟
                        self.cap = cv2.VideoCapture(1)
                        if self.cap.isOpened():
                            print("设备ID 1方式成功")
                            break
                    except Exception as e:
                        print(f"设备ID 1尝试失败: {e}")
                    
                    time.sleep(1)  # 等待一秒后重试
                
                # 最后检查是否成功
                if not self.cap or not self.cap.isOpened():
                    print("所有方法都失败，切换到模拟模式")
                    self._initialize_simulation_mode()
                    return True  # 使用模拟模式仍返回成功
                
                # 设置摄像头属性，提高稳定性
                try:
                    # 降低分辨率和帧率以减轻资源负担，防止内存溢出
                    self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 480)  # 进一步降低分辨率
                    self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
                    # 设置更小的缓冲区减少内存使用
                    self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    # 大幅降低帧率，减少处理负担
                    self.cap.set(cv2.CAP_PROP_FPS, 8)  # 从15降到8FPS
                    # 使用MJPG编码减少处理负担
                    try:
                        self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter.fourcc(*'MJPG'))
                    except:
                        # 如果FOURCC设置失败，继续执行
                        pass
                except Exception as e:
                    print(f"设置摄像头属性时出错: {e}")
                
                # 读取一帧测试是否真的可用
                ret, test_frame = False, None
                for test_attempt in range(3):  # 尝试3次读取
                    try:
                        ret, test_frame = self.cap.read()
                        if ret and test_frame is not None and test_frame.size > 0:
                            print(f"成功读取摄像头帧，尺寸: {test_frame.shape}")
                            break
                        print(f"读取帧尝试 {test_attempt+1}/3 失败，重试...")
                        time.sleep(0.5)
                    except Exception as e:
                        print(f"测试帧读取出错: {e}")
                        time.sleep(0.5)
                
                # 释放测试帧资源
                if test_frame is not None:
                    del test_frame
                
                if not ret:
                    print("摄像头连接成功但无法获取图像，切换到模拟模式")
                    self.cap.release()
                    self._initialize_simulation_mode()
                    return True  # 使用模拟模式仍返回成功
                
                print("摄像头已成功打开并可以获取图像")
                self.running = True
                self.simulation_mode = False
                
                # Create the camera window
                self.create_camera_window()
                
                # Start processing thread
                self.webcam_thread = threading.Thread(target=self._process_webcam)
                self.webcam_thread.daemon = True
                self.webcam_thread.start()
                
                # Start analysis (important - this kicks off the first capture)
                self.analysis_running = True
                
                # Start first analysis after a longer delay to improve performance
                self.app.after(10000, self.trigger_next_capture)  # 增加到10秒延迟，减少分析频率
                
                return True
            except Exception as e:
                error_msg = f"启动摄像头时出错: {e}"
                print(error_msg)
                self.app.update_status(error_msg)
                if hasattr(self, 'cap') and self.cap is not None:
                    try:
                        self.cap.release()
                    except:
                        pass
                
                # 出错也切换到模拟模式
                print("摄像头初始化出错，切换到模拟模式")
                self._initialize_simulation_mode()
                return True  # 使用模拟模式仍返回成功
        return False
    
    def _initialize_simulation_mode(self):
        """初始化摄像头模拟模式"""
        self.simulation_mode = True
        self.running = True
        
        # 创建一个彩色条纹的模拟图像
        width, height = 640, 480
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 绘制彩色条纹
        stripe_width = width // 7
        colors = [
            (255, 0, 0),    # 红
            (255, 127, 0),  # 橙
            (255, 255, 0),  # 黄
            (0, 255, 0),    # 绿
            (0, 0, 255),    # 蓝
            (75, 0, 130),   # 靛
            (148, 0, 211)   # 紫
        ]
        
        for i, color in enumerate(colors):
            start_x = i * stripe_width
            end_x = (i + 1) * stripe_width if i < 6 else width
            image[:, start_x:end_x] = color
            
        # 添加文本说明
        font = cv2.FONT_HERSHEY_SIMPLEX
        text = "模拟摄像头模式"
        font_scale = 1.5
        font_thickness = 2
        text_size = cv2.getTextSize(text, font, font_scale, font_thickness)[0]
        
        # 文本位置 (居中)
        text_x = (width - text_size[0]) // 2
        text_y = (height + text_size[1]) // 2
        
        # 添加背景矩形使文字更清晰
        cv2.rectangle(image, 
                    (text_x - 10, text_y - text_size[1] - 10),
                    (text_x + text_size[0] + 10, text_y + 10),
                    (0, 0, 0), 
                    -1)  # 填充矩形
                    
        cv2.putText(image, text, (text_x, text_y), font, font_scale, (255, 255, 255), font_thickness)
        
        # 转换为PIL图像
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(rgb_image)
        
        # 保存为模拟图像
        self.simulation_images = [pil_image]
        self.last_webcam_image = pil_image
        
        print("已初始化模拟摄像头模式")
        self.app.update_status("使用模拟摄像头模式 - 部分功能可用")
        
        # 创建摄像头窗口
        self.create_camera_window()
        
        # 开始处理线程
        self.webcam_thread = threading.Thread(target=self._process_simulated_webcam)
        self.webcam_thread.daemon = True
        self.webcam_thread.start()
        
        # 开始分析
        self.analysis_running = True
        
        # 延迟开始第一次分析，延长到10秒减少系统负担
        self.app.after(10000, self.trigger_next_capture)
    
    def _process_simulated_webcam(self):
        """模拟摄像头处理线程"""
        last_ui_update_time = 0
        ui_update_interval = 0.05  # 20fps
        
        # 生成不同的模拟图像供分析使用
        self._generate_simulation_images()
        
        while self.running:
            try:
                current_time = time.time()
                
                # 更新摄像头窗口
                if self.camera_window and not self.camera_window.is_closed and current_time - last_ui_update_time >= ui_update_interval:
                    # 随机选择一张模拟图像
                    img_index = int(current_time % len(self.simulation_images))
                    img = self.simulation_images[img_index]
                    
                    self.camera_window.update_frame(img)
                    self.last_webcam_image = img
                    last_ui_update_time = current_time
                
                time.sleep(0.05)
            except Exception as e:
                print(f"模拟摄像头处理出错: {e}")
                time.sleep(1)
    
    def _generate_simulation_images(self):
        """生成多张模拟图像用于分析测试"""
        if len(self.simulation_images) <= 1:
            base_img = self.simulation_images[0]
            width, height = base_img.size
            
            # 创建几个不同的模拟场景
            for i in range(3):
                # 复制基础图像
                new_img = base_img.copy()
                draw = ImageDraw.Draw(new_img)
                
                # 添加不同的元素
                scenarios = ["认真学习", "轻度走神", "玩手机分心"]
                
                # 添加文字说明场景
                text = f"模拟场景: {scenarios[i]}"
                font_size = 30
                try:
                    # 尝试加载系统字体
                    font = ImageFont.truetype("Arial", font_size)
                except:
                    # 如果无法加载，使用默认字体
                    font = ImageFont.load_default()
                
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_x = (width - text_width) // 2
                
                # 添加背景矩形
                draw.rectangle(
                    [text_x - 10, 70, text_x + text_width + 10, 70 + font_size + 20],
                    fill=(0, 0, 0)
                )
                
                # 添加文字
                draw.text((text_x, 80), text, font=font, fill=(255, 255, 255))
                
                self.simulation_images.append(new_img)
        
        print(f"生成了 {len(self.simulation_images)} 张模拟图像")
    
    def _process_webcam(self):
        """Main webcam processing loop - just keeps the most recent frame"""
        last_ui_update_time = 0
        ui_update_interval = 0.05  # Update UI at 20 fps
        frame_count = 0  # 帧计数器，用于控制内存清理频率
        consecutive_errors = 0  # 新增：连续错误计数
        
        while self.running and not self.simulation_mode:
            try:
                if not self.cap or not self.cap.isOpened():
                    print("摄像头已关闭，尝试重新初始化...")
                    # 如果摄像头断开，尝试重新初始化一次
                    try:
                        if self.cap:
                            self.cap.release()  # 确保先释放资源
                        time.sleep(0.5)  # 添加延迟确保资源释放
                        self.cap = cv2.VideoCapture(0)
                        if not self.cap.isOpened():
                            print("重新初始化失败，切换到模拟模式")
                            self._initialize_simulation_mode()
                            return
                        consecutive_errors = 0  # 重置错误计数
                    except Exception as e:
                        print(f"重新初始化出错: {e}")
                        consecutive_errors += 1
                        if consecutive_errors > 3:
                            print("连续多次初始化失败，切换到模拟模式")
                            self._initialize_simulation_mode()
                            return
                        time.sleep(1)  # 延迟后再尝试
                        continue
                
                # 使用try-except保护帧获取过程
                try:
                    ret, frame = self.cap.read()
                except Exception as e:
                    print(f"读取帧时异常: {e}")
                    time.sleep(0.2)
                    consecutive_errors += 1
                    if consecutive_errors > 5:
                        print("连续多次读取帧失败，切换到模拟模式")
                        self._initialize_simulation_mode()
                        return
                    continue
                
                if not ret or frame is None:
                    print("读取帧失败，可能是暂时性问题，重试...")
                    time.sleep(0.2)
                    consecutive_errors += 1
                    if consecutive_errors > 5:
                        print("连续多次获取空帧，切换到模拟模式")
                        self._initialize_simulation_mode()
                        return
                    continue
                
                # 成功读取帧，重置错误计数
                consecutive_errors = 0
                
                # 转换为PIL图像前确保帧有效
                if frame.size == 0 or frame.shape[0] == 0 or frame.shape[1] == 0:
                    print("获取到无效帧（尺寸为0），跳过处理")
                    time.sleep(0.1)
                    continue
                
                # 安全转换图像格式
                try:
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    img = Image.fromarray(frame_rgb)
                except Exception as e:
                    print(f"图像转换错误: {e}")
                    del frame  # 确保释放资源
                    time.sleep(0.1)
                    continue
                
                # 释放原始帧内存
                del frame
                del frame_rgb
                
                # 安全更新图像引用
                old_img = None
                if self.last_webcam_image is not None:
                    old_img = self.last_webcam_image
                    self.last_webcam_image = None  # 先清空引用
                    
                self.last_webcam_image = img
                
                # 确保在复制后再释放旧图像
                if old_img is not None:
                    del old_img
                
                # Update camera window with the current frame
                current_time = time.time()
                if self.camera_window and not self.camera_window.is_closed and current_time - last_ui_update_time >= ui_update_interval:
                    try:
                        # 创建一个新的副本以防止引用问题
                        display_img = img.copy()
                        self.camera_window.update_frame(display_img)
                        last_ui_update_time = current_time
                    except Exception as e:
                        print(f"更新相机窗口错误: {e}")
                
                # 每处理100帧执行一次内存清理
                frame_count += 1
                if frame_count >= 100:
                    gc.collect()
                    frame_count = 0
                
                time.sleep(0.03)  # ~30 fps for capture
            except Exception as e:
                error_msg = f"Webcam error: {e}"
                print(error_msg)
                self.app.update_status(error_msg)
                
                consecutive_errors += 1
                if consecutive_errors > 5:
                    print("连续多次处理错误，切换到模拟模式")
                    # 确保释放摄像头资源
                    if self.cap:
                        try:
                            self.cap.release()
                        except:
                            pass
                    self._initialize_simulation_mode()
                    return
                
                time.sleep(0.5)  # 增加重试间隔
    
    def trigger_next_capture(self):
        """Trigger the next capture and analysis cycle"""
        if self.running and self.analysis_running and not self.paused and not self.processing:
            print(f"触发新一轮图像分析 {time.strftime('%H:%M:%S')}")
            self.capture_and_analyze()
    
    def capture_and_analyze(self):
        """Capture webcam screenshots and send them for analysis"""
        if self.running and not self.processing and not self.paused:
            self.processing = True
            self.app.update_status("正在捕获图像进行分析...")
            
            try:
                # Capture multiple screenshots
                screenshots, current_screenshot = self._capture_screenshots(num_shots=5, interval=0.2)  # 增加截图数量到5张，确保超过最低要求
                
                if len(screenshots) < 4:
                    # 如果截图数量不足，不进行分析
                    error_msg = f"截图数量不足，无法进行分析 (获取了 {len(screenshots)} 张，需要至少4张)"
                    print(error_msg)
                    self.app.update_status(error_msg)
                    self.processing = False
                    # 如果是暂时性问题，延迟后重试
                    if not self.paused:
                        self.app.after(3000, self.trigger_next_capture)
                    return
                
                # Upload screenshots to OSS
                print(f"正在上传 {len(screenshots)} 张截图用于分析...")
                oss_urls = self._upload_screenshots(screenshots)
                
                if not oss_urls or len(oss_urls) < 4:
                    error_msg = f"上传截图失败或数量不足，无法进行分析 (上传了 {len(oss_urls)} 张，需要至少4张)"
                    print(error_msg)
                    self.app.update_status(error_msg)
                    self.processing = False
                    # 如果是暂时性问题，延迟后重试
                    if not self.paused:
                        self.app.after(3000, self.trigger_next_capture)
                    return
                
                # Display current screenshot
                if current_screenshot:
                    # Update main window display
                    self.app.update_image(current_screenshot)
                
                # Generate placeholder ID for tracking
                placeholder_id = f"img_{int(time.time())}"
                
                # Send to API for analysis
                print("开始分析图像...")
                self.app.update_status("正在分析学习状态...")
                
                # 创建分析线程
                analysis_thread = threading.Thread(
                    target=self._analyze_screenshots,
                    args=(oss_urls, current_screenshot, placeholder_id)
                )
                analysis_thread.daemon = True
                analysis_thread.start()
            except Exception as e:
                error_msg = f"分析过程出错: {e}"
                print(error_msg)
                self.app.update_status(error_msg)
                self.processing = False
                
                # 延迟后重试
                if not self.paused:
                    self.app.after(15000, self.trigger_next_capture)
    
    def _analyze_screenshots(self, oss_urls, current_screenshot, placeholder_id):
        """分析截图并更新UI"""
        try:
            self.app.update_status("正在分析服务器图像...")
            
            # 检查URL数量是否满足API要求
            if len(oss_urls) < 4:
                error_msg = f"图片URL数量不足，无法进行分析 (有 {len(oss_urls)} 张，需要至少4张)"
                print(error_msg)
                self.app.update_status(error_msg)
                self.processing = False
                
                # 如果是暂时性问题，延迟后重试
                if not self.paused:
                    self.app.after(15000, self.trigger_next_capture)
                return
            
            # 在右下角监督窗口显示分析开始信息
            self.app.add_supervision_message("🔍 正在分析当前画面...", is_system=True)
            
            # 发送分析请求并等待结果
            analysis_text = self._get_image_analysis(oss_urls)
            
            if analysis_text:
                print(f"分析完成，更新占位符: {placeholder_id}")
                
                # 提取行为类型用于日志记录
                behavior_num, behavior_desc = extract_behavior_type(analysis_text)
                
                # 从配置文件获取对应行为类型的语音提示 - 使用人性化随机选择
                try:
                    from behavior_prompts import get_random_voice_prompt
                    # 构建上下文信息
                    context = {
                        "frequency": getattr(self, f'behavior_{behavior_num}_count', 0),
                        "duration_minutes": getattr(self, 'session_duration_minutes', 0)
                    }
                    voice_prompt = get_random_voice_prompt(behavior_num, context)
                except ImportError:
                    # 降级处理：使用原有方式
                    voice_prompt = BEHAVIOR_VOICE_PROMPTS.get(behavior_num, f"行为监测：{behavior_desc}")
                
                # 记录行为日志
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                log_message = f"{timestamp}-{behavior_num}-{analysis_text}"
                logging.info(log_message)
                print(f"行为记录已保存到日志: {behavior_num}-{behavior_desc}")
                
                # 修改：在这里直接操作app的observation_history，确保记录被添加
                current_time = time.time()
                observation = {
                    "timestamp": current_time,
                    "behavior_num": behavior_num,
                    "behavior_desc": behavior_desc,
                    "analysis": analysis_text,
                    "voice_prompt": voice_prompt  # 添加语音提示到观察记录
                }
                
                self.app.observation_history.append(observation)
                print(f"WebcamHandler: 已添加新行为到observation_history: {behavior_num}-{behavior_desc}, 当前长度: {len(self.app.observation_history)}")
                
                # 限制观察历史记录的长度，防止内存泄漏
                if len(self.app.observation_history) > self.app.max_observation_history:
                    # 移除最旧的记录，保留最新的记录
                    excess_records = len(self.app.observation_history) - self.app.max_observation_history
                    self.app.observation_history = self.app.observation_history[excess_records:]
                    print(f"清理历史记录，移除了 {excess_records} 条旧记录，当前长度: {len(self.app.observation_history)}")
                
                # 直接处理图像分析结果，显示在右下角监督窗口
                    self.app.update_status("处理分析结果...")
                self.app.process_image_analysis(
                        analysis_text, 
                    image_urls=oss_urls,
                        screenshots=[current_screenshot] if current_screenshot else [],
                    msg_id=placeholder_id
                )
                
                # 不在这里播放语音，而是在update_placeholder中统一处理
                
                # 重置处理标志
                self.processing = False
                
                # 等待语音播放完成后再继续
                self._wait_for_audio_completion()
            else:
                # 如果分析失败，使用降级处理
                print("主分析失败，尝试使用降级分析...")
                fallback_text = self._get_fallback_analysis()
                if fallback_text:
                    print("使用降级分析处理结果")
                    self.app.process_image_analysis(fallback_text, oss_urls, current_screenshot, placeholder_id)
                else:
                    error_msg = "分析暂时不可用，将在下次尝试"
                    print(error_msg)
                    self.app.update_status(error_msg)
                
                self.processing = False
                
                # 延迟后重试
                if not self.paused:
                    self.app.after(15000, self.trigger_next_capture)
        except Exception as e:
            error_msg = f"分析截图时出错: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
            self.processing = False
            
            # 延迟后重试
            if not self.paused:
                self.app.after(15000, self.trigger_next_capture)
    
    def _wait_for_audio_completion(self):
        """等待语音播放完成后再调度下一次分析"""
        if self.app.audio_player.playing or self.app.audio_player.tts_queue.qsize() > 0:
            # 如果语音正在播放或队列中有待播放的内容，延迟后再次检查
            # 计算等待时间并更新状态，让用户知道系统正在等待
            static_indicators = ["⋯", "⋯⋯", "⋯⋯⋯"]
            wait_index = int(time.time()) % 3  # 循环显示等待指示器
            wait_indicator = static_indicators[wait_index]
            
            status_message = f"等待语音播放完成{wait_indicator}（暂停分析）"
            print("等待语音播放完成...")
            self.app.update_status(status_message)
            self.app.after(800, self._wait_for_audio_completion)  # 降低检查频率以减少CPU使用
        else:
            # 语音播放完成，可以开始下一次分析
            print("语音播放已完成，准备开始下一次分析")
            self.app.update_status("语音播放完成，即将开始新一轮分析...")
            self.processing = False
            # 添加较短的延迟，确保系统有时间恢复
            self.app.after(15000, self.trigger_next_capture)  # 从12秒增加到15秒
    
    def _cleanup_temp_files(self, file_paths):
        """清理分析后的临时文件"""
        try:
            for path in file_paths:
                # 只清理临时文件，不删除OSS URL
                if isinstance(path, str) and os.path.exists(path) and path.startswith('temp_screenshot_'):
                    try:
                        os.remove(path)
                        print(f"分析完成后清理临时文件: {path}")
                    except Exception as e:
                        print(f"清理临时文件失败: {e}")
        except Exception as e:
            print(f"清理临时文件过程出错: {e}")

    
    @handle_exceptions(default_return=None)
    def _get_image_analysis(self, image_urls):
        """Send images to Qwen-VL API and get analysis text"""
        try:
            print("调用图像分析API...")
            
            # 首先检查API客户端是否可用
            if qwen_client is None:
                error_msg = "图像分析API错误: 通义千问API客户端未初始化或连接失败"
                print(error_msg)
                self.app.update_status(error_msg)
                return None
            
            # 检查TPU服务器是否可用
            use_tpu = False
            if USE_TPU_SERVER:
                try:
                    use_tpu = check_tpu_server(TPU_SERVER_HOST, QWEN_TPU_PORT)
                except Exception as e:
                    print(f"检查通义千问TPU服务器时出错: {e}")
                    use_tpu = False
            
            model_name = "qwen-vl-tpu" if use_tpu else "qwen-vl-max"
            print(f"使用模型: {model_name}")
            
            messages = [{
                "role": "system",
                "content": [{"type": "text", "text": "详细观察这个学生正在做什么。务必判断他属于以下哪种学习状态：1.认真学习(专注做作业或阅读), 2.轻度走神(东张西望但仍在学习区域), 3.使用学习工具(使用尺子、计算器等), 4.喝水休息, 5.玩手机分心, 6.与同学交流学习内容, 7.睡觉或趴桌子, 8.其他分心行为, 9.吃零食, 10.不在座位上(学生离开了座位或画面中完全看不到学生)。\n\n特别注意：对于6.与同学交流学习内容，主要判断标准是：\n- 学生嘴巴明显张开并有说话动作（口型变化明显、嘴部有清晰可见的开合动作）\n- 明显张开定义：嘴部有清晰可见的开合动作，口型变化明显，说话姿态\n- 轻微张开不算：仅仅是嘴唇微微分开、打哈欠、喝水、或静态的微张状态不应判断为交流\n\n如果只是轻微的头部转动、偶尔看向别处、嘴唇微张、打哈欠等，应判断为2.轻度走神或8.其他分心行为，而不是交流。\n\n分析学生的表情、姿势、手部动作和桌面状态来作出判断。使用中文回答，并明确指出是哪种学习状态。重要说明：只有当画面中完全没有学生或学生明显离开座位时，才判断为10(不在座位上)；如果能看到学生身体的任何部分（如头部、手臂等），则应该根据可见部分判断其行为，而不要急于判断为不在座位上。"}]
            }]
            
            # 构建增强的提示词，包含姿态和情绪分析
            enhanced_prompt = """请对这个学生进行全面分析，包括行为、姿态和情绪三个方面：

【行为分析】
请判断学生的行为状态：
1.认真学习(专注做作业或阅读)
2.轻度走神(东张西望但仍在学习区域)
3.使用学习工具(使用尺子、计算器等)
4.喝水休息
5.玩手机分心
6.与同学交流学习内容(重要：主要看是否有明显说话动作)
7.睡觉或趴桌子
8.其他分心行为
9.吃零食
10.不在座位上(学生完全离开了座位或画面中完全看不到学生)

特别说明：对于6.与同学交流学习内容，主要判断标准：
- 学生嘴巴明显张开并有说话动作（口型变化明显、嘴部有清晰可见的开合动作）
- 明显张开定义：嘴部有清晰可见的开合动作，口型变化明显，说话姿态
- 轻微张开不算：仅仅是嘴唇微微分开、打哈欠、喝水、或静态的微张状态不应判断为交流
如果只是头部轻微转动、偶尔看向别处、嘴唇微张、打哈欠等，应判断为2.轻度走神或8.其他分心行为。

【姿态分析】
请详细描述学生的身体姿态：
- 头部位置：正直/前倾/后仰/左偏/右偏/低头/抬头
- 颈部状态：挺直/前伸/侧弯/紧张/放松
- 肩膀状态：平衡/高低不平/耸肩/含胸/挺胸
- 身体姿势：端正坐姿/弯腰驼背/侧身/趴桌/站立/其他
- 整体评价：良好姿态/轻微不良/明显不良/需要纠正

【情绪分析】
请识别学生的情绪状态：
- 主要情绪：快乐/平静/专注/疲惫/焦虑/沮丧/愤怒/困惑/兴奋/无聊
- 情绪强度：轻微/中等/强烈
- 面部表情：微笑/皱眉/专注/疲惫/困惑/其他
- 精神状态：精神饱满/一般/疲惫/困倦

请按照以下格式输出分析结果：
【行为判断】：[行为编号]-[行为描述]
【姿态描述】：[详细的姿态分析]
【情绪状态】：[情绪分析结果]
【综合评价】：[整体观察和建议]

注意：只有在画面中完全看不到学生时，才判断为'不在座位上'；如果能看到学生身体的任何部分，应根据可见部分判断其行为。"""

            message_payload = {
                "role": "user",
                "content": [
                    {"type": "video", "video": image_urls},
                    {"type": "text", "text": enhanced_prompt}
                ]
            }
            messages.append(message_payload)
            
            # 使用优化的API客户端进行调用
            completion = qwen_client.chat_completion_with_timeout(
                model=model_name,
                messages=messages,
            )
            analysis_text = completion.choices[0].message.content
            print(f"图像分析完成，分析长度: {len(analysis_text)} 字符")
            
            return analysis_text
            
        except TimeoutError as e:
            error_msg = f"图像分析API超时: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
            # 提供降级的模拟分析结果
            return self._get_fallback_analysis()
        except Exception as e:
            error_msg = f"图像分析API错误: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
            # 提供降级的模拟分析结果
            return self._get_fallback_analysis()

    def _get_fallback_analysis(self):
        """当API失败时提供降级的分析结果，包含姿态和情绪信息"""
        import random
        fallback_analyses = [
            """【行为判断】：1-认真学习
【姿态描述】：学生保持端正的坐姿，头部位置正直，颈部挺直，肩膀平衡。身体姿势良好，整体评价为良好姿态。
【情绪状态】：主要情绪为专注，情绪强度中等，面部表情专注，精神状态良好。
【综合评价】：学生正在认真学习，专注于桌面上的学习材料。姿势端正，注意力集中，是良好的学习状态。""",

            """【行为判断】：2-轻度走神
【姿态描述】：学生坐姿基本端正，但头部稍有偏转，颈部略微前伸，肩膀状态一般。身体姿势轻微不良，需要适当调整。
【情绪状态】：主要情绪为平静，情绪强度轻微，面部表情一般，精神状态一般。
【综合评价】：学生似乎在思考问题，偶尔环顾四周。虽然有轻微的走神迹象，但整体还在学习区域内。""",

            """【行为判断】：1-认真学习
【姿态描述】：学生保持良好的学习姿态，头部位置适中，颈部放松，肩膀平衡。身体姿势端正，整体评价为良好姿态。
【情绪状态】：主要情绪为专注，情绪强度中等，面部表情专注，精神状态精神饱满。
【综合评价】：学生保持学习姿态，专注于面前的作业或阅读材料。手部动作显示正在书写或翻页，是积极的学习行为。"""
        ]
        selected_analysis = random.choice(fallback_analyses)
        print(f"使用降级分析结果: {selected_analysis[:50]}...")
        return selected_analysis
            
    def toggle_pause(self):
        """Toggle the paused state of the analysis cycle"""
        self.paused = not self.paused
        status = "已暂停分析" if self.paused else "已恢复分析"
        self.app.update_status(status)
        print(status)
        
        # If unpausing, trigger next capture
        if not self.paused and not self.processing:
            self.app.after(25000, self.trigger_next_capture)  # 从20秒增加到25秒
    
    def get_current_screenshot(self):
        """Get the most recent webcam image"""
        return self.last_webcam_image
    
    def _capture_screenshots(self, num_shots=4, interval=0.2):  # 确保至少有4张截图
        """Capture multiple screenshots from webcam for analysis
           Return both the full set (for analysis) and one current screenshot for display"""
        
        screenshots = []
        
        # 使用模拟模式时，返回模拟图像
        if self.simulation_mode:
            # 返回随机的模拟图像
            for i in range(num_shots):
                img_index = (int(time.time() * 10) + i) % len(self.simulation_images)
                screenshots.append(self.simulation_images[img_index].copy())
                time.sleep(interval)
            
            # 当前帧显示
            current_screenshot = self.simulation_images[0].copy()
            
            if self.debug:
                print(f"已捕获 {len(screenshots)} 张模拟截图用于分析")
                
            return screenshots, current_screenshot
        
        # 正常摄像头模式
        current_screenshot = None
        
        # 捕获前检查摄像头状态
        if not self.cap or not self.cap.isOpened():
            print("摄像头未打开，无法捕获截图")
            return [], None
        
        # 捕获多张截图，确保至少有4张
        attempt_count = 0
        max_attempts = num_shots * 3  # 最多尝试次数是请求数量的3倍
        
        while len(screenshots) < num_shots and attempt_count < max_attempts:
            try:
                ret, frame = self.cap.read()
                if not ret or frame is None or frame.size == 0:
                    print(f"截图 {attempt_count+1} 获取失败，重试")
                    attempt_count += 1
                    time.sleep(0.1)  # 短暂延迟后重试
                    continue
                
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                img = Image.fromarray(frame_rgb)
                screenshots.append(img)
                
                # 第一张有效图同时用于当前显示
                if current_screenshot is None:
                    current_screenshot = img.copy()
                
                # 释放资源
                del frame
                del frame_rgb
                
                print(f"成功捕获第 {len(screenshots)}/{num_shots} 张截图")
                time.sleep(interval)  # 拍摄间隔
                
                attempt_count += 1
                
            except Exception as e:
                print(f"捕获截图时出错: {e}")
                attempt_count += 1
                time.sleep(0.1)
        
        # 确保我们至少有4张截图（API要求）
        if len(screenshots) < 4:
            print(f"警告: 只获取到 {len(screenshots)} 张截图，少于API要求的4张")
            # 如果截图不足4张，复制已有截图直到达到4张
            while len(screenshots) < 4 and len(screenshots) > 0:
                print(f"复制已有截图以满足最低要求: {len(screenshots)}/4")
                screenshots.append(screenshots[len(screenshots) % len(screenshots)].copy())
        
        # 如果没有当前截图但有其他截图，使用第一张
        if current_screenshot is None and len(screenshots) > 0:
            current_screenshot = screenshots[0].copy()
        
        if self.debug:
            print(f"已捕获 {len(screenshots)} 张截图用于分析和 {1 if current_screenshot else 0} 张当前截图")
            
        return screenshots, current_screenshot
    
    def _upload_screenshots(self, screenshots):
        """Upload screenshots to OSS and return URLs"""
        try:
            if self.debug:
                print(f"正在上传 {len(screenshots)} 张截图到OSS")
                
            auth = oss2.Auth(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET)
            bucket = oss2.Bucket(auth, OSS_ENDPOINT, OSS_BUCKET)
            
            oss_urls = []
            for i, img in enumerate(screenshots):
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG')
                buffer.seek(0)
                
                object_key = f"screenshots/{int(time.time())}_{i}.jpg"
                
                result = bucket.put_object(object_key, buffer)
                if result.status == 200:
                    url = f"https://{OSS_BUCKET}.{OSS_ENDPOINT}/{object_key}"
                    oss_urls.append(url)
                    if self.debug:
                        print(f"已上传图片 {i+1}: {url}")
                else:
                    error_msg = f"上传错误，状态码: {result.status}"
                    print(error_msg)
                    self.app.update_status(error_msg)
            
            return oss_urls
        except Exception as e:
            error_msg = f"上传图片时出错: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
            return []

    def create_camera_window(self):
        """创建一个摄像头框架集成到主窗口边栏"""
        if not self.camera_window or self.camera_window.is_closed:
            # 创建摄像头集成窗口
            self.camera_window = CameraWindow(self.app)
            
            # 新UI中摄像头直接集成在preview_frame中，不需要单独pack

    def stop(self):
        """Stop webcam capture process"""
        self.running = False
        self.analysis_running = False
        self.simulation_mode = False
        
        # 确保摄像头资源被释放
        if hasattr(self, 'cap') and self.cap is not None:
            try:
                print("正在释放摄像头资源...")
                self.cap.release()
                self.cap = None
                print("摄像头资源已释放")
            except Exception as e:
                print(f"释放摄像头资源时出错: {e}")
        
        # 确保等待线程结束
        if hasattr(self, 'webcam_thread') and self.webcam_thread and self.webcam_thread.is_alive():
            try:
                print("等待摄像头线程结束...")
                self.webcam_thread.join(timeout=3.0)
                if self.webcam_thread.is_alive():
                    print("警告: 摄像头线程未能在超时时间内结束")
                else:
                    print("摄像头线程已正常结束")
            except Exception as e:
                print(f"等待线程结束时出错: {e}")
        
        # 标记摄像头窗口为关闭状态，但不销毁它，因为它是集成到主窗口中
        if self.camera_window:
            self.camera_window.is_closed = True
            print("摄像头窗口已标记为关闭")

        # 清理可能的内存引用
        if hasattr(self, 'last_webcam_image') and self.last_webcam_image is not None:
            del self.last_webcam_image
            self.last_webcam_image = None
        
        # 执行一次内存清理
        gc.collect()
        print("摄像头停止过程完成并执行了内存清理")

class AudioPlayer:
    def __init__(self, app):
        self.app = app
        
        # TTS队列设置
        self.tts_queue = queue.PriorityQueue()
        self.max_queue_size = 5
        
        # 播放状态标志
        self.playing = False
        self.skip_requested = False
        
        # TTS处理线程
        self.tts_running = False
        self.tts_thread = None
        
        # 播放线程
        self.play_thread = None
        
        # 初始化增强版TTS
        try:
            print("初始化TTS系统...")
            # 直接指定使用Edge TTS和小小声音
            self.tts_model = EnhancedTTS(voice_type="edge", system_voice="zh-CN-XiaoxiaoNeural")
            print("TTS初始化成功，使用小小声音")
        except Exception as e:
            print(f"TTS初始化失败: {e}")
            self.tts_model = None
        
        # 初始化pygame音频系统
        try:
            print("初始化音频播放系统...")
            pygame.mixer.init()
            print("音频播放系统初始化成功")
        except Exception as e:
            print(f"音频播放系统初始化失败: {e}")
            print("将使用备用的音频播放方法")
    
    def start_tts_thread(self):
        """启动TTS处理线程"""
        if not self.tts_running:
            self.tts_running = True
            self.tts_thread = threading.Thread(target=self._process_tts_queue)
            self.tts_thread.daemon = True
            self.tts_thread.start()
            print("TTS处理线程已启动")
    
    def _process_tts_queue(self):
        """处理TTS队列中的文本，按优先级播放"""
        while self.tts_running:
            try:
                if not self.tts_queue.empty() and not self.playing:
                    # 获取优先级最高的项目 (priority, timestamp, category, text)
                    priority, timestamp, category, text = self.tts_queue.get()

                    # 检查是否过期（超过15秒的低优先级消息被视为过期）
                    current_time = time.time()
                    if priority > 1 and current_time - timestamp > 15:
                        print(f"忽略过期的TTS请求 (已过{current_time - timestamp:.1f}秒): '{text[:30]}...'")
                        self.tts_queue.task_done()
                        continue

                    print(f"从TTS队列获取文本 (优先级: {priority}, 分类: {category}): '{text[:30]}...'")
                    self._synthesize_and_play(text, category)
                    self.tts_queue.task_done()
                time.sleep(0.1)
            except Exception as e:
                print(f"处理TTS队列时出错: {e}")
                time.sleep(1)
    
    def play_text(self, text, priority=2, category="general"):
        """将文本添加到TTS队列，支持优先级和分类
           优先级: 1=姿态情绪提醒(最高), 2=行为分析(普通), 3=其他(最低)
           分类: posture=姿态提醒, emotion=情绪提醒, behavior=行为提醒, general=一般
           返回: True表示成功添加到队列，False表示失败
        """
        if not text or len(text.strip()) == 0:
            print("警告: 尝试播放空文本，已忽略")
            return False

        try:
            # 清理队列，如果是高优先级请求或队列已满
            if priority == 1 or self.tts_queue.qsize() >= self.max_queue_size:
                self._clean_queue(priority)

            print(f"添加文本到TTS队列 (优先级: {priority}, 分类: {category}): '{text[:30]}...'")

            # 确保TTS处理线程已启动
            if not self.tts_running or not self.tts_thread or not self.tts_thread.is_alive():
                self.start_tts_thread()

            # 添加到队列（包含优先级、时间戳、分类和文本）
            self.tts_queue.put((priority, time.time(), category, text))

            # 返回True表示成功添加到队列
            return True

        except Exception as e:
            print(f"添加文本到TTS队列失败: {e}")
            return False
    
    def _clean_queue(self, new_priority):
        """清理队列，保留更高优先级的项目"""
        if self.tts_queue.empty():
            return
            
        # 如果是最高优先级请求，清空所有正在排队的音频
        if new_priority == 1:
            print("收到高优先级语音请求，清空当前TTS队列")
            while not self.tts_queue.empty():
                try:
                    self.tts_queue.get_nowait()
                    self.tts_queue.task_done()
                except:
                    pass
            return
        
        # 对于普通优先级，仅保持队列在最大长度以下
        while self.tts_queue.qsize() >= self.max_queue_size:
            try:
                self.tts_queue.get_nowait()
                self.tts_queue.task_done()
                print("队列已满，移除最旧的TTS请求")
            except:
                break

    def _synthesize_and_play(self, text, category="general"):
        """合成并播放语音（使用系统TTS）"""
        self.app.update_status(f"正在合成语音({category})...")
        print(f"TTS合成 [{category}]: '{text}'")

        # 设置播放状态以禁用语音检测
        self.app.is_playing_audio = True
        self.playing = True  # 确保播放状态正确设置
        
        try:
            # 使用系统TTS来实现语音合成功能
            try:
                # 使用已初始化的TTS模型或重新初始化
                if self.tts_model is None:
                    print("TTS模型未初始化，尝试重新初始化")
                    # 直接指定使用Edge TTS和小小声音
                    self.tts_model = EnhancedTTS(voice_type="edge", system_voice="zh-CN-XiaoxiaoNeural")
                    print("TTS重新初始化成功，使用小小声音")
                
                # 合成并播放语音
                print(f"使用TTS合成文本: '{text}'")
                success = self.tts_model.speak(text)
            except Exception as model_error:
                print(f"TTS错误: {model_error}")
                self.app.update_status(f"TTS错误: {model_error}")
                success = False
            
            # 如果成功合成和播放
            if success:
                print("系统TTS播放成功")
                self.app.update_status("语音播放完成")
            else:
                error_msg = "系统TTS播放失败，跳过语音播放"
                print(error_msg)
                self.app.update_status(error_msg)
                
                # 打印消息而不是播放
                print(f"[语音无法合成] 提示内容: {text}")
        except Exception as e:
            error_msg = f"TTS处理错误: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
            
            # 打印消息而不是播放
            print(f"[语音无法播放] 提示内容: {text}")
        finally:
            # 无论是否成功，都重置播放状态
            self.app.is_playing_audio = False
            self.playing = False  # 确保播放状态正确重置
    
    def play_audio_file(self, file_path):
        """公共方法用于播放音频文件"""
        print(f"请求播放音频文件: {file_path}")
        
        # 跳过当前播放并等待
        if self.playing:
            self.skip_requested = True
            if self.play_thread and self.play_thread.is_alive():
                print("等待当前播放结束...")
                self.play_thread.join(timeout=2.0)
                
        # 直接播放文件，不通过队列
        self._play_audio_file_internal(file_path)
    
    def _play_audio_file_internal(self, file_path):
        """内部方法用于实际播放音频文件"""
        print(f"开始播放音频文件: {file_path}")
        
        # 确保之前的播放已停止
        if self.playing:
            self.skip_requested = True
            if self.play_thread and self.play_thread.is_alive():
                self.play_thread.join(timeout=1.0)
        
        self.skip_requested = False
        self.playing = True
        
        # Mark system as playing audio to disable voice detection
        self.app.is_playing_audio = True
        
        self.play_thread = threading.Thread(target=self._play_audio, args=(file_path,))
        self.play_thread.daemon = True
        self.play_thread.start()
    
    def _play_audio(self, file_path):
        """Audio playback worker thread"""
        self.app.update_status("正在播放语音...")
        
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                error_msg = f"音频文件不存在: {file_path}"
                print(error_msg)
                self.app.update_status(error_msg)
                self.playing = False
                self.app.is_playing_audio = False
                return
                
            # Check file size
            file_size = os.path.getsize(file_path)
            print(f"音频文件大小: {file_size} 字节")
            if file_size == 0:
                error_msg = f"音频文件为空: {file_path}"
                print(error_msg)
                self.app.update_status(error_msg)
                self.playing = False
                self.app.is_playing_audio = False
                return
            
            # 尝试使用pygame播放音频 (替代pydub播放)
            try:
                print("使用pygame播放音频...")
                
                pygame.mixer.init()
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()
                
                # 等待播放完成或用户跳过
                while pygame.mixer.music.get_busy() and not self.skip_requested and self.playing:
                    time.sleep(0.1)
                    
                # 停止播放
                pygame.mixer.music.stop()
                pygame.mixer.quit()
                    
                if self.skip_requested:
                    print("音频播放被跳过")
                else:
                    print("音频播放完成")
                
            except Exception as e:
                print(f"pygame播放出错，尝试使用系统命令: {e}")
                
                # 备用方案：使用系统命令播放
                if sys.platform == 'darwin':  # macOS
                    print("使用macOS的afplay命令播放...")
                    os.system(f"afplay {file_path}")
                elif sys.platform.startswith('linux'):  # Linux
                    print("使用Linux的aplay命令播放...")
                    os.system(f"aplay {file_path}")
                else:
                    print(f"不支持的平台: {sys.platform}")
                    raise Exception(f"不支持的平台: {sys.platform}")
                
            # 尝试删除临时文件
            try:
                if os.path.exists(file_path) and file_path.startswith('output_'):
                    os.remove(file_path)
                    print(f"临时文件已删除: {file_path}")
            except Exception as e:
                print(f"删除临时文件出错: {e}")
                
        except Exception as e:
            error_msg = f"音频播放错误: {e}"
            print(error_msg)
            self.app.update_status(error_msg)
        
        self.playing = False
        # Reset playing status to re-enable voice detection
        self.app.is_playing_audio = False
        
        self.app.update_status("Ready")
    
    def skip_current(self):
        """Skip the currently playing audio"""
        if self.playing:
            self.skip_requested = True
            self.app.update_status("跳过当前音频...")
            print("已请求跳过当前音频")
            
            # Reset playing status immediately to re-enable voice detection
            self.app.is_playing_audio = False
            
    def get_queue_status(self):
        """获取队列状态信息"""
        return {
            "queue_size": self.tts_queue.qsize(),
            "is_playing": self.playing,
            "is_audio_active": self.app.is_playing_audio,
            "tts_running": self.tts_running
        }

    def wait_for_completion(self, timeout=30):
        """等待当前播放和队列完成"""
        start_time = time.time()
        while (self.playing or self.tts_queue.qsize() > 0) and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        return not (self.playing or self.tts_queue.qsize() > 0)

    def stop(self):
        """停止所有播放和处理"""
        self.skip_current()
        self.tts_running = False

        # 清空队列
        while not self.tts_queue.empty():
            try:
                self.tts_queue.get_nowait()
                self.tts_queue.task_done()
            except:
                pass

# ---------------- UI Class ----------------
# ===================== 学习状态分析报告窗口 =====================
class LearningAnalysisWindow(ctk.CTkToplevel):
    """学习状态分析报告窗口"""
    
    def __init__(self, parent, analytics_data):
        super().__init__(parent)
        self.analytics = analytics_data
        self.parent = parent
        
        self.title("📊 学习状态分析报告")
        # 设置完全白色背景（同时兼容 Tk 属性）
        self.configure(fg_color="#ffffff", bg="#ffffff")
        self.geometry("1200x800")
        self.minsize(1000, 700)
        
        # ====== 优化的配色方案（参照界面截图）======
        self.modern_colors = {
            'primary': "#4f46e5",      # 主色调 - 靛蓝
            'secondary': "#10b981",    # 次要色调 - 绿色
            'accent': "#f59e0b",       # 强调色 - 橙色
            'danger': "#ef4444",       # 危险色 - 红色
            'success': "#10b981",      # 成功色 - 绿色
            'warning': "#f59e0b",      # 警告色 - 橙色
            'info': "#3b82f6",         # 信息色 - 蓝色
            'light': "#f8fafc",        # 浅色背景
            'white': "#ffffff",        # 纯白色
            'gray_50': "#f9fafb",      # 极浅灰
            'gray_100': "#f3f4f6",     # 浅灰
            'gray_200': "#e5e7eb",     # 中浅灰
            'gray_300': "#d1d5db",     # 中灰
            'gray_600': "#4b5563",     # 深灰
            'gray_800': "#1f2937",     # 极深灰
            'text_primary': "#111827",  # 主要文本色
            'text_secondary': "#6b7280", # 次要文本色
            # 参照截图的卡片背景色 - 与学习计划界面保持一致
            'card_blue': "#dbeafe",    # 柔和蓝色卡片背景（学习时长）- 减少眼部疲劳
            'card_green': "#d1fae5",   # 绿色卡片背景（检测次数）
            'card_orange': "#fed7aa",  # 橙色卡片背景（平均专注度）
            'card_pink': "#fce7f3",    # 粉色卡片背景（主要行为）
            'card_large_green': "#d1fae5", # 大绿色卡片（行为分布）
            'card_large_orange': "#fed7aa" # 大橙色卡片（专注度趋势）
        }

        # 设置窗口图标和样式
        self.resizable(True, True)
        
        # 防止窗口在创建时过度消耗内存
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 设置UI
        self.setup_analysis_ui()
        
        # 加载数据
        self.load_analysis_data()
        
        # 聚焦到此窗口
        self.focus()
        self.lift()
        
    def setup_analysis_ui(self):
        """设置分析报告UI"""
        try:
            # 主容器（纯白背景）
            main_container = ctk.CTkFrame(self, corner_radius=0, fg_color=self.modern_colors['white'])
            main_container.pack(fill="both", expand=True, padx=15, pady=15)
            
            # 标题区域
            self.create_title_section(main_container)
            
            # 创建标签页
            self.notebook = ctk.CTkTabview(main_container)
            self.notebook.pack(fill="both", expand=True, pady=(20, 0))

            # 添加标签页
            self.notebook.add("📈 数据概览")
            self.notebook.add("📋 详细报告")

            # 设置标签页背景色
            try:
                self.notebook.configure(fg_color=self.modern_colors['white'])
                for tab_frame in self.notebook._tab_dict.values():
                    tab_frame.configure(fg_color=self.modern_colors['white'])
            except Exception as bg_err:
                print("Notebook 颜色调整失败:", bg_err)
            
            # 设置各个标签页
            self.setup_overview_tab()
            self.setup_detailed_report_tab()
            
        except Exception as e:
            print(f"设置分析UI失败: {e}")
    
    def create_title_section(self, parent):
        """创建标题区域"""
        title_frame = ctk.CTkFrame(parent, corner_radius=16, fg_color=self.modern_colors['gray_50'], height=80)
        title_frame.pack(fill="x", pady=(0, 10))
        title_frame.pack_propagate(False)
        
        # 标题和图标
        title_container = ctk.CTkFrame(title_frame, fg_color="transparent")
        title_container.pack(expand=True, fill="both")
        
        title_label = ctk.CTkLabel(
            title_container,
            text="📊 学习状态分析报告",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.modern_colors['text_primary']
        )
        title_label.pack(side="left", padx=30, pady=20)
        
        # 当前时间
        current_time = datetime.now().strftime("%Y年%m月%d日 %H:%M")
        time_label = ctk.CTkLabel(
            title_container,
            text=f"📅 报告生成时间: {current_time}",
            font=ctk.CTkFont(size=12),
            text_color=self.modern_colors['text_secondary']
        )
        time_label.pack(side="right", padx=30, pady=20)
    
    def setup_overview_tab(self):
        """设置数据概览标签页"""
        try:
            overview_frame = self.notebook.tab("📈 数据概览")
            overview_frame.configure(fg_color=self.modern_colors['white'])
            
            # 创建滚动框架
            scroll_frame = ctk.CTkScrollableFrame(overview_frame, fg_color=self.modern_colors['white'])
            scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            # 会话摘要卡片
            self.create_modern_summary_card(scroll_frame)
            
            # 行为分布卡片
            self.create_modern_behavior_distribution_card(scroll_frame)
            
            # 专注度趋势分析卡片
            self.create_modern_concentration_trend_card(scroll_frame)
            
        except Exception as e:
            print(f"设置概览标签页失败: {e}")
    
    def create_modern_summary_card(self, parent):
        """创建现代化会话摘要卡片"""
        try:
            # 获取摘要数据
            summary = self.analytics.get_session_summary()
            
            # 主标题
            summary_title = ctk.CTkLabel(
                parent,
                text="🎯 本次学习会话摘要",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=self.modern_colors['text_primary'],
                anchor="w"
            )
            summary_title.pack(anchor="w", padx=10, pady=(0, 20))
            
            # 创建卡片网格容器
            cards_container = ctk.CTkFrame(parent, fg_color="transparent")
            cards_container.pack(fill="x", pady=(0, 30))
            
            # 配置网格布局
            cards_container.grid_columnconfigure(0, weight=1)
            cards_container.grid_columnconfigure(1, weight=1)
            
            # 学习时长卡片
            duration_card = self.create_metric_card_modern(
                cards_container, 
                "⏱️", 
                "学习时长", 
                f"{summary['duration']} 分钟",
                self.modern_colors['card_blue'],
                "学习专注时间统计"
            )
            duration_card.grid(row=0, column=0, sticky="ew", padx=(0, 10), pady=10)
            
            # 检测次数卡片
            detection_card = self.create_metric_card_modern(
                cards_container, 
                "🔍", 
                "检测次数", 
                f"{summary['total_behaviors']} 次",
                self.modern_colors['card_green'],
                "行为监测统计"
            )
            detection_card.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=10)
            
            # 平均专注度卡片
            concentration_card = self.create_metric_card_modern(
                cards_container, 
                "📊", 
                "平均专注度", 
                f"{summary['avg_concentration']} 分",
                self.modern_colors['card_orange'],
                "整体专注水平评估"
            )
            concentration_card.grid(row=1, column=0, sticky="ew", padx=(0, 10), pady=10)
            
            # 主要行为卡片
            behavior_card = self.create_metric_card_modern(
                cards_container, 
                "🎯", 
                "主要行为", 
                summary['dominant_behavior'],
                self.modern_colors['card_pink'],
                "学习期间主导行为"
            )
            behavior_card.grid(row=1, column=1, sticky="ew", padx=(10, 0), pady=10)
            
        except Exception as e:
            print(f"创建摘要卡片失败: {e}")
    
    def create_metric_card_modern(self, parent, icon, title, value, bg_color, description):
        """创建现代化指标卡片（参照截图设计）"""
        card = ctk.CTkFrame(
            parent, 
            corner_radius=12, 
            fg_color=bg_color,
            border_width=0,
            height=120
        )
        card.pack_propagate(False)
        
        # 卡片内容容器
        content_frame = ctk.CTkFrame(card, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=15)
        
        # 顶部：图标和标题
        header_frame = ctk.CTkFrame(content_frame, fg_color="transparent", height=30)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)
        
        # 图标
        icon_label = ctk.CTkLabel(
            header_frame,
            text=icon,
            font=ctk.CTkFont(size=18),
            text_color=self.modern_colors['text_primary']
        )
        icon_label.pack(side="left")
        
        # 标题
        title_label = ctk.CTkLabel(
            header_frame,
            text=title,
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.modern_colors['text_primary']
        )
        title_label.pack(side="left", padx=(10, 0))
        
        # 数值（居中显示）
        value_label = ctk.CTkLabel(
            content_frame,
            text=str(value),
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.modern_colors['text_primary']
        )
        value_label.pack(expand=True)
        
        return card
    
    def create_modern_behavior_distribution_card(self, parent):
        """创建现代化行为分布卡片"""
        try:
            # 标题
            behavior_title = ctk.CTkLabel(
                parent,
                text="📈 行为分布统计",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=self.modern_colors['text_primary'],
                anchor="w"
            )
            behavior_title.pack(anchor="w", padx=10, pady=(0, 20))
            
            # 行为分布卡片容器
            behavior_frame = ctk.CTkFrame(
                parent, 
                corner_radius=12, 
                fg_color=self.modern_colors['card_large_green'],
                border_width=0
            )
            behavior_frame.pack(fill="x", pady=(0, 20))
            
            # 获取行为分布数据
            distribution = self.analytics.get_behavior_distribution()
            
            if distribution:
                # 表格容器
                table_frame = ctk.CTkFrame(behavior_frame, fg_color="transparent")
                table_frame.pack(fill="x", padx=25, pady=25)
                
                # 表头
                header_frame = ctk.CTkFrame(
                    table_frame,
                    corner_radius=8,
                    height=45,
                    fg_color="transparent"
                )
                header_frame.pack(fill="x", pady=(0, 10))
                header_frame.pack_propagate(False)
                
                # 表头内容容器
                header_content = ctk.CTkFrame(header_frame, fg_color="transparent")
                header_content.pack(fill="both", expand=True, padx=20, pady=10)
                
                ctk.CTkLabel(
                    header_content, 
                    text="行为类型", 
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.modern_colors['text_primary']
                ).pack(side="left")
                
                ctk.CTkLabel(
                    header_content, 
                    text="占比", 
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.modern_colors['text_primary']
                ).pack(side="right", padx=(0, 40))
                
                ctk.CTkLabel(
                    header_content, 
                    text="次数", 
                    font=ctk.CTkFont(size=14, weight="bold"),
                    text_color=self.modern_colors['text_primary']
                ).pack(side="right", padx=(0, 20))
                
                # 数据行
                for i, (behavior, data) in enumerate(distribution.items()):
                    if data['count'] > 0:  # 只显示有数据的行为
                        row_frame = ctk.CTkFrame(
                            table_frame,
                            corner_radius=6,
                            height=40,
                            fg_color="transparent"
                        )
                        row_frame.pack(fill="x", pady=2)
                        row_frame.pack_propagate(False)
                        
                        # 行内容容器
                        row_content = ctk.CTkFrame(row_frame, fg_color="transparent")
                        row_content.pack(fill="both", expand=True, padx=20, pady=8)
                        
                        # 行为名称
                        ctk.CTkLabel(
                            row_content, 
                            text=behavior, 
                            font=ctk.CTkFont(size=12),
                            text_color=self.modern_colors['text_primary']
                        ).pack(side="left")
                        
                        # 占比
                        ctk.CTkLabel(
                            row_content, 
                            text=f"{data['percentage']}%", 
                            font=ctk.CTkFont(size=12, weight="bold"),
                            text_color=self.modern_colors['primary']
                        ).pack(side="right", padx=(0, 40))
                        
                        # 次数
                        ctk.CTkLabel(
                            row_content, 
                            text=f"{data['count']} 次", 
                            font=ctk.CTkFont(size=12),
                            text_color=self.modern_colors['text_secondary']
                        ).pack(side="right", padx=(0, 20))
            else:
                no_data_label = ctk.CTkLabel(
                    behavior_frame,
                    text="暂无行为数据",
                    font=ctk.CTkFont(size=14),
                    text_color=self.modern_colors['text_secondary']
                )
                no_data_label.pack(pady=40)
                
        except Exception as e:
            print(f"创建行为分布卡片失败: {e}")
    
    def create_modern_concentration_trend_card(self, parent):
        """创建现代化专注度趋势分析卡片（参照截图设计）"""
        try:
            # 标题
            trend_title = ctk.CTkLabel(
                parent,
                text="📈 专注度趋势分析",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=self.modern_colors['text_primary'],
                anchor="w"
            )
            trend_title.pack(anchor="w", padx=10, pady=(0, 20))
            
            # 专注度趋势卡片容器
            trend_frame = ctk.CTkFrame(
                parent, 
                corner_radius=12, 
                fg_color=self.modern_colors['card_large_orange'],
                border_width=0
            )
            trend_frame.pack(fill="x", pady=(0, 20))
            
            # 获取趋势数据
            trend_data = self.analytics.get_concentration_trend()
            
            # 趋势内容
            trend_content = ctk.CTkFrame(trend_frame, fg_color="transparent")
            trend_content.pack(fill="x", padx=25, pady=25)
            
            # 趋势文本
            trend_text = f"趋势: {trend_data['trend']}"
            if trend_data['change'] != 0:
                trend_text += f" (变化: {trend_data['change']:+.1f}分)"
            else:
                trend_text = "趋势: 无足够数据"
            
            trend_label = ctk.CTkLabel(
                trend_content,
                text=trend_text,
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=self.modern_colors['text_primary'],
                anchor="w"
            )
            trend_label.pack(anchor="w", pady=(0, 10))
            
            # 近期平均专注度
            avg_text = f"近期平均专注度: {trend_data['recent_avg']} 分"
            avg_label = ctk.CTkLabel(
                trend_content,
                text=avg_text,
                font=ctk.CTkFont(size=12),
                text_color=self.modern_colors['text_primary'],
                anchor="w"
            )
            avg_label.pack(anchor="w")
            
        except Exception as e:
            print(f"创建专注度趋势卡片失败: {e}")
    
    def setup_detailed_report_tab(self):
        """设置详细报告标签页"""
        try:
            report_frame = self.notebook.tab("📋 详细报告")
            report_frame.configure(fg_color=self.modern_colors['white'])
            
            # 创建滚动框架
            scroll_frame = ctk.CTkScrollableFrame(report_frame, fg_color=self.modern_colors['white'])
            scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            # 生成并显示现代化报告
            self.create_modern_detailed_report(scroll_frame)
            
        except Exception as e:
            print(f"设置详细报告标签页失败: {e}")
    
    def create_modern_detailed_report(self, parent):
        """创建现代化详细报告"""
        try:
            summary = self.analytics.get_session_summary()
            distribution = self.analytics.get_behavior_distribution()
            trend_data = self.analytics.get_concentration_trend()
            
            # 会话基本信息卡片
            self.create_session_info_card(parent, summary)
            
            # 主要发现卡片
            self.create_key_findings_card(parent, summary, trend_data)
            
            # 专注度分析卡片
            self.create_concentration_analysis_card(parent, trend_data)
            
            # 学习建议卡片
            self.create_suggestions_card(parent, summary, trend_data)
            
        except Exception as e:
            print(f"创建现代化详细报告失败: {e}")
    
    def create_session_info_card(self, parent, summary):
        """创建会话基本信息卡片"""
        info_card = ctk.CTkFrame(
            parent, 
            corner_radius=12, 
            fg_color=self.modern_colors['white'],
            border_width=1,
            border_color=self.modern_colors['gray_200']
        )
        info_card.pack(fill="x", pady=(0, 20))
        
        # 卡片标题
        title_frame = ctk.CTkFrame(info_card, fg_color=self.modern_colors['card_blue'], corner_radius=8)
        title_frame.pack(fill="x", padx=20, pady=(20, 15))
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="📋 会话基本信息",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.modern_colors['text_primary']
        )
        title_label.pack(pady=12)
        
        # 信息内容
        info_content = ctk.CTkFrame(info_card, fg_color="transparent")
        info_content.pack(fill="x", padx=30, pady=(0, 25))
        
        info_items = [
            ("⏰", "开始时间", self.analytics.session_start.strftime('%Y年%m月%d日 %H:%M:%S')),
            ("⏱️", "结束时间", datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')),
            ("📅", "学习时长", f"{summary['duration']} 分钟"),
            ("🔍", "检测次数", f"{summary['total_behaviors']} 次"),
            ("📊", "平均专注度", f"{summary['avg_concentration']} 分")
        ]
        
        for icon, label, value in info_items:
            item_frame = ctk.CTkFrame(info_content, fg_color="transparent", height=30)
            item_frame.pack(fill="x", pady=3)
            item_frame.pack_propagate(False)
            
            icon_label = ctk.CTkLabel(item_frame, text=icon, font=ctk.CTkFont(size=14))
            icon_label.pack(side="left", padx=(0, 10))
            
            label_text = ctk.CTkLabel(
                item_frame, 
                text=f"{label}:", 
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.modern_colors['text_secondary']
            )
            label_text.pack(side="left")
            
            value_text = ctk.CTkLabel(
                item_frame, 
                text=value, 
                font=ctk.CTkFont(size=12),
                text_color=self.modern_colors['text_primary']
            )
            value_text.pack(side="left", padx=(10, 0))
    
    def create_key_findings_card(self, parent, summary, trend_data):
        """创建主要发现卡片"""
        findings_card = ctk.CTkFrame(
            parent, 
            corner_radius=12, 
            fg_color=self.modern_colors['white'],
            border_width=1,
            border_color=self.modern_colors['gray_200']
        )
        findings_card.pack(fill="x", pady=(0, 20))
        
        # 卡片标题
        title_frame = ctk.CTkFrame(findings_card, fg_color=self.modern_colors['card_green'], corner_radius=8)
        title_frame.pack(fill="x", padx=20, pady=(20, 15))
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="🎯 主要发现",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.modern_colors['text_primary']
        )
        title_label.pack(pady=12)
        
        # 发现内容
        findings_content = ctk.CTkFrame(findings_card, fg_color="transparent")
        findings_content.pack(fill="x", padx=30, pady=(0, 25))
        
        findings_items = [
            ("🎯", "主导行为", summary['dominant_behavior']),
            ("📈", "专注度趋势", trend_data['trend']),
            ("📊", "近期平均专注度", f"{trend_data['recent_avg']} 分")
        ]
        
        for icon, label, value in findings_items:
            item_frame = ctk.CTkFrame(findings_content, fg_color="transparent", height=30)
            item_frame.pack(fill="x", pady=3)
            item_frame.pack_propagate(False)
            
            icon_label = ctk.CTkLabel(item_frame, text=icon, font=ctk.CTkFont(size=14))
            icon_label.pack(side="left", padx=(0, 10))
            
            label_text = ctk.CTkLabel(
                item_frame, 
                text=f"{label}:", 
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.modern_colors['text_secondary']
            )
            label_text.pack(side="left")
            
            value_text = ctk.CTkLabel(
                item_frame, 
                text=value, 
                font=ctk.CTkFont(size=12),
                text_color=self.modern_colors['text_primary']
            )
            value_text.pack(side="left", padx=(10, 0))
    
    def create_concentration_analysis_card(self, parent, trend_data):
        """创建专注度分析卡片"""
        analysis_card = ctk.CTkFrame(
            parent, 
            corner_radius=12, 
            fg_color=self.modern_colors['white'],
            border_width=1,
            border_color=self.modern_colors['gray_200']
        )
        analysis_card.pack(fill="x", pady=(0, 20))
        
        # 卡片标题
        title_frame = ctk.CTkFrame(analysis_card, fg_color=self.modern_colors['card_orange'], corner_radius=8)
        title_frame.pack(fill="x", padx=20, pady=(20, 15))
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="📊 专注度分析",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.modern_colors['text_primary']
        )
        title_label.pack(pady=12)
        
        # 分析内容
        analysis_content = ctk.CTkFrame(analysis_card, fg_color="transparent")
        analysis_content.pack(fill="x", padx=30, pady=(0, 25))
        
        analysis_items = [
            ("📈", "当前专注度趋势", trend_data['trend']),
            ("📊", "与之前相比变化", f"{trend_data['change']:+.1f} 分"),
            ("⭐", "近期平均水平", f"{trend_data['recent_avg']} 分")
        ]
        
        for icon, label, value in analysis_items:
            item_frame = ctk.CTkFrame(analysis_content, fg_color="transparent", height=30)
            item_frame.pack(fill="x", pady=3)
            item_frame.pack_propagate(False)
            
            icon_label = ctk.CTkLabel(item_frame, text=icon, font=ctk.CTkFont(size=14))
            icon_label.pack(side="left", padx=(0, 10))
            
            label_text = ctk.CTkLabel(
                item_frame, 
                text=f"{label}:", 
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.modern_colors['text_secondary']
            )
            label_text.pack(side="left")
            
            value_text = ctk.CTkLabel(
                item_frame, 
                text=value, 
                font=ctk.CTkFont(size=12),
                text_color=self.modern_colors['text_primary']
            )
            value_text.pack(side="left", padx=(10, 0))
    
    def create_suggestions_card(self, parent, summary, trend_data):
        """创建学习建议卡片"""
        suggestions_card = ctk.CTkFrame(
            parent, 
            corner_radius=12, 
            fg_color=self.modern_colors['white'],
            border_width=1,
            border_color=self.modern_colors['gray_200']
        )
        suggestions_card.pack(fill="x", pady=(0, 20))
        
        # 卡片标题
        title_frame = ctk.CTkFrame(suggestions_card, fg_color=self.modern_colors['card_pink'], corner_radius=8)
        title_frame.pack(fill="x", padx=20, pady=(20, 15))
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="🎯 学习建议",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.modern_colors['text_primary']
        )
        title_label.pack(pady=12)
        
        # 建议内容
        suggestions_content = ctk.CTkFrame(suggestions_card, fg_color="transparent")
        suggestions_content.pack(fill="x", padx=30, pady=(0, 25))
        
        # 根据数据生成个性化建议
        suggestions = []
        
        if summary['avg_concentration'] >= 80:
            suggestions.append(("✅", "专注度表现优秀，请继续保持！"))
        elif summary['avg_concentration'] >= 60:
            suggestions.append(("⚠️", "专注度一般，建议调整学习环境，减少干扰因素。"))
        else:
            suggestions.append(("🔴", "专注度较低，建议短时间学习，多次休息。"))
        
        if trend_data['trend'] == "显著下降":
            suggestions.append(("📉", "注意到专注度下降趋势，建议适当休息调整。"))
        elif trend_data['trend'] == "显著上升":
            suggestions.append(("📈", "专注度呈上升趋势，学习状态良好！"))
        
        suggestions.extend([
            ("⏰", "建议优化时间管理，合理安排学习和休息。"),
            ("🌟", "保持良好的学习环境，减少干扰因素。")
        ])
        
        for icon, suggestion in suggestions:
            item_frame = ctk.CTkFrame(suggestions_content, fg_color="transparent")
            item_frame.pack(fill="x", pady=5, anchor="w")
            
            icon_label = ctk.CTkLabel(item_frame, text=icon, font=ctk.CTkFont(size=14))
            icon_label.pack(side="left", padx=(0, 10))
            
            suggestion_text = ctk.CTkLabel(
                item_frame, 
                text=suggestion, 
                font=ctk.CTkFont(size=12),
                text_color=self.modern_colors['text_primary'],
                wraplength=500,
                justify="left"
            )
            suggestion_text.pack(side="left", fill="x", expand=True)
    
    def generate_detailed_report(self):
        """生成详细的学习分析报告"""
        try:
            summary = self.analytics.get_session_summary()
            distribution = self.analytics.get_behavior_distribution()
            trend_data = self.analytics.get_concentration_trend()
            
            report = f"""📊 学习状态详细分析报告
{'='*50}

📋 会话基本信息
• 开始时间: {self.analytics.session_start.strftime('%Y-%m-%d %H:%M:%S')}
• 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• 学习时长: {summary['duration']} 分钟
• 检测次数: {summary['total_behaviors']} 次
• 平均专注度: {summary['avg_concentration']} 分

🎯 主要发现
• 主导行为: {summary['dominant_behavior']}
• 专注度趋势: {trend_data['trend']}
• 近期平均专注度: {trend_data['recent_avg']} 分

📈 行为分布详情
"""
            
            if distribution:
                for behavior, data in distribution.items():
                    if data['count'] > 0:
                        report += f"• {behavior}: {data['count']} 次 ({data['percentage']}%)\n"
            else:
                report += "• 暂无行为数据\n"
            
            report += f"""

📊 专注度分析
• 当前专注度趋势: {trend_data['trend']}
• 与之前相比变化: {trend_data['change']:+.1f} 分
• 近期平均水平: {trend_data['recent_avg']} 分

🎯 学习建议
"""
            
            # 基于数据生成个性化建议
            if summary['avg_concentration'] >= 80:
                report += "• 专注度表现优秀，请继续保持！\n"
            elif summary['avg_concentration'] >= 60:
                report += "• 专注度一般，建议调整学习环境，减少干扰因素。\n"
            else:
                report += "• 专注度较低，建议短时间学习，多次休息。\n"
            
            if trend_data['trend'] == "显著下降":
                report += "• 注意到专注度下降趋势，建议适当休息调整。\n"
            elif trend_data['trend'] == "显著上升":
                report += "• 专注度呈上升趋势，学习状态良好！\n"
            
            report += f"""
• 建议优化时间管理，合理安排学习和休息。
• 保持良好的学习环境，减少干扰因素。

📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            return report
            
        except Exception as e:
            print(f"生成详细报告失败: {e}")
            return "报告生成失败，请稍后重试。"
    
    def load_analysis_data(self):
        """加载分析数据"""
        try:
            # 这里可以添加数据加载逻辑
            # 目前直接使用传入的analytics数据
            pass
        except Exception as e:
            print(f"加载分析数据失败: {e}")
    
    def on_closing(self):
        """窗口关闭时的清理操作"""
        try:
            # 清理matplotlib图表内存
            if VISUALIZATION_AVAILABLE:
                plt.close('all')
            
            # 强制垃圾回收
            gc.collect()
            
            # 销毁窗口
            self.destroy()
            
        except Exception as e:
            print(f"关闭分析窗口时出错: {e}")
            self.destroy()



class MultimediaAssistantApp(ctk.CTk):
    def __init__(self):
        """初始化应用程序 - 添加安全防护避免segmentation fault"""
        try:
            print("🔧 正在初始化MultimediaAssistantApp...")
            super().__init__()
            print("✅ 基础CTk窗口初始化成功")

            # 基本窗口设置
            try:
                self.title("学习伙伴AI助手")
                self.geometry("1200x800")
                print("✅ 窗口基本属性设置成功")
            except Exception as window_error:
                print(f"⚠️ 窗口属性设置失败: {window_error}")

            # 初始化基本数据结构
            try:
                self.message_queue = queue.Queue()
                self.observation_history = []
                self.max_observation_history = 50  # 减少历史记录以节省内存
                print("✅ 基本数据结构初始化成功")
            except Exception as data_error:
                print(f"⚠️ 数据结构初始化失败: {data_error}")

            # Initialize webcam handler with error handling
            try:
                self.webcam_handler = WebcamHandler(self)
                print("✅ 摄像头处理器初始化成功")
            except Exception as webcam_error:
                print(f"⚠️ 摄像头处理器初始化失败: {webcam_error}")
                # 创建一个空的处理器以避免后续错误
                self.webcam_handler = None

        except Exception as init_error:
            print(f"❌ MultimediaAssistantApp初始化严重错误: {init_error}")
            import traceback
            traceback.print_exc()
            raise

        # 学生状态同步变量 - 新增
        self.current_student_status = {
            "behavior": "正常学习",
            "behavior_num": "1", 
            "attention_level": "集中",
            "last_detected_behavior": "",
            "last_analysis_time": None,
            "current_activity": "待检测",
            "concentration_score": 85,
            "behavior_history": [],
            "warning_count": 0,
            "session_start_time": datetime.now()
        }
        
        # 行为上下文存储
        self.current_behavior_context = ""
        self.recent_behaviors = []  # 存储最近的行为记录
        self.max_behavior_history = 10  # 保留最近10条行为记录

        # 初始化学习计划管理器
        try:
            self.learning_plan_manager = LearningPlanManager()
            print("✅ 学习计划管理器初始化成功")
        except Exception as plan_error:
            print(f"⚠️ 学习计划管理器初始化失败: {plan_error}")
            self.learning_plan_manager = None
        
        # 初始化音频播放器
        self.audio_player = AudioPlayer(self)
        self.is_playing_audio = False
        
        # 初始化学习分析系统 - 新增
        self.learning_analytics = LearningAnalytics()
        self.analysis_window = None  # 分析窗口引用

        # 初始化番茄钟组件
        try:
            self.pomodoro_timer = PomodoroTimer(self)
            print("✅ 番茄钟组件初始化成功")
        except Exception as pomodoro_error:
            print(f"⚠️ 番茄钟组件初始化失败: {pomodoro_error}")
            self.pomodoro_timer = None
        
        # 初始化DeepSeek问答客户端
        try:
            self._init_qa_deepseek_client()
            print("DeepSeek问答客户端初始化完成")
        except Exception as e:
            print(f"DeepSeek问答客户端初始化失败: {e}")
            self.qa_deepseek_client = None
        
        # 初始化独立的问答DeepSeek客户端
        self._init_qa_deepseek_client()

        # 初始化题目识别系统 - 延迟到DeepSeek客户端初始化完成后
        self.question_system = None
        self._init_question_system()

        # 内存管理
        self.memory_threshold_mb = 400  # 适当提高内存阈值，避免过于频繁清理
        self.memory_check_interval = 60000  # 内存检查间隔，毫秒
        perform_memory_cleanup()  # 启动时先执行一次清理
        
        # 设置定期内存检查和清理
        self.after(self.memory_check_interval, self.check_and_cleanup_memory)
        
        # Setup UI
        self.setup_ui()
        self.setup_key_bindings()
        
        # Start processing thread
        self.start_processing_thread()
        
        # Start webcam
        # 启动TTS线程
        self.audio_player.start_tts_thread()
        
        # Start webcam
        self.start_webcam()
        
        # 播放欢迎语音
        self.after(2000, self._play_welcome_message)
    
    def _play_welcome_message(self):
        """播放欢迎语音 - 使用人性化版本"""
        try:
            # 使用新的人性化欢迎消息
            try:
                from behavior_prompts import get_welcome_message
                welcome_text = get_welcome_message()
            except ImportError:
                welcome_text = WELCOME_MESSAGE

            self.audio_player.play_text(welcome_text, priority=1)
            # 添加欢迎信息到界面
            self.add_ai_message(INTRO_MESSAGE)
        except Exception as welcome_error:
            print(f"⚠️ 播放欢迎消息失败: {welcome_error}")

    def _delayed_webcam_start(self):
        """延迟启动摄像头以避免初始化冲突"""
        try:
            if self.webcam_handler:
                self.start_webcam()
                print("✅ 摄像头延迟启动成功")
            else:
                print("⚠️ 摄像头处理器未初始化，跳过启动")
        except Exception as webcam_error:
            print(f"⚠️ 摄像头延迟启动失败: {webcam_error}")

    def start_webcam(self):
        """Start webcam capture"""
        success = self.webcam_handler.start()
        if not success:
            print("摄像头启动失败，将禁用所有摄像头相关功能")
            self.update_status("摄像头未启动 - 其他功能仍可使用")
            
            # 在UI中显示一条消息
            message = "摄像头未能成功启动。可能原因：\n\n1. 没有授予摄像头访问权限\n2. 摄像头被其他应用程序占用\n3. 系统没有检测到摄像头\n\n请在系统偏好设置>安全性与隐私>摄像头中检查访问权限，然后重启应用程序。"
            self.add_ai_message(message)
            return False
        
        return True
    
    def setup_ui(self):
        """设置DeepSeek风格的简洁现代界面"""
        self.title("睿课云眸 - AI学习监督系统")
        self.geometry("1600x900")
        self.minsize(1400, 800)
        
        # 设置浅色主题
        ctk.set_appearance_mode("light")
        
        # 创建主容器
        self.main_container = ctk.CTkFrame(
            self,
            corner_radius=0,
            fg_color=("#ffffff", "#ffffff"),
            border_width=0
        )
        self.main_container.pack(fill="both", expand=True)
        
        # 创建简洁的顶部标题栏
        self.setup_title_bar()
        
        # 初始化消息列表 - 必须在UI设置之前
        self.qa_messages = []
        self.supervision_messages = []
        self.messages = []
        self.placeholder_map = {}
        
        # 创建主工作区
        self.setup_workspace()
        
        # 创建底部状态栏
        self.setup_status_bar()
        
        # 初始化消息列表
        if not hasattr(self, 'messages'):
            self.messages = []
        if not hasattr(self, 'qa_messages'):
            self.qa_messages = []
        if not hasattr(self, 'supervision_messages'):
            self.supervision_messages = []

        # 初始化占位符管理的线程安全机制
        import threading
        if not hasattr(self, 'placeholder_lock'):
            self.placeholder_lock = threading.RLock()  # 使用可重入锁
        if not hasattr(self, 'qa_placeholders'):
            self.qa_placeholders = {}
            
        # 验证状态同步系统完整性
        self.verify_status_sync_integrity()
        
        # 添加欢迎消息
        self.add_initial_messages()

        # 初始化学习目标显示
        self.update_goal_display()
        
    def setup_title_bar(self):
        """设置现代化顶部标题栏 - 优化学习目标显示和标题居中"""
        title_bar = ctk.CTkFrame(
            self.main_container,
            corner_radius=0,
            fg_color=("#f8fafc", "#1e293b"),  # 更柔和的背景色
            height=85,  # 进一步增加高度以适应更大的文本元素
            border_width=0
        )
        title_bar.pack(fill="x", side="top")
        title_bar.pack_propagate(False)

        # 创建渐变背景效果的容器 - 增强视觉层次
        gradient_frame = ctk.CTkFrame(
            title_bar,
            corner_radius=0,
            fg_color="transparent",
            border_width=0
        )
        gradient_frame.pack(fill="both", expand=True)

        # 主要内容容器 - 水平布局
        main_content = ctk.CTkFrame(
            gradient_frame,
            corner_radius=0,
            fg_color="transparent"
        )
        main_content.pack(expand=True, fill="both")

        # 左侧：学习目标显示区域 - 增强视觉突出度设计
        self.goal_display_frame = ctk.CTkFrame(
            main_content,
            corner_radius=12,  # 增加圆角，更现代化
            fg_color=("#c7d2fe", "#1e40af"),  # 更深的蓝色背景，增强对比度
            border_width=3,  # 增加边框宽度，提升视觉权重
            border_color=("#6366f1", "#60a5fa"),  # 更亮的边框色，增强突出度
            height=85   # 精确调整为与番茄钟组件相同的高度
        )
        self.goal_display_frame.pack(side="left", padx=(25, 20), pady=11)

        # 学习目标显示标签 - 增强视觉突出度的双行文本显示
        self.goal_display_label = ctk.CTkLabel(
            self.goal_display_frame,
            text="📋 暂无学习目标",
            font=ctk.CTkFont(size=15, weight="bold"),  # 优化字体大小确保文字完整显示
            text_color=("#1e3a8a", "#f1f5f9"),  # 更深的文字色，增强对比度
            anchor="w",  # 左对齐
            justify="left"   # 左对齐多行文本
        )
        self.goal_display_label.pack(padx=16, pady=12, anchor="w")  # 优化内边距确保文字完整显示

        # 更新显示框宽度以适应内容
        self.update_goal_frame_width()

        # 中央：Logo和标题组合 - 使用place确保绝对居中
        center_frame = ctk.CTkFrame(
            main_content,
            corner_radius=0,
            fg_color="transparent"
        )
        center_frame.place(relx=0.5, rely=0.5, anchor="center")

        # 品牌Logo图标 - 增强视觉效果
        logo_label = ctk.CTkLabel(
            center_frame,
            text="🧠",
            font=ctk.CTkFont(size=34, weight="bold"),  # 进一步增大Logo
            text_color=("#6366f1", "#8b5cf6")
        )
        logo_label.pack(side="left", padx=(0, 12))

        # 主标题 - 增强视觉权重
        title_label = ctk.CTkLabel(
            center_frame,
            text="睿课云眸",
            font=ctk.CTkFont(family="PingFang SC", size=30, weight="bold"),  # 进一步增大字体
            text_color=("#1f2937", "#f9fafb")
        )
        title_label.pack(side="left")

        # 副标题 - 优化视觉层次
        subtitle_label = ctk.CTkLabel(
            center_frame,
            text=" AI学习监督系统",
            font=ctk.CTkFont(family="PingFang SC", size=17, weight="normal"),  # 进一步增大副标题
            text_color=("#6b7280", "#9ca3af")
        )
        subtitle_label.pack(side="left", padx=(6, 0))

        # 右侧：番茄钟组件和状态指示器容器
        right_container = ctk.CTkFrame(
            main_content,
            corner_radius=0,
            fg_color="transparent"
        )
        right_container.pack(side="right", padx=(20, 25), pady=8)

        # 番茄钟组件
        if hasattr(self, 'pomodoro_timer') and self.pomodoro_timer:
            try:
                pomodoro_widget = self.pomodoro_timer.create_ui(right_container)
                if pomodoro_widget:
                    pomodoro_widget.pack(side="left", padx=(0, 15))
                    print("✅ 番茄钟组件已添加到标题栏")
            except Exception as e:
                print(f"⚠️ 添加番茄钟组件到标题栏失败: {e}")

        # 状态指示器 - 保持原有功能
        status_indicator = ctk.CTkFrame(
            right_container,
            width=14,  # 稍微增大尺寸
            height=14,
            corner_radius=7,
            fg_color=("#059669", "#10b981"),  # 更深的绿色，增强对比度
            border_width=2,  # 添加边框
            border_color=("#34d399", "#6ee7b7")  # 亮绿色边框
        )
        status_indicator.pack(side="right", pady=20)
        status_indicator.pack_propagate(False)

    def update_goal_frame_width(self):
        """动态调整学习目标显示框宽度以适应内容"""
        try:
            if hasattr(self, 'goal_display_label') and hasattr(self, 'goal_display_frame'):
                # 获取当前文本内容
                text = self.goal_display_label.cget("text")
                if text:
                    # 计算文本所需的大致宽度
                    # 考虑双行显示，取较长行的宽度
                    lines = text.split('\n')
                    max_line_length = max(len(line) for line in lines) if lines else 0

                    # 基础宽度计算（每个字符约8.5像素，加上增强的内边距）
                    base_width = max_line_length * 8.5 + 35

                    # 设置最小和最大宽度限制
                    min_width = 220  # 增加最小宽度以适应新的视觉设计
                    max_width = 480  # 稍微增加最大宽度

                    # 计算最终宽度
                    final_width = max(min_width, min(base_width, max_width))

                    # 更新显示框宽度
                    self.goal_display_frame.configure(width=final_width)
        except Exception as e:
            print(f"更新目标显示框宽度时出错: {e}")

    def setup_workspace(self):
        """设置现代化主工作区域 - 左右分布布局"""
        # 创建主工作区域容器 - 优化背景渐变
        workspace = ctk.CTkFrame(
            self.main_container,
            corner_radius=0,
            fg_color=("#fafbfc", "#0f172a")  # 更柔和的背景渐变
        )
        workspace.pack(fill="both", expand=True, padx=25, pady=(15, 0))
        
        # 配置左右两栏布局：左侧问答(7)，右侧监控区域(3) - 7:3比例
        workspace.grid_columnconfigure(0, weight=7)  # 左侧：聊天对话
        workspace.grid_columnconfigure(1, weight=3)  # 右侧：监控区域
        workspace.grid_rowconfigure(0, weight=1)
        
        # 设置左侧聊天面板
        self.setup_chat_panel(workspace)
        
        # 设置右侧监控区域（包含摄像头和监督）
        self.setup_right_monitoring_area(workspace)
        
    def setup_chat_panel(self, parent):
        """设置现代化左侧聊天对话面板"""
        chat_panel = ctk.CTkFrame(
            parent,
            corner_radius=16,
            fg_color=("#ffffff", "#1e1e2e"),
            border_width=0
        )
        chat_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 15), pady=15)
        
        # 添加阴影效果（通过外层框架模拟）
        shadow_frame = ctk.CTkFrame(
            parent,
            corner_radius=16,
            fg_color=("#e5e7eb", "#0f0f23"),
            border_width=0
        )
        shadow_frame.grid(row=0, column=0, sticky="nsew", padx=(2, 17), pady=(17, 13))
        shadow_frame.lower()  # 置于底层作为阴影
        
        # 重要：确保正确的行权重配置以避免重叠
        chat_panel.grid_rowconfigure(0, weight=0)  # 标题固定高度
        chat_panel.grid_rowconfigure(1, weight=1)  # 聊天容器可扩展
        chat_panel.grid_rowconfigure(2, weight=0)  # 输入区域固定高度
        chat_panel.grid_columnconfigure(0, weight=1)
        
        # 现代化聊天标题
        chat_header = ctk.CTkFrame(
            chat_panel,
            corner_radius=16,
            fg_color=("#6366f1", "#5b21b6"),
            height=55
        )
        chat_header.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        chat_header.grid_propagate(False)
        chat_header.grid_columnconfigure(0, weight=1)
        
        # 标题内容容器
        header_content = ctk.CTkFrame(
            chat_header,
            corner_radius=0,
            fg_color="transparent"
        )
        header_content.grid(row=0, column=0, sticky="ew", padx=20, pady=0)
        header_content.grid_columnconfigure(1, weight=1)
        
        # 聊天图标
        chat_icon = ctk.CTkLabel(
            header_content,
            text="💬",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("#ffffff", "#ffffff")
        )
        chat_icon.grid(row=0, column=0, sticky="w", pady=17)
        
        # 聊天标题
        chat_title = ctk.CTkLabel(
            header_content,
            text="AI学习助手",
            font=ctk.CTkFont(family="PingFang SC", size=18, weight="bold"),  # 增大字体
            text_color=("#ffffff", "#ffffff")
        )
        chat_title.grid(row=0, column=1, sticky="w", padx=(12, 0), pady=17)
        
        # 在线状态指示
        online_indicator = ctk.CTkFrame(
            header_content,
            width=8,
            height=8,
            corner_radius=4,
            fg_color=("#10b981", "#34d399")
        )
        online_indicator.grid(row=0, column=2, sticky="e", pady=21)
        online_indicator.pack_propagate(False)
        
        # 聊天容器 - 现代化设计
        self.qa_chat_container = ctk.CTkScrollableFrame(
            chat_panel,
            corner_radius=0,
            fg_color=("#fafbfc", "#181825"),
            scrollbar_button_color=("#e2e8f0", "#3c4043"),
            scrollbar_button_hover_color=("#cbd5e1", "#5f6368")
        )
        self.qa_chat_container.grid(row=1, column=0, sticky="nsew", padx=8, pady=(12, 25))
        self.qa_chat_container.grid_columnconfigure(0, weight=1)
        
        # 输入区域
        self.setup_input_area(chat_panel)
    
    def setup_right_monitoring_area(self, parent):
        """设置现代化右侧监控区域（包含摄像头和监督面板）"""
        right_panel = ctk.CTkFrame(
            parent,
            corner_radius=0,
            fg_color="transparent"
        )
        right_panel.grid(row=0, column=1, sticky="nsew", padx=(15, 0), pady=15)
        right_panel.grid_rowconfigure(0, weight=0)  # 摄像头区域固定高度，不拉伸
        right_panel.grid_rowconfigure(1, weight=1)  # 监督区域可扩展填充剩余空间
        right_panel.grid_columnconfigure(0, weight=1)
        
        # 上方摄像头监控面板
        self.setup_camera_area(right_panel)
        
        # 下方监督反馈面板
        self.setup_supervision_area(right_panel)
        
    def setup_input_area(self, parent):
        """设置现代化聊天输入区域"""
        input_frame = ctk.CTkFrame(
            parent,
            corner_radius=16,
            fg_color=("#f8fafc", "#181825"),
            height=95  # 增加高度以适应更大的输入框
        )
        input_frame.grid(row=2, column=0, sticky="ew", padx=0, pady=(20, 0))
        input_frame.grid_propagate(False)
        input_frame.grid_columnconfigure(0, weight=1)
        
        # 输入框和按钮容器
        input_container = ctk.CTkFrame(
            input_frame,
            corner_radius=0,
            fg_color="transparent"
        )
        input_container.grid(row=0, column=0, sticky="ew", padx=20, pady=15)
        input_container.grid_columnconfigure(0, weight=1)
        
        # 超现代化输入框设计 - 增大尺寸和字体
        self.qa_input = ctk.CTkEntry(
            input_container,
            placeholder_text="✨ 输入您的问题，AI将为您提供专业解答...",
            font=ctk.CTkFont(family="PingFang SC", size=16),  # 增大字体从14到16
            height=56,  # 增大高度从46到56
            corner_radius=28,  # 相应调整圆角
            border_width=3,
            border_color=("#3b82f6", "#6366f1"),
            fg_color=("#dbeafe", "#1e293b"),  # 使用更柔和的蓝色背景
            text_color=("#0f172a", "#f1f5f9"),
            placeholder_text_color=("#6b7280", "#94a3b8")
        )
        self.qa_input.grid(row=0, column=0, sticky="ew", padx=(0, 8))

        # 题目拍照识别按钮 - 新增功能
        self.qa_camera_button = ctk.CTkButton(
            input_container,
            text="📷",
            width=56,  # 与发送按钮相同尺寸
            height=56,  # 与发送按钮相同高度
            font=ctk.CTkFont(size=18, weight="bold"),
            command=self.capture_question_image,
            fg_color=("#dbeafe", "#374151"),  # 使用软蓝色主题
            hover_color=("#bfdbfe", "#4b5563"),
            text_color=("#3b82f6", "#93c5fd"),
            corner_radius=28,
            border_width=0  # 无边框设计
        )
        self.qa_camera_button.grid(row=0, column=1, padx=(0, 8))

        # 超现代化发送按钮 - 增大尺寸匹配输入框
        self.qa_send_button = ctk.CTkButton(
            input_container,
            text="🚀",
            width=56,  # 增大宽度匹配输入框高度
            height=56,  # 增大高度匹配输入框高度
            font=ctk.CTkFont(size=20, weight="bold"),  # 增大字体
            command=self.send_deepseek_message,
            fg_color=("#3b82f6", "#6366f1"),
            hover_color=("#2563eb", "#4f46e5"),
            text_color=("#ffffff", "#ffffff"),
            corner_radius=28,  # 相应调整圆角
            border_width=2,
            border_color=("#1d4ed8", "#3730a3")
        )
        self.qa_send_button.grid(row=0, column=2)
        
        # 绑定回车发送
        self.qa_input.bind("<Return>", lambda event: self.send_deepseek_message())
        

        
    def setup_camera_area(self, parent):
        """设置现代化右上方摄像头监控区域"""
        camera_panel = ctk.CTkFrame(
            parent,
            corner_radius=16,
            fg_color=("#ffffff", "#1e1e2e"),
            border_width=0
        )
        camera_panel.grid(row=0, column=0, sticky="nsew", padx=0, pady=(0, 15))
        
        # 添加阴影效果
        shadow_frame = ctk.CTkFrame(
            parent,
            corner_radius=16,
            fg_color=("#e5e7eb", "#0f0f23"),
            border_width=0
        )
        shadow_frame.grid(row=0, column=0, sticky="nsew", padx=2, pady=(2, 13))
        shadow_frame.lower()
        
        camera_panel.grid_rowconfigure(1, weight=1)
        camera_panel.grid_columnconfigure(0, weight=1)
        
        # 现代化摄像头标题
        camera_header = ctk.CTkFrame(
            camera_panel,
            corner_radius=16,
            fg_color=("#10b981", "#059669"),
            height=50
        )
        camera_header.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        camera_header.grid_propagate(False)
        camera_header.grid_columnconfigure(0, weight=1)
        
        # 标题内容容器
        header_content = ctk.CTkFrame(
            camera_header,
            corner_radius=0,
            fg_color="transparent"
        )
        header_content.grid(row=0, column=0, sticky="ew", padx=15, pady=0)
        header_content.grid_columnconfigure(1, weight=1)
        
        # 摄像头图标
        camera_icon = ctk.CTkLabel(
            header_content,
            text="📹",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("#ffffff", "#ffffff")
        )
        camera_icon.grid(row=0, column=0, sticky="w", pady=15)
        
        # 摄像头标题
        camera_title = ctk.CTkLabel(
            header_content,
            text="实时监控",
            font=ctk.CTkFont(family="PingFang SC", size=16, weight="bold"),  # 增大字体
            text_color=("#ffffff", "#ffffff")
        )
        camera_title.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=15)
        
        # 录制状态指示
        record_indicator = ctk.CTkFrame(
            header_content,
            width=8,
            height=8,
            corner_radius=4,
            fg_color=("#ef4444", "#f87171")
        )
        record_indicator.grid(row=0, column=2, sticky="e", pady=19)
        record_indicator.pack_propagate(False)
        
        # 现代化摄像头预览容器
        preview_container = ctk.CTkFrame(
            camera_panel,
            corner_radius=12,
            fg_color=("#f1f5f9", "#0f172a"),
            border_width=3,
            border_color=("#10b981", "#059669"),
            width=285,
            height=215
        )
        preview_container.grid(row=1, column=0, sticky="", padx=12, pady=(8, 12))
        preview_container.grid_propagate(False)
        preview_container.grid_columnconfigure(0, weight=1)
        preview_container.grid_rowconfigure(0, weight=1)
        
        self.preview_label = ctk.CTkLabel(
            preview_container,
            text="🎥 摄像头启动中...",
            font=ctk.CTkFont(family="PingFang SC", size=13, weight="bold"),
            text_color=("#64748b", "#94a3b8"),
            fg_color="transparent"
        )
        self.preview_label.grid(row=0, column=0, sticky="nsew", padx=3, pady=3)
        
    def setup_supervision_area(self, parent):
        """设置现代化右下方监督反馈区域"""
        supervision_panel = ctk.CTkFrame(
            parent,
            corner_radius=16,
            fg_color=("#ffffff", "#1e1e2e"),
            border_width=0
        )
        supervision_panel.grid(row=1, column=0, sticky="nsew", padx=0, pady=(15, 0))
        
        # 添加阴影效果
        shadow_frame = ctk.CTkFrame(
            parent,
            corner_radius=16,
            fg_color=("#e5e7eb", "#0f0f23"),
            border_width=0
        )
        shadow_frame.grid(row=1, column=0, sticky="nsew", padx=2, pady=(17, 2))
        shadow_frame.lower()
        
        supervision_panel.grid_rowconfigure(1, weight=1)
        supervision_panel.grid_columnconfigure(0, weight=1)
        
        # 现代化监督标题
        supervision_header = ctk.CTkFrame(
            supervision_panel,
            corner_radius=16,
            fg_color=("#f59e0b", "#d97706"),
            height=50
        )
        supervision_header.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        supervision_header.grid_propagate(False)
        supervision_header.grid_columnconfigure(0, weight=1)
        
        # 标题内容容器
        header_content = ctk.CTkFrame(
            supervision_header,
            corner_radius=0,
            fg_color="transparent"
        )
        header_content.grid(row=0, column=0, sticky="ew", padx=15, pady=0)
        header_content.grid_columnconfigure(1, weight=1)
        
        # 监督图标
        supervision_icon = ctk.CTkLabel(
            header_content,
            text="👀",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("#ffffff", "#ffffff")
        )
        supervision_icon.grid(row=0, column=0, sticky="w", pady=15)
        
        # 监督标题
        supervision_title = ctk.CTkLabel(
            header_content,
            text="学习监督",
            font=ctk.CTkFont(family="PingFang SC", size=16, weight="bold"),  # 增大字体
            text_color=("#ffffff", "#ffffff")
        )
        supervision_title.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=15)
        
        # 活动状态指示
        activity_indicator = ctk.CTkFrame(
            header_content,
            width=8,
            height=8,
            corner_radius=4,
            fg_color=("#10b981", "#34d399")
        )
        activity_indicator.grid(row=0, column=2, sticky="e", pady=19)
        activity_indicator.pack_propagate(False)
        
        # 现代化监督聊天容器
        self.supervision_chat_container = ctk.CTkScrollableFrame(
            supervision_panel,
            corner_radius=0,
            fg_color=("#fefefe", "#181825"),
            scrollbar_button_color=("#e2e8f0", "#3c4043"),
            scrollbar_button_hover_color=("#cbd5e1", "#5f6368")
        )
        self.supervision_chat_container.grid(row=1, column=0, sticky="nsew", padx=8, pady=(8, 8))
        self.supervision_chat_container.grid_columnconfigure(0, weight=1)
    
    def setup_status_bar(self):
        """设置现代化底部状态栏 - 优化布局防止重叠"""
        self.status_bar = ctk.CTkFrame(
            self.main_container,
            corner_radius=0,
            fg_color=("#f1f5f9", "#1e293b"),
            height=58,  # 进一步增加高度以容纳更大的按钮和文本
            border_width=1,
            border_color=("#e2e8f0", "#334155")
        )
        self.status_bar.pack(fill="x", side="bottom")
        self.status_bar.pack_propagate(False)
        
        # 状态栏内容容器 - 使用grid布局更好控制
        status_content = ctk.CTkFrame(
            self.status_bar,
            corner_radius=0,
            fg_color="transparent"
        )
        status_content.pack(fill="both", expand=True, padx=20, pady=0)
        
        # 配置grid权重
        status_content.grid_columnconfigure(0, weight=1)  # 左侧可扩展
        status_content.grid_columnconfigure(1, weight=0)  # 右侧固定
        status_content.grid_rowconfigure(0, weight=1)
        
        # 左侧状态信息容器
        status_left = ctk.CTkFrame(
            status_content,
            corner_radius=0,
            fg_color="transparent"
        )
        status_left.grid(row=0, column=0, sticky="w", padx=(5, 0))
        
        self.status_label = ctk.CTkLabel(
            status_left,
            text="✅ 系统运行正常",
            font=ctk.CTkFont(family="PingFang SC", size=14, weight="bold"),  # 增大字体
            text_color=("#059669", "#10b981")
        )
        self.status_label.pack(side="left", pady=15)

        # 分隔符
        separator = ctk.CTkLabel(
            status_left,
            text="│",
            font=ctk.CTkFont(size=14),  # 增大字体
            text_color=("#d1d5db", "#6b7280")
        )
        separator.pack(side="left", padx=(15, 15), pady=15)

        # 实时状态指示器
        self.realtime_status = ctk.CTkLabel(
            status_left,
            text="🎯 监控中",
            font=ctk.CTkFont(family="PingFang SC", size=13),  # 增大字体
            text_color=("#2563eb", "#3b82f6")
        )
        self.realtime_status.pack(side="left", pady=15)
        
        # 右侧功能区和版本信息 - 使用grid布局防止重叠
        status_right = ctk.CTkFrame(
            status_content,
            corner_radius=0,
            fg_color="transparent"
        )
        status_right.grid(row=0, column=1, sticky="e", padx=(0, 5))
        
        # 配置右侧容器的 grid（登录 / 学习计划 / 历史 / 报告 / 分隔符 / 版本）
        status_right.grid_columnconfigure(0, weight=0)  # 登录按钮
        status_right.grid_columnconfigure(1, weight=0)  # 学习计划按钮
        status_right.grid_columnconfigure(2, weight=0)  # 问答历史按钮
        status_right.grid_columnconfigure(3, weight=0)  # 学习报告按钮
        status_right.grid_columnconfigure(4, weight=0)  # 分隔符
        status_right.grid_columnconfigure(5, weight=0)  # 版本信息
        status_right.grid_rowconfigure(0, weight=1)
        
        # 登录按钮
        self.login_button = ctk.CTkButton(
            status_right,
            text="🔑 登录",
            width=85,  # 稍微增大宽度
            height=36,  # 增大高度
            font=ctk.CTkFont(family="PingFang SC", size=12, weight="bold"),  # 增大字体
            command=self.open_login_page,
            fg_color=("#3b82f6", "#2563eb"),
            hover_color=("#2563eb", "#1d4ed8"),
            corner_radius=16,
            border_width=1,
            border_color=("#1e40af", "#1e3a8a"),
            text_color=("#ffffff", "#ffffff")
        )
        self.login_button.grid(row=0, column=0, padx=(0, 15), pady=7)  # 调整padding
        
        # 学习计划按钮
        self.plan_button = ctk.CTkButton(
            status_right,
            text="📋 学习计划",
            width=105,  # 稍微增大宽度
            height=36,  # 增大高度
            font=ctk.CTkFont(family="PingFang SC", size=12, weight="bold"),  # 增大字体
            command=self.open_learning_plan,
            fg_color=("#10b981", "#059669"),
            hover_color=("#059669", "#047857"),
            corner_radius=16,
            border_width=1,
            border_color=("#047857", "#065f46"),
            text_color=("#ffffff", "#ffffff")
        )
        self.plan_button.grid(row=0, column=1, padx=(0, 15), pady=7)  # 调整padding
        
        # 问答历史按钮
        self.history_button = ctk.CTkButton(
            status_right,
            text="📜 问答历史",
            width=105,  # 稍微增大宽度
            height=36,  # 增大高度
            font=ctk.CTkFont(family="PingFang SC", size=12, weight="bold"),  # 增大字体
            command=self.open_qa_history,
            fg_color=("#3b82f6", "#2563eb"),
            hover_color=("#2563eb", "#1d4ed8"),
            corner_radius=16,
            border_width=1,
            border_color=("#1e40af", "#1e3a8a"),
            text_color=("#ffffff", "#ffffff")
        )
        self.history_button.grid(row=0, column=2, padx=(0, 15), pady=7)  # 调整padding
        
        # 学习分析报告按钮
        self.analysis_button = ctk.CTkButton(
            status_right,
            text="📊 学习报告",
            width=105,  # 稍微增大宽度
            height=36,  # 增大高度
            font=ctk.CTkFont(family="PingFang SC", size=12, weight="bold"),  # 增大字体
            command=self.open_analysis_report,
            fg_color=("#3b82f6", "#2563eb"),
            hover_color=("#2563eb", "#1d4ed8"),
            corner_radius=16,
            border_width=1,
            border_color=("#1e40af", "#1e3a8a"),
            text_color=("#ffffff", "#ffffff")
        )
        self.analysis_button.grid(row=0, column=3, padx=(0, 15), pady=7)  # 调整padding
        
        # 分隔符 - 中间分离
        separator2 = ctk.CTkLabel(
            status_right,
            text="│",
            font=ctk.CTkFont(size=12),
            text_color=("#d1d5db", "#6b7280")
        )
        separator2.grid(row=0, column=4, padx=(0, 15), pady=15)
        
        # 版本信息 - 放在最右边
        version_label = ctk.CTkLabel(
            status_right,
            text="🧠 睿课云眸 v2.2",
            font=ctk.CTkFont(family="PingFang SC", size=11, weight="bold"),
            text_color=("#7c3aed", "#8b5cf6")
        )
        version_label.grid(row=0, column=5, padx=(0, 0), pady=15)
    
    def setup_key_bindings(self):
        """Setup keyboard shortcuts"""
        # keyboard shortcuts are disabled
        pass
    
    def start_processing_thread(self):
        """Start the message processing thread"""
        threading.Thread(target=self.process_message_queue, daemon=True).start()
    
    def process_message_queue(self):
        """Process messages in the queue"""
        while True:
            try:
                message = self.message_queue.get()
                self.handle_message(message)
                self.message_queue.task_done()
            except Exception as e:
                print(f"Error processing message: {e}")
            time.sleep(0.1)
    
    def handle_message(self, message):
        """Handle different types of messages"""
        msg_type = message.get("type")
        content = message.get("content")
        msg_id = message.get("id")
        
        if msg_type == "image_analysis":
            analysis = message.get("analysis")
            image_urls = message.get("image_urls", [])
            screenshots = message.get("screenshots", [])
            self.process_image_analysis(analysis, image_urls, screenshots, msg_id)
    
    def update_preview(self, img):
        """Update preview image safely"""
        try:
            if img is not None and hasattr(self, 'preview_label') and self.preview_label:
                # 安全检查UI组件是否还存在
                try:
                    if self.preview_label.winfo_exists():
                        # 获取预览容器的实际尺寸
                        container_width = self.preview_label.winfo_width()
                        container_height = self.preview_label.winfo_height()
                        
                        # 获取容器实际尺寸，如果容器还没有渲染，使用适中的默认值
                        if container_width <= 1 or container_height <= 1:
                            # 使用适中的默认尺寸，保持7:3左右布局比例
                            target_width, target_height = 270, 200
                        else:
                            # 使用容器实际尺寸，减去适当边距
                            target_width = max(container_width - 10, 270)
                            target_height = max(container_height - 10, 200)
                        
                        img_copy = img.copy()
                        
                        # 计算最佳缩放比例，保持宽高比的同时最大化填充容器
                        img_width, img_height = img_copy.size
                        width_ratio = target_width / img_width
                        height_ratio = target_height / img_height
                        
                        # 选择较大的缩放比例以填满容器（可能会裁剪）
                        scale_ratio = max(width_ratio, height_ratio)
                        
                        # 计算缩放后的尺寸
                        new_width = int(img_width * scale_ratio)
                        new_height = int(img_height * scale_ratio)
                        
                        # 先缩放图像
                        img_copy = img_copy.resize((new_width, new_height), Image.Resampling.LANCZOS)
                        
                        # 如果缩放后的图像比目标尺寸大，进行居中裁剪
                        if new_width > target_width or new_height > target_height:
                            left = max(0, (new_width - target_width) // 2)
                            top = max(0, (new_height - target_height) // 2)
                            right = left + target_width
                            bottom = top + target_height
                            img_copy = img_copy.crop((left, top, right, bottom))
                        
                        # 最终尺寸就是目标尺寸
                        preview_size = (target_width, target_height)
                        
                        # Convert to CTkImage，使用目标尺寸确保填满
                        actual_size = preview_size
                        ctk_img = ctk.CTkImage(
                            light_image=img_copy, 
                            dark_image=img_copy, 
                            size=actual_size
                        )
                        
                        # Update preview label safely
                        self.preview_label.configure(image=ctk_img, text="")
                        
                        # Store reference to prevent garbage collection
                        self.preview_label._preview_image = ctk_img
                except:
                    # 组件不存在或已销毁，忽略错误
                    pass
        except Exception as e:
            # 静默处理错误，避免程序崩溃
            pass

    def add_ai_message(self, text, image=None, is_placeholder=False, placeholder_id=None, is_system_notification=False):
        """Add modern AI message to chat container"""
        try:
            # 根据消息类型确定现代化样式
            if is_system_notification:
                frame_color = ("#fef7ff", "#2d1b2f")
                border_color = ("#a855f7", "#7c3aed")
                header_text = "🔔 系统通知"
                header_color = ("#8b5cf6", "#a78bfa")
                icon = "🔔"
                stick_side = "w"
                padx_config = (20, 60)
            else:
                frame_color = ("#f0f9ff", "#0c1626")
                border_color = ("#0ea5e9", "#0284c7")
                icon = "🤖"
                if is_placeholder:
                    header_text = "AI思考中..."
                    header_color = ("#0ea5e9", "#38bdf8")
                else:
                    header_text = "AI助手"
                    header_color = ("#0284c7", "#0ea5e9")
                stick_side = "w"
                padx_config = (20, 60)
            
            # Create message frame with improved styling - 检查是否使用监督聊天容器
            if is_system_notification:
                parent_container = self.supervision_chat_container
            else:
                parent_container = self.qa_chat_container
                
            # 现代化消息框架设计
            msg_frame = ctk.CTkFrame(
                parent_container, 
                corner_radius=18,
                fg_color=frame_color,
                border_width=2,
                border_color=border_color
            )
            
            # 统一使用grid布局避免冲突 - 安全地获取行号
            if not hasattr(self, 'messages'):
                self.messages = []
            row_num = len(self.messages)
            msg_frame.grid(row=row_num, column=0, sticky="ew", padx=padx_config, pady=(12, 8))
            parent_container.grid_columnconfigure(0, weight=1)
            msg_frame.grid_columnconfigure(0, weight=1)
                
            # 现代化标题栏
            header_frame = ctk.CTkFrame(
                msg_frame,
                corner_radius=0,
                fg_color="transparent"
            )
            header_frame.grid(row=0, column=0, sticky="ew", padx=18, pady=(15, 8))
            header_frame.grid_columnconfigure(1, weight=1)
                
            # 图标
            icon_label = ctk.CTkLabel(
                header_frame,
                text=icon,
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=header_color
            )
            icon_label.grid(row=0, column=0, sticky="w")
                
            # 标题文字
            header = ctk.CTkLabel(
                header_frame, 
                text=header_text,
                font=ctk.CTkFont(family="PingFang SC", size=13, weight="bold"),
                text_color=header_color
            )
            header.grid(row=0, column=1, sticky="w", padx=(8, 0))
            
            # 时间戳
            timestamp = ctk.CTkLabel(
                header_frame,
                text=datetime.now().strftime("%H:%M"),
                font=ctk.CTkFont(family="PingFang SC", size=10),
                text_color=("#94a3b8", "#64748b")
            )
            timestamp.grid(row=0, column=2, sticky="e")
            
            # Add image if provided
            img_label = None
            if image is not None:
                # 创建图像容器框架
                img_container = ctk.CTkFrame(
                    msg_frame
                )
                img_container.grid(row=1, column=0, sticky="ew", pady=(0, 10), padx=15)
                img_container.grid_columnconfigure(0, weight=1)
                
                # Resize image for display
                display_size = (200, 150)
                img_copy = image.copy()
                img_copy.thumbnail(display_size)
                    
                # Convert to CTkImage
                ctk_img = ctk.CTkImage(light_image=img_copy, dark_image=img_copy, size=display_size)
                
                # Create and grid image label
                img_label = ctk.CTkLabel(img_container, image=ctk_img, text="")
                img_label.grid(row=0, column=0)
                
                # Store reference to prevent garbage collection
                img_label._ai_image = ctk_img
                    
            # 现代化内容容器
            content_container = ctk.CTkFrame(
                msg_frame,
                corner_radius=12,
                fg_color=("#ffffff", "#1a1a2e")
            )
            content_row = 2 if image else 1
            content_container.grid(row=content_row, column=0, sticky="ew", padx=18, pady=(0, 18))
            content_container.grid_columnconfigure(0, weight=1)
            
            # 现代化文本标签设计
            text_label = ctk.CTkLabel(
                content_container,
                text=text,
                wraplength=480,
                justify="left",
                font=ctk.CTkFont(family="PingFang SC", size=14, weight="normal"),  # 增大字体
                text_color=("#1f2937", "#e5e7eb"),
                anchor="nw"
            )
            text_label.grid(row=0, column=0, sticky="ew", padx=15, pady=12)
            
            # Store message data for reference
            message_data = {
                "frame": msg_frame,
                "text": text,
                "text_label": text_label,
                "image": image,
                "image_label": img_label,
                "is_placeholder": is_placeholder,
                "placeholder_id": placeholder_id,
                "header": header,
                "role": "assistant",
                "content": text,
                "timestamp": datetime.now()
            }
            
            self.messages.append(message_data)
            
            # Store placeholder mapping for later update
            if is_placeholder and placeholder_id:
                if not hasattr(self, "placeholder_map"):
                    self.placeholder_map = {}
                self.placeholder_map[placeholder_id] = len(self.messages) - 1  # 存储消息索引
                
            # Scroll to bottom
            self.scroll_to_bottom()
            
            return message_data
        except Exception as e:
            print(f"Error adding AI message: {e}")
            return None
    
    def update_placeholder(self, placeholder_id, text, screenshots=None, behavior_info=None):
        """Update a placeholder message with analysis results"""
        try:
            if not hasattr(self, "placeholder_map") or placeholder_id not in self.placeholder_map:
                print(f"警告: 找不到占位符 {placeholder_id}")
                return False
                
            msg_index = self.placeholder_map[placeholder_id]
            message_data = self.messages[msg_index]
            
            # Update text
            message_data["text"] = text
            message_data["text_label"].configure(text=text)
            
            # 更新标题从"AI分析中..."变为"AI分析结果"
            if "header" in message_data:
                message_data["header"].configure(text="AI分析结果")
            
            # Update image if provided
            if screenshots and len(screenshots) > 0 and message_data["image_label"]:
                display_size = (300, 225)
                img_copy = screenshots[0].copy()
                img_copy.thumbnail(display_size)
                
                ctk_img = ctk.CTkImage(light_image=img_copy, dark_image=img_copy, size=display_size)
                message_data["image_label"].configure(image=ctk_img)
                message_data["image_label"].image = ctk_img
            
            # 添加一个微小的动画效果，吸引注意力
            frame = message_data["frame"]
            original_color = frame.cget("fg_color")
            
            # 闪烁效果函数
            def flash_effect(count=0):
                if count >= 4:  # 完成两次闪烁
                    frame.configure(fg_color=original_color)
                    return
                
                # 在亮色和原色之间切换
                if count % 2 == 0:
                    highlight_color = ("#e6f0ff", "#3a4554") if ctk.get_appearance_mode() == "Dark" else ("#e6f0ff", "#3a4554")
                    frame.configure(fg_color=highlight_color)
                else:
                    frame.configure(fg_color=original_color)
                
                # 每隔300毫秒调用一次
                self.after(300, lambda: flash_effect(count + 1))
            
            # 开始闪烁效果
            flash_effect()
            
            # Ensure updated message is visible
            if hasattr(self, 'qa_chat_container'):
                try:
                    self.qa_chat_container.update_idletasks()
                except:
                    pass
            
            # 播放与分析结果对应的语音提示
            if behavior_info and "voice_prompt" in behavior_info:
                voice_prompt = behavior_info["voice_prompt"]
                behavior_desc = behavior_info["behavior_desc"] if "behavior_desc" in behavior_info else "未知行为"
                behavior_num = behavior_info["behavior_num"] if "behavior_num" in behavior_info else "0"
                
                # 显示语音即将播放的状态提示
                self.update_status(f"播放语音提示：{behavior_desc}")

                # 优化提醒逻辑：减少不必要的提醒
                # 只对真正需要干预的行为进行警告
                warning_behaviors = ["5", "7", "8", "9", "10"]  # 玩手机、睡觉、其他分心、吃零食、不在座位
                mild_concern_behaviors = ["2", "6"]  # 轻度走神、与他人交流
                positive_behaviors = ["1", "3", "4"]  # 认真学习、使用工具、休息

                should_play_voice = True  # 默认播放语音

                if behavior_num in warning_behaviors:
                    # 严重问题：显示为警告
                    self.add_supervision_message(f"⚠️ {behavior_desc}: {voice_prompt}", is_system=False)
                elif behavior_num in mild_concern_behaviors:
                    # 轻微问题：显示为系统提示，降低紧迫感
                    self.add_supervision_message(f"💡 {behavior_desc}: {voice_prompt}", is_system=True)
                elif behavior_num in positive_behaviors:
                    # 正面行为：简化提示
                    if behavior_num == "1":  # 认真学习时不需要语音提示
                        should_play_voice = False
                        self.add_supervision_message(f"✅ {behavior_desc}", is_system=True)
                    else:
                        self.add_supervision_message(f"✅ {behavior_desc}", is_system=True)
                else:
                    # 未知行为：使用原有逻辑
                    self.add_supervision_message(f"{behavior_desc}: {voice_prompt}", is_system=True)

                # 播放语音提示（根据行为类型决定是否播放）
                if should_play_voice:
                    print(f"播放行为提示语音: {voice_prompt} (行为类型: {behavior_num}-{behavior_desc})")
                    self.audio_player.play_text(voice_prompt, priority=2)
                else:
                    print(f"跳过语音提示: {behavior_desc} (认真学习状态)")
            
            # Clean up placeholder mapping
            del self.placeholder_map[placeholder_id]
            
            return True
        except Exception as e:
            print(f"Error updating placeholder: {e}")
            return False
    
    def process_image_analysis(self, analysis, image_urls=None, screenshots=None, msg_id=None):
        """Process image analysis results and update UI with posture and emotion analysis"""
        try:
            if not analysis:
                print("警告: 图像分析结果为空")
                return

            # 预处理：确保AI分析结果的编码正确
            try:
                analysis = self._safe_text_encode(analysis)
                print(f"🔍 处理AI分析结果: {analysis[:100]}...")  # 显示前100个字符用于调试
            except Exception as e:
                print(f"⚠️ AI分析结果编码处理失败: {e}")
                analysis = str(analysis) if analysis else ""

            # 提取行为类型并选择特定的语音提示
            behavior_num, behavior_desc = extract_behavior_type(analysis)

            # 提取姿态和情绪分析数据（异步处理以提高性能）
            try:
                posture_data, emotion_data = extract_posture_and_emotion_analysis(analysis)

                # 生成姿态和情绪干预建议（仅在有有效数据时处理）
                if posture_data or emotion_data:
                    text_reminders, voice_reminders = generate_posture_emotion_interventions(posture_data, emotion_data)
                else:
                    text_reminders, voice_reminders = [], []
            except Exception as e:
                print(f"姿态情绪分析处理出错: {e}")
                posture_data, emotion_data = {}, {}
                text_reminders, voice_reminders = [], []

            # 从配置文件获取对应行为类型的语音提示 - 使用人性化随机选择
            # 优先使用behavior_prompts.py中的配置，确保一致性
            try:
                from behavior_prompts import get_random_voice_prompt
                # 构建上下文信息
                context = {
                    "frequency": getattr(self, f'behavior_{behavior_num}_count', 0),
                    "duration_minutes": getattr(self, 'session_duration_minutes', 0)
                }
                voice_prompt = get_random_voice_prompt(behavior_num, context)
            except ImportError:
                # 降级处理：使用原有方式
                from behavior_prompts import BEHAVIOR_VOICE_PROMPTS as CONFIG_VOICE_PROMPTS
                voice_prompt = CONFIG_VOICE_PROMPTS.get(behavior_num,
                              BEHAVIOR_VOICE_PROMPTS.get(behavior_num, f"行为监测：{behavior_desc}"))

            # 准备行为信息
            behavior_info = {
                "behavior_num": behavior_num,
                "behavior_desc": behavior_desc,
                "voice_prompt": voice_prompt,
                "posture_data": posture_data,
                "emotion_data": emotion_data
            }

            # 在监督聊天窗口显示行为检测结果 - 修复文本编码
            try:
                # 确保行为描述的文本编码正确
                safe_behavior_desc = self._safe_text_encode(behavior_desc)
                self.add_supervision_message(f"🔍 检测到：{safe_behavior_desc}", is_system=True)
            except Exception as e:
                print(f"⚠️ 显示行为检测结果失败: {e}")
                self.add_supervision_message("🔍 检测到行为变化", is_system=True)

            # 显示姿态分析结果 - 修复文本编码
            if posture_data and posture_data.get("overall_evaluation") != "未识别":
                try:
                    posture_status = posture_data.get("overall_evaluation", "未知")
                    safe_posture_status = self._safe_text_encode(posture_status)
                    self.add_supervision_message(f"🧍 姿态状态：{safe_posture_status}", is_system=True)
                except Exception as e:
                    print(f"⚠️ 显示姿态分析结果失败: {e}")
                    self.add_supervision_message("🧍 姿态状态已更新", is_system=True)

            # 显示情绪分析结果 - 修复文本编码
            if emotion_data and emotion_data.get("primary_emotion") != "未识别":
                try:
                    emotion_status = emotion_data.get("primary_emotion", "未知")
                    emotion_intensity = emotion_data.get("emotion_intensity", "")

                    safe_emotion_status = self._safe_text_encode(emotion_status)
                    safe_emotion_intensity = self._safe_text_encode(emotion_intensity) if emotion_intensity else ""

                    if safe_emotion_intensity:
                        emotion_display = f"{safe_emotion_status}({safe_emotion_intensity})"
                    else:
                        emotion_display = safe_emotion_status
                    self.add_supervision_message(f"😊 情绪状态：{emotion_display}", is_system=True)
                except Exception as e:
                    print(f"⚠️ 显示情绪分析结果失败: {e}")
                    self.add_supervision_message("😊 情绪状态已更新", is_system=True)

            # 显示姿态纠正提醒
            for reminder in text_reminders:
                self.add_supervision_message(reminder, is_system=False)

            # 根据行为类型决定是否显示警告
            if behavior_num in ["2", "3", "4", "5", "6", "7", "8"]:  # 需要警告的行为
                self.add_supervision_message(f"⚠️ {voice_prompt}", is_system=False)


            # DEBUG: 坐姿语音播放调试
            print(f"🔍 DEBUG: process_image_analysis - 开始处理")
            print(f"🔍 DEBUG: hasattr(self, 'audio_player'): {hasattr(self, 'audio_player')}")
            if hasattr(self, 'audio_player'):
                print(f"🔍 DEBUG: self.audio_player: {self.audio_player}")
                print(f"🔍 DEBUG: audio_player is not None: {self.audio_player is not None}")
            print(f"🔍 DEBUG: posture_data: {posture_data}")
            print(f"🔍 DEBUG: emotion_data: {emotion_data}")
            print(f"🔍 DEBUG: voice_reminders count: {len(voice_reminders)}")
            # 优化的语音播放逻辑 - 按优先级分类播放
            if hasattr(self, 'audio_player') and self.audio_player:
                # 传递姿态和情绪数据以便直接生成准确的语音提醒
                self._play_categorized_voice_reminders_with_data(
                    voice_reminders, voice_prompt, behavior_num, behavior_desc,
                    posture_data, emotion_data
                )

            # 将AI分析结果完全移动到右下角监督窗口，不在左侧显示
            # 在监督聊天窗口显示完整的详细分析
            if len(analysis) > 200:
                # 如果分析内容很长，分段显示
                self.add_supervision_message(f"📊 行为分析结果：", is_system=True)
                self.add_supervision_message(f"{analysis}", is_system=True)
            else:
                self.add_supervision_message(f"📊 {analysis}", is_system=True)

            # 更新DeepSeek聊天模块的行为上下文和学生状态
            self.update_deepseek_context(behavior_num, behavior_desc, analysis)

            # Update preview if available
            if screenshots and len(screenshots) > 0:
                self.update_preview(screenshots[0])

        except Exception as e:
            print(f"Error processing image analysis: {e}")
            self.update_status(f"处理分析时出错: {e}")

    def _play_categorized_voice_reminders(self, voice_reminders, voice_prompt, behavior_num, behavior_desc):
        """
        按分类和优先级播放语音提醒
        确保姿态、情绪、行为提醒按序完整播放
        改进版：直接从姿态和情绪数据生成准确的语音提醒
        """
        try:
            # 获取当前的姿态和情绪数据
            posture_data = getattr(self, 'current_posture_data', {})
            emotion_data = getattr(self, 'current_emotion_data', {})

            # 直接从姿态和情绪数据生成准确的语音提醒
            posture_reminders = self._generate_accurate_posture_reminders(posture_data)
            emotion_reminders = self._generate_accurate_emotion_reminders(emotion_data)

            # 如果没有从数据生成提醒，则使用原有的分类逻辑作为备用
            if not posture_reminders and not emotion_reminders:
                posture_reminders, emotion_reminders = self._classify_voice_reminders(voice_reminders)

            # 1. 最高优先级：姿态纠正提醒
            if posture_reminders:
                # 智能合并姿态提醒，避免重复和过长
                posture_text = self._merge_similar_reminders(posture_reminders, "posture")

                print(f"播放姿态纠正提醒 (优先级1): {posture_text[:50]}...")
                # DEBUG: 坐姿语音播放调试
                print(f"🔍 DEBUG: 即将播放坐姿语音: {posture_text}")
                print(f"🔍 DEBUG: audio_player状态: {self.audio_player}")
                try:
                    result = self.audio_player.play_text(posture_text, priority=1, category="posture")
                    print(f"🔍 DEBUG: 坐姿语音播放结果: {result}")
                except Exception as play_error:
                    print(f"🔍 DEBUG: 坐姿语音播放异常: {play_error}")
                    raise

            # 2. 高优先级：情绪关怀提醒
            if emotion_reminders:
                # 智能合并情绪提醒，避免重复和过长
                emotion_text = self._merge_similar_reminders(emotion_reminders, "emotion")

                print(f"播放情绪关怀提醒 (优先级1): {emotion_text[:50]}...")
                self.audio_player.play_text(emotion_text, priority=1, category="emotion")

            # 3. 普通优先级：行为分析提醒
            # 只有在需要警告的行为时才播放行为提醒
            if behavior_num in ["2", "3", "4", "5", "6", "7", "8", "9", "10"]:
                print(f"播放行为分析提醒 (优先级2): {voice_prompt[:50]}...")
                self.audio_player.play_text(voice_prompt, priority=2, category="behavior")
            elif not posture_reminders and not emotion_reminders:
                # 如果没有姿态和情绪提醒，且是正常学习行为，播放鼓励语音
                if behavior_num == "1":  # 认真学习
                    encouragement = "很好，继续保持专注的学习状态"
                    print(f"播放鼓励语音 (优先级2): {encouragement}")
                    self.audio_player.play_text(encouragement, priority=2, category="encouragement")

            # 记录播放的语音类型统计
            reminder_count = len(posture_reminders) + len(emotion_reminders)
            if reminder_count > 0:
                print(f"语音提醒统计: 姿态 {len(posture_reminders)} 条, 情绪 {len(emotion_reminders)} 条, 行为 1 条")

        except Exception as e:
            print(f"播放分类语音提醒时出错: {e}")
            # 降级处理：播放基本的行为提醒
            try:
                self.audio_player.play_text(voice_prompt, priority=2, category="fallback")
            except Exception as fallback_error:
                print(f"降级语音播放也失败: {fallback_error}")

    def _merge_similar_reminders(self, reminders, category):
        """
        智能合并相似的语音提醒，避免重复和过长
        """
        if not reminders:
            return ""

        if len(reminders) == 1:
            return reminders[0]

        # 根据类别进行智能合并
        if category == "posture":
            return self._merge_posture_reminders(reminders)
        elif category == "emotion":
            return self._merge_emotion_reminders(reminders)
        else:
            # 默认合并：取前两个，用逗号连接
            merged = "，".join(reminders[:2])
            if len(merged) > 120:  # 如果太长，只取第一个
                return reminders[0]
            return merged

    def _merge_posture_reminders(self, reminders):
        """合并姿态提醒，优先处理最严重的问题，改进问题识别准确性"""
        if not reminders:
            return ""

        # 按严重程度排序的关键词
        severity_keywords = {
            "立即调整": 5,
            "明显不良": 4,
            "弯腰驼背": 4,
            "趴桌": 4,
            "需要纠正": 3,
            "前倾": 2,
            "前伸": 2,
            "耸肩": 2,
            "高低不平": 2,
            "轻微": 1
        }

        # 找出最严重的问题
        max_severity = 0
        primary_reminder = reminders[0]

        for reminder in reminders:
            for keyword, severity in severity_keywords.items():
                if keyword in reminder and severity > max_severity:
                    max_severity = severity
                    primary_reminder = reminder

        # 如果有多个提醒，尝试合并关键信息
        if len(reminders) > 1:
            # 提取关键动作词和问题描述
            actions = []
            problems = []

            # 检查具体问题 - 改进识别逻辑
            combined_text = " ".join(reminders)  # 合并所有提醒文本进行分析

            # 更精确的问题识别
            if any(keyword in combined_text for keyword in ["头部", "前倾", "低头"]):
                problems.append("头部前倾")
                actions.append("抬起头部")
            if any(keyword in combined_text for keyword in ["颈部", "前伸"]):
                problems.append("颈部前伸")
                actions.append("收回颈部")
            if any(keyword in combined_text for keyword in ["弯腰", "驼背"]):
                problems.append("弯腰驼背")
                actions.append("挺直腰背")
            if any(keyword in combined_text for keyword in ["肩膀", "高低不平", "不平"]):
                problems.append("肩膀不平")
                actions.append("调整肩膀")
            if any(keyword in combined_text for keyword in ["耸肩", "紧张"]):
                actions.append("放松肩膀")

            # 去重
            actions = list(dict.fromkeys(actions))
            problems = list(dict.fromkeys(problems))

            if problems and actions:
                problem_text = "、".join(problems[:2])  # 最多显示2个问题
                action_text = "、".join(actions[:3])   # 最多显示3个动作
                return f"检测到{problem_text}，请{action_text}，保持端正的坐姿"
            elif actions:
                action_text = "、".join(actions[:3])
                return f"注意姿态，请{action_text}，保持端正的坐姿"

        return primary_reminder

    def _merge_emotion_reminders(self, reminders):
        """合并情绪提醒，保持关怀语调"""
        if not reminders:
            return ""

        # 情绪类型优先级（负面情绪优先处理）
        emotion_priority = {
            "疲惫": 3,
            "焦虑": 3,
            "沮丧": 3,
            "愤怒": 2,
            "困惑": 2,
            "无聊": 1,
            "快乐": 0,
            "专注": 0
        }

        # 找出最需要关注的情绪
        primary_emotion = ""
        max_priority = -1

        for reminder in reminders:
            for emotion, priority in emotion_priority.items():
                if emotion in reminder and priority > max_priority:
                    max_priority = priority
                    primary_emotion = emotion

        # 根据主要情绪类型合并提醒
        if max_priority >= 3:  # 负面情绪
            if "疲惫" in primary_emotion:
                return "您看起来很疲惫，建议休息5-10分钟，做一些放松运动来缓解疲劳"
            elif "焦虑" in primary_emotion:
                return "检测到您有些焦虑，建议深呼吸放松，学习要循序渐进，不要给自己太大压力"
        elif max_priority >= 1:  # 中性情绪
            return reminders[0]  # 保持原始提醒
        else:  # 正面情绪
            return "很高兴看到您的积极状态，继续保持这种良好的学习态度"

        return reminders[0]  # 默认返回第一个

    def _generate_accurate_posture_reminders(self, posture_data):
        """
        直接从姿态数据生成准确的语音提醒
        解决姿态问题识别率低的问题
        """
        # DEBUG: 坐姿语音播放调试
        print(f"🔍 DEBUG: _generate_accurate_posture_reminders - 开始")
        print(f"🔍 DEBUG: posture_data: {posture_data}")
        
        if not posture_data:
            print(f"🔍 DEBUG: posture_data为空，返回空列表")
            return []

        reminders = []

        # 检查具体的姿态问题
        head_position = posture_data.get("head_position", "")
        neck_status = posture_data.get("neck_status", "")
        shoulder_status = posture_data.get("shoulder_status", "")
        body_posture = posture_data.get("body_posture", "")
        overall_evaluation = posture_data.get("overall_evaluation", "")

        # POSTURE_ROBUSTNESS_FIX: 增强条件匹配逻辑
        print(f"🔍 DEBUG: 字段值详细检查:")
        print(f"   head_position: '{head_position}'")
        print(f"   neck_status: '{neck_status}'")
        print(f"   shoulder_status: '{shoulder_status}'")
        print(f"   body_posture: '{body_posture}'")
        print(f"   overall_evaluation: '{overall_evaluation}'")
        
        # 增强的头部位置问题检查（添加模糊匹配） - 使用人性化提示
        if head_position in ["前倾", "低头"] or "前倾" in head_position or "低头" in head_position:
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("2")  # 头部前倾对应类型2
            except ImportError:
                reminder = "检测到头部前倾，请抬起头部，保持颈部挺直"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 头部问题匹配成功 -> {reminder}")
        elif head_position == "后仰" or "后仰" in head_position:
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("2")  # 头部问题对应类型2
            except ImportError:
                reminder = "头部过度后仰，请调整到自然位置"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 头部后仰问题匹配成功 -> {reminder}")
        else:
            print(f"🔍 DEBUG: 头部问题未匹配: '{head_position}'")

        # 增强的颈部状态问题检查（添加模糊匹配）
        if neck_status == "前伸" or "前伸" in neck_status:
            reminder = "检测到颈部前伸，请将颈部向后收回，保持挺直状态"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 颈部问题匹配成功 -> {reminder}")
        elif neck_status == "紧张":
            reminders.append("颈部紧张，建议适当放松")

        # 增强的肩膀状态问题检查（添加模糊匹配） - 使用人性化提示
        if shoulder_status == "高低不平" or "不平" in shoulder_status or "高低" in shoulder_status:
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("3")  # 肩膀问题对应类型3
            except ImportError:
                reminder = "检测到肩膀不平，请调整坐姿，让两边肩膀保持平衡"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 肩膀问题匹配成功 -> {reminder}")
        elif shoulder_status == "耸肩" or "耸肩" in shoulder_status:
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("3")  # 肩膀问题对应类型3
            except ImportError:
                reminder = "肩膀耸起，请放松肩膀，让它们自然下沉"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 肩膀耸起问题匹配成功 -> {reminder}")
        elif shoulder_status == "含胸" or "含胸" in shoulder_status:
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("4")  # 含胸驼背对应类型4
            except ImportError:
                reminder = "含胸驼背，请挺起胸部，展开肩膀"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 含胸问题匹配成功 -> {reminder}")
        else:
            print(f"🔍 DEBUG: 肩膀问题未匹配: '{shoulder_status}'")

        # 增强的身体姿势问题检查（添加模糊匹配） - 使用人性化提示
        if body_posture == "弯腰驼背" or "弯腰" in body_posture or "驼背" in body_posture:
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("4")  # 驼背问题对应类型4
            except ImportError:
                reminder = "检测到弯腰驼背，请挺直腰背，保持端正的坐姿"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 身体姿势问题匹配成功 -> {reminder}")
        elif body_posture == "趴桌":
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("6")  # 过于放松对应类型6
            except ImportError:
                reminder = "请不要趴在桌子上，坐直身体"
            reminders.append(reminder)
        elif body_posture == "侧身":
            try:
                from behavior_prompts import get_random_posture_prompt
                reminder = get_random_posture_prompt("5")  # 身体倾斜对应类型5
            except ImportError:
                reminder = "身体侧向，请面向正前方"
            reminders.append(reminder)

        # 增强的整体评价检查（添加模糊匹配）
        if overall_evaluation == "明显不良" or "不良" in overall_evaluation:
            reminder = "姿态明显不良，请立即调整坐姿"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 整体评价问题匹配成功 -> {reminder}")
        elif overall_evaluation == "需要纠正" or "纠正" in overall_evaluation:
            reminder = "姿态需要纠正，请注意调整"
            reminders.append(reminder)
            print(f"🔍 DEBUG: 需要纠正问题匹配成功 -> {reminder}")
        else:
            print(f"🔍 DEBUG: 整体评价问题未匹配: '{overall_evaluation}'")

        print(f"🔍 DEBUG: 生成的坐姿提醒数量: {len(reminders)}")
        
        # POSTURE_ROBUSTNESS_FIX: 备用生成机制
        if not reminders and posture_data:
            # 如果没有生成任何提醒，但有姿态数据，尝试生成通用提醒
            raw_description = posture_data.get("raw_description", "")
            if raw_description and ("不良" in raw_description or "前倾" in raw_description or "驼背" in raw_description or "纠正" in raw_description):
                backup_reminder = "检测到坐姿问题，请注意调整坐姿，保持身体挺直"
                reminders.append(backup_reminder)
                print(f"🔍 DEBUG: 使用备用生成机制 -> {backup_reminder}")
        
        print(f"🔍 DEBUG: 最终返回的坐姿提醒: {reminders}")
        return reminders

    def _generate_accurate_emotion_reminders(self, emotion_data):
        """
        直接从情绪数据生成准确的语音提醒
        """
        if not emotion_data:
            return []

        reminders = []

        primary_emotion = emotion_data.get("primary_emotion", "")
        emotion_intensity = emotion_data.get("emotion_intensity", "")
        mental_state = emotion_data.get("mental_state", "")

        # 使用人性化情绪提示
        try:
            from behavior_prompts import get_random_emotion_prompt

            # 负面情绪支持
            if primary_emotion == "疲惫":
                reminder = get_random_emotion_prompt("疲惫", emotion_intensity)
                if reminder:
                    reminders.append(reminder)

            elif primary_emotion == "焦虑":
                reminder = get_random_emotion_prompt("焦虑")
                if reminder:
                    reminders.append(reminder)

            elif primary_emotion == "沮丧":
                reminder = get_random_emotion_prompt("沮丧")
                if reminder:
                    reminders.append(reminder)

            elif primary_emotion == "愤怒":
                reminder = get_random_emotion_prompt("愤怒")
                if reminder:
                    reminders.append(reminder)

            elif primary_emotion == "困惑":
                reminders.append("学习中有困惑是正常的，可以向老师或同学寻求帮助")

            elif primary_emotion == "无聊":
                reminders.append("学习感到无聊时，可以尝试不同的学习方法，或者设定小目标来增加成就感")

            # 正面情绪鼓励
            elif primary_emotion == "快乐":
                reminders.append("很高兴看到您心情愉快，保持这种积极的状态，学习效果会更好")

            elif primary_emotion == "专注":
                reminder = get_random_emotion_prompt("专注")
                if reminder:
                    reminders.append(reminder)

            elif primary_emotion == "兴奋":
                reminders.append("您的学习热情很高，这种积极的态度值得鼓励，继续加油")

            # 精神状态建议
            if mental_state == "困倦":
                reminder = get_random_emotion_prompt("疲惫", "中等")
                if reminder:
                    reminders.append(reminder)
                else:
                    reminders.append("您看起来有些困倦，建议休息一下或者做一些提神的活动")
            elif mental_state == "疲惫":
                reminder = get_random_emotion_prompt("疲惫", "强烈")
                if reminder:
                    reminders.append(reminder)
                else:
                    reminders.append("精神状态有些疲惫，建议调整学习节奏，适当放松")

        except ImportError:
            # 降级处理：使用原有方式
            if primary_emotion == "疲惫":
                if emotion_intensity == "强烈":
                    reminders.append("您看起来很疲惫，建议休息5-10分钟，做一些放松运动来缓解疲劳")
                else:
                    reminders.append("检测到您有些疲惫，记得适当休息，保持良好的学习状态")
            elif primary_emotion == "焦虑":
                reminders.append("检测到您有些焦虑，建议深呼吸放松，学习要循序渐进，不要给自己太大压力")
            elif primary_emotion == "专注":
                reminders.append("您现在的专注状态很棒，继续保持这种投入的学习态度")

        return reminders

    def _classify_voice_reminders(self, voice_reminders):
        """
        备用的语音提醒分类方法
        """
        posture_reminders = []
        emotion_reminders = []

        # 从voice_reminders中分离姿态和情绪提醒
        for reminder in voice_reminders:
            # 更精确的姿态提醒识别
            posture_keywords = ["姿态", "头部", "颈部", "肩膀", "腰背", "坐姿", "体态", "前倾", "前伸", "驼背", "弯腰", "高低不平", "耸肩", "含胸", "挺胸", "趴桌"]
            emotion_keywords = ["情绪", "疲惫", "焦虑", "沮丧", "愤怒", "困惑", "无聊", "快乐", "专注", "兴奋", "精神", "困倦", "休息", "放松", "压力"]

            if any(keyword in reminder for keyword in posture_keywords):
                posture_reminders.append(reminder)
            elif any(keyword in reminder for keyword in emotion_keywords):
                emotion_reminders.append(reminder)

        return posture_reminders, emotion_reminders

    def _play_categorized_voice_reminders_with_data(self, voice_reminders, voice_prompt, behavior_num, behavior_desc, posture_data, emotion_data):
        """
        使用姿态和情绪数据直接生成准确的语音提醒
        这是修复后的版本，解决了姿态问题识别率低的问题
        """
        try:
            # DEBUG: 坐姿语音播放调试
            print(f"🔍 DEBUG: _play_categorized_voice_reminders_with_data - 开始")
            print(f"🔍 DEBUG: posture_data: {posture_data}")
            print(f"🔍 DEBUG: emotion_data: {emotion_data}")
            print(f"🔍 DEBUG: behavior_num: {behavior_num}")
            print(f"🔍 DEBUG: behavior_desc: {behavior_desc}")
            print(f"🔍 DEBUG: voice_reminders: {voice_reminders}")

            # 优先使用修复的精确语音生成逻辑，忽略传入的voice_reminders
            posture_reminders = self._generate_accurate_posture_reminders(posture_data)
            emotion_reminders = self._generate_accurate_emotion_reminders(emotion_data)

            print(f"🔍 DEBUG: 修复逻辑生成的坐姿提醒: {len(posture_reminders)} 条")
            print(f"🔍 DEBUG: 修复逻辑生成的情绪提醒: {len(emotion_reminders)} 条")

            # 如果修复逻辑没有生成提醒，则使用原有的分类逻辑作为备用
            if not posture_reminders and not emotion_reminders:
                print(f"🔍 DEBUG: 使用备用分类逻辑")
                posture_reminders, emotion_reminders = self._classify_voice_reminders(voice_reminders)
            else:
                print(f"🔍 DEBUG: 使用修复的精确生成逻辑")

            # 统一语音播放策略：合并所有三类语音为综合健康提醒
            comprehensive_reminders = []
            reminder_types = []

            # 1. 坐姿纠正提醒（优先级最高，放在最前面）
            if posture_reminders:
                posture_text = self._merge_similar_reminders(posture_reminders, "posture")
                comprehensive_reminders.append(posture_text)
                reminder_types.append("坐姿纠正")
                print(f"准备合并坐姿纠正提醒: {posture_text[:50]}...")
                print(f"🔍 DEBUG: 坐姿语音内容: {posture_text}")

            # 2. 情绪关怀提醒（第二优先级）
            if emotion_reminders:
                emotion_text = self._merge_similar_reminders(emotion_reminders, "emotion")
                comprehensive_reminders.append(emotion_text)
                reminder_types.append("情绪关怀")
                print(f"准备合并情绪关怀提醒: {emotion_text[:50]}...")
                print(f"🔍 DEBUG: 情绪语音内容: {emotion_text}")

            # 3. 注意力监测提醒（第三优先级，但仍包含在综合提醒中）
            if behavior_num in ['2','3','4','5','6','7','8','9','10']:
                comprehensive_reminders.append(voice_prompt)
                reminder_types.append("注意力监测")
                print(f"准备合并注意力监测提醒: {voice_prompt[:50]}...")
                print(f"🔍 DEBUG: 注意力语音内容: {voice_prompt}")
            elif behavior_num == "1" and not posture_reminders and not emotion_reminders:
                # 如果是正常学习且没有其他提醒，添加鼓励语音
                encouragement = "很好，继续保持专注的学习状态"
                comprehensive_reminders.append(encouragement)
                reminder_types.append("学习鼓励")
                print(f"准备合并学习鼓励: {encouragement}")
                print(f"🔍 DEBUG: 鼓励语音内容: {encouragement}")

            # 合并所有语音为单一综合健康提醒
            if comprehensive_reminders:
                if len(comprehensive_reminders) == 1:
                    # 只有一种类型的提醒
                    final_comprehensive_text = comprehensive_reminders[0]
                elif len(comprehensive_reminders) == 2:
                    # 两种类型的提醒
                    final_comprehensive_text = f"{comprehensive_reminders[0]}。同时，{comprehensive_reminders[1]}"
                else:
                    # 三种类型的提醒（完整的综合指导）
                    final_comprehensive_text = f"{comprehensive_reminders[0]}。同时，{comprehensive_reminders[1]}。另外，{comprehensive_reminders[2]}"

                print(f"播放综合健康提醒 (优先级1): {final_comprehensive_text[:50]}...")
                print(f"🔍 DEBUG: 包含的提醒类型: {', '.join(reminder_types)}")
                print(f"🔍 DEBUG: 最终综合语音内容: {final_comprehensive_text}")
                print(f"🔍 DEBUG: audio_player状态: {self.audio_player}")

                try:
                    result = self.audio_player.play_text(final_comprehensive_text, priority=1, category="comprehensive_health")
                    print(f"🔍 DEBUG: 综合健康语音播放结果: {result}")

                    if result:
                        print(f"✅ 综合健康提醒播放成功，包含 {len(comprehensive_reminders)} 类指导: {', '.join(reminder_types)}")
                    else:
                        print(f"❌ 综合健康提醒播放失败")

                except Exception as play_error:
                    print(f"🔍 DEBUG: 综合健康语音播放异常: {play_error}")
                    raise
            else:
                print(f"🔍 DEBUG: 未生成任何语音提醒")

            # 记录播放的语音类型统计
            if comprehensive_reminders:
                total_reminders = len(posture_reminders) + len(emotion_reminders) + (1 if behavior_num in ['1','2','3','4','5','6','7','8','9','10'] else 0)
                print(f"📊 综合语音提醒统计: 坐姿 {len(posture_reminders)} 条, 情绪 {len(emotion_reminders)} 条, 行为/鼓励 {1 if behavior_num in ['1','2','3','4','5','6','7','8','9','10'] else 0} 条")
                print(f"📊 合并为 1 条综合健康提醒，避免队列冲突和超时问题")

        except Exception as e:
            print(f"播放分类语音提醒时出错: {e}")
            # 降级处理：使用原有方法
            try:
                self._play_categorized_voice_reminders(voice_reminders, voice_prompt, behavior_num, behavior_desc)
            except Exception as fallback_error:
                print(f"降级语音播放也失败: {fallback_error}")

    @handle_exceptions()
    def check_and_cleanup_memory(self):
        """Check and perform memory cleanup if necessary"""
        try:
            current_memory_usage = get_memory_usage()
            if current_memory_usage > self.memory_threshold_mb:
                print(f"内存使用超过阈值 {self.memory_threshold_mb} MB，执行内存清理")
                perform_memory_cleanup()
                
                # 清理观察历史记录
                if hasattr(self, 'observation_history') and len(self.observation_history) > self.max_observation_history // 2:
                    excess = len(self.observation_history) - self.max_observation_history // 2
                    self.observation_history = self.observation_history[excess:]
                    print(f"清理历史记录，移除了 {excess} 条旧记录")
                    
            else:
                print(f"内存使用正常，当前内存使用: {current_memory_usage:.1f} MB")
            
            # Schedule next check
            self.after(self.memory_check_interval, self.check_and_cleanup_memory)
            
        except Exception as e:
            print(f"内存检查失败: {e}")
            # 继续调度下次检查
            self.after(self.memory_check_interval, self.check_and_cleanup_memory)

    def update_image(self, img):
        """更新主界面图像显示"""
        if img is not None:
            # 更新预览图
            self.update_preview(img)
            
            # 可以在这里添加其他需要的图像更新逻辑
            print("已更新主界面图像")
    

    
    def send_deepseek_message(self):
        """发送DeepSeek问答消息 - 新的API集成"""
        try:
            user_text = self.qa_input.get().strip()
            if not user_text:
                return
            
            # 清空输入框
            self.qa_input.delete(0, 'end')
            
            # 添加用户消息
            self.add_qa_message(user_text, is_ai=False)
            
            # 添加思考中占位符
            placeholder_id = f"thinking_{int(time.time() * 1000)}"
            self.add_qa_message("🧠 DeepSeek正在思考中...", is_ai=True, placeholder_id=placeholder_id)
            
            # 使用新的DeepSeek API处理回复
            threading.Thread(target=self._process_deepseek_reply, args=(user_text, placeholder_id), daemon=True).start()
            
        except Exception as e:
            print(f"发送DeepSeek消息时出错: {e}")
            self.add_qa_message("抱歉，发送消息时出现错误，请重试。", is_ai=True)
    
    def send_qa_message(self):
        """保持兼容性的原有方法"""
        self.send_deepseek_message()

    def capture_question_image(self):
        """拍照识别题目并提供分步讲解"""
        try:
            # 检查题目识别系统是否可用
            if not hasattr(self, 'question_system') or not self.question_system:
                self.add_qa_message("题目识别功能不可用，请检查OCR依赖库是否已安装", is_ai=True)
                return

            if not self.question_system.is_available:
                self.add_qa_message("OCR识别功能不可用，请安装easyocr或paddleocr库", is_ai=True)
                return

            # 检查摄像头是否可用
            if not hasattr(self, 'webcam_handler') or not self.webcam_handler:
                self.add_qa_message("摄像头不可用，无法拍照识别题目", is_ai=True)
                return

            if not self.webcam_handler.running:
                self.add_qa_message("摄像头未启动，请先启动摄像头功能", is_ai=True)
                return

            # 添加处理中的占位符消息
            placeholder_id = f"question_processing_{int(time.time() * 1000)}"
            print(f"🆔 生成占位符ID: {placeholder_id}")

            self.add_qa_message("📷 正在拍照识别题目，请稍候...", is_ai=True, placeholder_id=placeholder_id)

            # 验证占位符是否成功创建
            if hasattr(self, 'qa_placeholders') and placeholder_id in self.qa_placeholders:
                print(f"✅ 占位符 {placeholder_id} 创建成功")
            else:
                print(f"❌ 占位符 {placeholder_id} 创建失败")

            # 在后台线程中处理题目识别
            threading.Thread(target=self._process_question_recognition, args=(placeholder_id,), daemon=True).start()

        except Exception as e:
            error_msg = f"启动拍照识别时出错: {e}"
            print(error_msg)
            self.add_qa_message("抱歉，拍照识别功能暂时不可用，请重试", is_ai=True)

    def _process_question_recognition(self, placeholder_id):
        """在后台线程中处理题目识别 - 线程安全增强版本"""
        import threading
        import gc

        # 获取当前线程信息
        current_thread = threading.current_thread()
        print(f"🧵 后台处理线程: {current_thread.name} (ID: {current_thread.ident})")

        # 初始化时间变量，防止作用域错误
        start_time = time.time()
        process_start = start_time

        try:
            print(f"🚀 开始题目识别流程，占位符ID: {placeholder_id}")

            # 线程安全检查：确保不在主线程中
            if threading.current_thread() is threading.main_thread():
                print("⚠️ 警告：在主线程中执行后台任务，可能导致UI冻结")

            # 强制垃圾回收，释放内存
            gc.collect()

            # 1. 捕获当前摄像头图像 - 添加线程安全保护
            print("📷 开始捕获摄像头图像...")
            try:
                current_image = self._capture_single_image()
                if current_image is None:
                    error_msg = "❌ 无法获取摄像头图像，请检查摄像头连接状态"
                    print(error_msg)
                    # 使用安全的占位符更新
                    self._safe_placeholder_update(placeholder_id, error_msg)
                    return
            except Exception as capture_error:
                error_msg = f"❌ 图像捕获失败: {capture_error}"
                print(error_msg)
                self._safe_placeholder_update(placeholder_id, error_msg)
                return

            capture_time = time.time() - start_time
            print(f"✅ 图像捕获完成，耗时: {capture_time:.3f}s，图像尺寸: {current_image.size}")

            # 2. 线程安全的状态消息更新
            print("📝 更新状态消息...")
            self._safe_placeholder_update(placeholder_id, "🔍 正在识别题目文字，请稍候...")

            # 3. 验证题目识别系统
            if not hasattr(self, 'question_system') or not self.question_system:
                error_msg = "❌ 题目识别系统未初始化"
                print(error_msg)
                self._safe_placeholder_update(placeholder_id, error_msg)
                return

            # 4. 使用题目识别系统处理图像
            print("🤖 开始题目识别处理...")
            process_start = time.time()  # 更新处理开始时间

            try:
                # 使用线程安全的超时机制
                print("🔄 开始题目处理（线程安全超时模式）...")

                # 使用concurrent.futures实现超时
                import concurrent.futures

                def process_image_task():
                    """题目处理任务函数"""
                    return self.question_system.process_question_image(current_image)

                # 创建线程池执行器
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    # 提交任务
                    future = executor.submit(process_image_task)

                    try:
                        # 等待结果，设置120秒超时
                        solution, question_info = future.result(timeout=120)
                        print("✅ 题目处理在超时限制内完成")

                    except concurrent.futures.TimeoutError:
                        print("⏰ 题目处理超时，尝试取消任务...")
                        future.cancel()
                        raise TimeoutError("题目处理超时（120秒）")

                process_time = time.time() - process_start

                print(f"📊 题目识别处理完成，耗时: {process_time:.3f}s")
                print(f"📝 解题结果长度: {len(solution) if solution else 0} 字符")
                print(f"📋 题目信息: {question_info}")

            except TimeoutError:
                # 安全计算超时时间
                try:
                    process_time = time.time() - process_start
                    print(f"⏰ 题目处理超时，耗时: {process_time:.3f}s")
                except NameError:
                    print("⏰ 题目处理超时")

                error_msg = "题目处理超时，请重新尝试"
                # 使用安全的占位符更新，确保不被意外删除
                self._safe_placeholder_update(placeholder_id, f"❌ {error_msg}")
                return

            except MemoryError:
                # 安全计算内存错误时间
                try:
                    process_time = time.time() - process_start
                    print(f"💾 内存不足，耗时: {process_time:.3f}s")
                except NameError:
                    print("💾 内存不足")

                error_msg = "内存不足，请关闭其他应用程序后重试"
                # 强制垃圾回收
                import gc
                gc.collect()
                # 使用安全的占位符更新
                self._safe_placeholder_update(placeholder_id, f"❌ {error_msg}")
                return

            except Exception as process_error:
                # 安全计算异常时间
                try:
                    process_time = time.time() - process_start
                except NameError:
                    process_time = 0.0

                # 检查是否是特定类型的错误
                error_str = str(process_error).lower()
                if 'signal only works in main thread' in error_str:
                    error_msg = "线程处理错误已修复，请重新尝试"
                    print(f"🔧 检测到线程信号错误: {process_error}")
                elif 'segmentation' in error_str or 'segfault' in error_str or 'objc' in error_str:
                    error_msg = "系统兼容性问题，请重新尝试"
                    print(f"🔧 检测到系统级错误: {process_error}")
                else:
                    error_msg = f"题目识别处理失败: {process_error}"
                    print(f"❌ {error_msg}")

                import traceback
                traceback.print_exc()
                # 使用安全的占位符更新
                self._safe_placeholder_update(placeholder_id, f"❌ {error_msg}")
                return

            total_time = time.time() - start_time
            print(f"📊 题目识别总耗时: {total_time:.3f}s (捕获: {capture_time:.3f}s, 处理: {process_time:.3f}s)")

            # 5. 格式化结果消息
            print("📋 开始格式化结果消息...")

            if solution and "失败" not in solution and "错误" not in solution and "不可用" not in solution:
                # 成功识别
                print("✅ 题目识别成功，开始格式化结果...")
                result_message = f"📝 题目识别成功！\n\n"

                # 添加题目信息
                if question_info:
                    question_type = question_info.get("题目类型", "未知类型")
                    if question_type != "未知类型":
                        result_message += f"**题目类型**: {question_type}\n\n"
                        print(f"📊 题目类型: {question_type}")

                    # 添加识别的原始文本（截取前100字符）
                    original_text = question_info.get("原始文本", "")
                    if original_text and len(original_text) > 20:
                        preview_text = original_text[:100] + "..." if len(original_text) > 100 else original_text
                        result_message += f"**识别文本**: {preview_text}\n\n"
                        print(f"📝 识别文本预览: {preview_text}")

                # 添加分步讲解
                result_message += f"**详细讲解**:\n{solution}"
                print(f"📚 讲解内容长度: {len(solution)} 字符")

                # 使用安全的占位符更新方法
                print(f"🔄 准备更新占位符 {placeholder_id}...")
                self._safe_placeholder_update(placeholder_id, result_message)
                print("✅ 题目识别和讲解完成")
            else:
                # 识别失败
                error_message = solution if solution else "题目识别失败，请确保图像清晰且包含题目文字"
                print(f"❌ 题目识别失败: {error_message}")

                # 使用安全的占位符更新
                self._safe_placeholder_update(placeholder_id, f"❌ {error_message}")

        except Exception as e:
            # 全局崩溃恢复机制
            error_msg = f"题目处理过程出错: {e}"
            print(f"❌ {error_msg}")

            # 检查错误类型并提供相应的恢复建议
            error_str = str(e).lower()
            if 'segmentation' in error_str or 'segfault' in error_str:
                recovery_msg = "❌ 系统级错误，建议重启应用程序"
                print("🔧 检测到段错误，可能需要重启应用")
            elif 'memory' in error_str or 'malloc' in error_str:
                recovery_msg = "❌ 内存不足，请关闭其他应用程序后重试"
                print("💾 检测到内存错误，建议释放内存")
                # 强制垃圾回收
                import gc
                gc.collect()
            elif 'objc' in error_str or 'cocoa' in error_str:
                recovery_msg = "❌ macOS系统兼容性问题，请重新尝试"
                print("🍎 检测到macOS系统级错误")
            else:
                recovery_msg = "❌ 抱歉，题目处理过程中出现错误，请重试"

            import traceback
            traceback.print_exc()

            # 使用安全的占位符更新方式
            self._safe_placeholder_update(placeholder_id, recovery_msg)

            # 记录错误到日志文件
            try:
                with open("crash_log.txt", "a", encoding="utf-8") as f:
                    f.write(f"\n[{datetime.now()}] 拍照解题崩溃:\n")
                    f.write(f"错误: {error_msg}\n")
                    f.write(f"堆栈: {traceback.format_exc()}\n")
                    f.write("-" * 50 + "\n")
            except:
                pass

    def _capture_single_image(self):
        """捕获单张图像用于题目识别"""
        try:
            if self.webcam_handler.simulation_mode:
                # 模拟模式：返回模拟图像
                if self.webcam_handler.simulation_images:
                    print("📷 使用模拟图像进行测试")
                    return self.webcam_handler.simulation_images[0].copy()
                else:
                    print("❌ 模拟模式下无可用图像")
                    return None

            # 真实摄像头模式
            if not self.webcam_handler.cap or not self.webcam_handler.cap.isOpened():
                print("❌ 摄像头未打开，无法捕获图像")
                return None

            # 多次尝试捕获图像以确保质量
            max_attempts = 3
            for attempt in range(max_attempts):
                ret, frame = self.webcam_handler.cap.read()
                if ret and frame is not None:
                    # 检查图像质量
                    if frame.shape[0] > 100 and frame.shape[1] > 100:  # 最小尺寸检查
                        # 转换为PIL图像
                        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        pil_image = Image.fromarray(frame_rgb)

                        # 图像质量检查
                        width, height = pil_image.size
                        if width >= 320 and height >= 240:  # 确保足够的分辨率
                            print(f"✅ 成功捕获图像 (尺寸: {width}x{height})")
                            return pil_image
                        else:
                            print(f"⚠️ 图像分辨率过低: {width}x{height}")
                    else:
                        print(f"⚠️ 图像尺寸异常: {frame.shape}")
                else:
                    print(f"⚠️ 捕获失败 (尝试 {attempt + 1}/{max_attempts})")

                # 短暂等待后重试
                if attempt < max_attempts - 1:
                    time.sleep(0.1)

            print("❌ 多次尝试后仍无法获取有效图像")
            return None

        except Exception as e:
            print(f"❌ 捕获图像时出错: {e}")
            import traceback
            traceback.print_exc()
            return None


    
    def _process_qa_reply_new(self, user_text):
        """处理问答回复 - 新的独立实现"""
        try:
            # 构建对话上下文
            conversation = []
            if hasattr(self, 'qa_messages'):
                for msg in self.qa_messages[-10:]:  # 只保留最近10条消息
                    role = "assistant" if msg['is_ai'] else "user"
                    conversation.append({"role": role, "content": msg['text']})
            
            # 添加当前用户消息
            conversation.append({"role": "user", "content": user_text})
            
            # 使用qa_deepseek_client进行对话
            if hasattr(self, 'qa_deepseek_client') and self.qa_deepseek_client:
                response = self.qa_deepseek_client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[
                        {"role": "system", "content": "你是睿课云眸学习助手，专门帮助学生解答学习问题。请用简洁、友好的语言回答。"},
                        *conversation
                    ],
                    max_tokens=1000,
                    temperature=0.7
                )
                ai_reply = response.choices[0].message.content.strip()
            else:
                ai_reply = "AI助手暂时无法处理您的问题，请稍后重试。"
            
            # 在主线程中添加AI回复
            self.after(0, lambda: self.add_qa_message(ai_reply, is_ai=True))
            
        except Exception as e:
            print(f"处理问答回复时出错: {e}")
            error_msg = "抱歉，AI助手暂时无法回复，请稍后重试。"
            self.after(0, lambda: self.add_qa_message(error_msg, is_ai=True))
    
    def _process_deepseek_reply(self, user_text, placeholder_id=None):
        """处理新DeepSeek API回复 - 包含状态同步功能"""
        try:
            # 使用优化的API客户端
            if not hasattr(self, 'qa_deepseek_client'):
                self._init_qa_deepseek_client()
            
            if hasattr(self, 'qa_deepseek_client') and self.qa_deepseek_client:
                try:
                    # 使用包含状态同步的对话上下文构建方法
                    messages = self._build_qa_conversation_context(user_text)
                    
                    # 使用优化的API客户端调用
                    response = self.qa_deepseek_client.chat_completion_with_timeout(
                        model="deepseek-chat",
                        messages=messages,
                        max_tokens=1500,
                        temperature=0.7,
                        stream=False
                    )
                    
                    ai_reply = response.choices[0].message.content.strip()
                    
                    # 在主线程中更新UI
                    if placeholder_id:
                        self.after(0, lambda: self.update_qa_placeholder(placeholder_id, ai_reply))
                    else:
                        self.after(0, lambda: self.add_qa_message(ai_reply, is_ai=True))
                        
                except TimeoutError as timeout_error:
                    print(f"DeepSeek API超时: {timeout_error}")
                    error_msg = "请求超时，请稍后重试。"
                    if placeholder_id:
                        self.after(0, lambda: self.update_qa_placeholder(placeholder_id, error_msg))
                    else:
                        self.after(0, lambda: self.add_qa_message(error_msg, is_ai=True))
                        
                except Exception as api_error:
                    print(f"DeepSeek API调用错误: {api_error}")
                    error_msg = "网络连接或API服务暂时不可用，请稍后重试。"
                    if placeholder_id:
                        self.after(0, lambda: self.update_qa_placeholder(placeholder_id, error_msg))
                    else:
                        self.after(0, lambda: self.add_qa_message(error_msg, is_ai=True))
            else:
                error_msg = "AI助手暂不可用，请检查网络连接。"
                if placeholder_id:
                    self.after(0, lambda: self.update_qa_placeholder(placeholder_id, error_msg))
                else:
                    self.after(0, lambda: self.add_qa_message(error_msg, is_ai=True))
                
        except Exception as e:
            print(f"处理DeepSeek回复时出错: {e}")
            error_msg = f"抱歉，处理您的问题时出现错误，请重试。"
            
            if placeholder_id:
                self.after(0, lambda: self.update_qa_placeholder(placeholder_id, error_msg))
            else:
                self.after(0, lambda: self.add_qa_message(error_msg, is_ai=True))

    def _process_chat_reply(self, user_text):
        """处理AI回复（在后台线程中运行）"""
        try:
            # 添加"正在思考"占位符
            placeholder_id = f"thinking_{int(time.time() * 1000)}"
            self.add_ai_message("🤔 正在思考中...", is_placeholder=True, placeholder_id=placeholder_id)
            
            # 构建对话上下文
            context = self._build_conversation_context(user_text)
            
            # 使用新的DeepSeek Chat API进行聊天回复
            from openai import OpenAI
            
            chat_client = OpenAI(
                api_key=DEEPSEEK_CHAT_API_KEY,
                base_url=DEEPSEEK_CHAT_BASE_URL
            )
            
            response = chat_client.chat.completions.create(
                model="deepseek-chat",
                messages=context,
                temperature=0.7,
                max_tokens=1000
            )
            
            ai_reply = response.choices[0].message.content
            
            # 更新占位符消息
            self.update_placeholder_message(placeholder_id, ai_reply)
            
        except Exception as e:
            error_msg = f"抱歉，处理您的消息时出现错误：{str(e)}"
            if 'placeholder_id' in locals():
                self.update_placeholder_message(placeholder_id, error_msg)
            else:
                self.add_ai_message(error_msg)
    
    def _build_conversation_context(self, user_text):
        """构建对话上下文"""
        # 系统提示
        system_prompt = """你是睿课云眸AI学习助手，一个专业的学习监督和指导助手。你的主要职责是：

1. 监督学习者的学习状态和行为
2. 提供专业的学习建议和指导
3. 回答关于学习方法、效率提升等问题
4. 根据实时监测的行为给出针对性建议

请用温和、鼓励的语调与学习者交流，提供实用且个性化的建议。"""
        
        # 构建消息列表
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加行为上下文（如果有）
        if hasattr(self, 'current_behavior_context') and self.current_behavior_context:
            context_msg = f"当前监测到的学习状态：{self.current_behavior_context}"
            messages.append({"role": "system", "content": context_msg})
        
        # 添加最近的对话历史（最多5轮）
        recent_messages = self.messages[-10:] if len(self.messages) > 10 else self.messages
        for msg in recent_messages:
            if msg.get('role') in ['user', 'assistant']:
                messages.append({"role": msg['role'], "content": msg['content']})
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_text})
        
        return messages
    
    def add_user_message(self, text):
        """添加用户消息 - 修复布局管理器冲突"""
        message_frame = ctk.CTkFrame(
            self.chat_container,
            corner_radius=15,
            fg_color=("#e3f2fd", "#1a2e42")
        )
        message_frame.grid(row=self.chat_row, column=0, sticky="e", padx=(50, 15), pady=(10, 5))
        message_frame.grid_columnconfigure(0, weight=1)
        message_frame.grid_rowconfigure(1, weight=1)
        
        # 用户头像和姓名
        header_frame = ctk.CTkFrame(message_frame)
        header_frame.grid(row=0, column=0, sticky="ew", padx=15, pady=(10, 5))
        header_frame.grid_columnconfigure(0, weight=1)
        
        user_label = ctk.CTkLabel(
            header_frame,
            text="👤 您",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=("#1565c0", "#42a5f5")
        )
        user_label.grid(row=0, column=0, sticky="e")
        
        # 消息内容
        content_label = ctk.CTkLabel(
            message_frame,
            text=text,
            font=ctk.CTkFont(size=13),
            text_color=("#212121", "#e0e0e0"),
            wraplength=400,
            justify="center"
        )
        content_label.grid(row=1, column=0, sticky="ew", padx=15, pady=(0, 10))
        
        # 记录消息
        if not hasattr(self, 'messages'):
            self.messages = []
        self.messages.append({
            "role": "user",
            "content": text,
            "timestamp": datetime.now()
        })
        
        self.scroll_to_bottom()
    
    def update_placeholder_message(self, placeholder_id, new_text):
        """安全更新占位符消息"""
        if not hasattr(self, 'placeholder_map') or placeholder_id not in self.placeholder_map:
            return
            
        try:
            msg_index = self.placeholder_map[placeholder_id]
            
            # 检查消息索引是否有效
            if hasattr(self, 'messages') and 0 <= msg_index < len(self.messages):
                message_data = self.messages[msg_index]
                
                # 更新文本内容
                message_data["text"] = new_text
                if "text_label" in message_data and message_data["text_label"]:
                    try:
                        if message_data["text_label"].winfo_exists():
                            message_data["text_label"].configure(text=new_text)
                        else:
                            # UI组件已被销毁，清理引用
                            del self.placeholder_map[placeholder_id]
                            return
                    except:
                        # UI组件访问失败，清理引用
                        del self.placeholder_map[placeholder_id]
                        return
                
                # 更新消息角色和内容
                message_data['content'] = new_text
                message_data['role'] = 'assistant'
                if 'placeholder_id' in message_data:
                    del message_data['placeholder_id']
            else:
                # 索引无效，清理引用
                del self.placeholder_map[placeholder_id]
                return
            
            # 成功更新后清理占位符映射
            if placeholder_id in self.placeholder_map:
                del self.placeholder_map[placeholder_id]
                
        except Exception as e:
            # 清理可能损坏的placeholder映射
            try:
                if hasattr(self, 'placeholder_map') and placeholder_id in self.placeholder_map:
                    del self.placeholder_map[placeholder_id]
            except:
                pass
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        try:
            # 滚动问答聊天窗口到底部
            if hasattr(self, 'qa_chat_container'):
                self.after(100, lambda: self.qa_chat_container._parent_canvas.yview_moveto(1.0))
        except:
            pass
    
    def trigger_instant_analysis(self):
        """触发即时行为分析"""
        if hasattr(self, 'webcam_handler') and self.webcam_handler:
            self.webcam_handler.trigger_next_capture()
            self.update_status("🔍 正在分析当前行为...")
    
    def open_deepseek_chat(self):
        """保留原方法以防兼容性问题"""
        pass
    
    def update_deepseek_context(self, behavior_num, behavior_desc, analysis_text=""):
        """更新DeepSeek上下文信息并同步学生状态，包含姿态和情绪分析"""
        try:
            # 解析姿态和情绪信息
            posture_data, emotion_data = extract_posture_and_emotion_analysis(analysis_text)

            # 构建增强的上下文信息
            context_parts = [f"行为类型：{behavior_desc}"]

            # 添加姿态信息
            if posture_data and posture_data.get("raw_description"):
                context_parts.append(f"姿态状态：{posture_data['overall_evaluation']}")
                context_parts.append(f"姿态详情：{posture_data['raw_description'][:100]}...")

            # 添加情绪信息
            if emotion_data and emotion_data.get("raw_description"):
                context_parts.append(f"情绪状态：{emotion_data['primary_emotion']}")
                context_parts.append(f"情绪详情：{emotion_data['raw_description'][:100]}...")

            # 更新行为上下文
            self.current_behavior_context = "，".join(context_parts)

            # 存储姿态和情绪数据供后续使用
            if not hasattr(self, 'current_posture_data'):
                self.current_posture_data = {}
            if not hasattr(self, 'current_emotion_data'):
                self.current_emotion_data = {}

            self.current_posture_data = posture_data or {}
            self.current_emotion_data = emotion_data or {}

            # 更新学生状态 - 新增同步功能
            self.update_student_status(behavior_num, behavior_desc, analysis_text)

            # 在监督聊天窗口显示行为检测结果
            self.add_supervision_message(f"🔍 检测到：{behavior_desc}", is_system=True)

            # 显示状态同步成功消息
            self.add_supervision_message("✅ 状态已同步至问答系统", is_system=True)

        except Exception as e:
            print(f"更新DeepSeek上下文失败: {e}")

    def open_analysis_report(self):
        """打开学习状态分析报告窗口"""
        try:
            # 检查是否已经有分析窗口打开
            if self.analysis_window and self.analysis_window.winfo_exists():
                # 如果窗口存在，则聚焦到该窗口
                self.analysis_window.focus()
                self.analysis_window.lift()
                return
            
            # 创建新的分析报告窗口
            self.analysis_window = LearningAnalysisWindow(self, self.learning_analytics)
            
            # 添加分析报告打开消息
            self.add_supervision_message("📊 学习分析报告已打开", is_system=True)
            
        except Exception as e:
            print(f"打开学习分析报告失败: {e}")
            # 显示错误消息
            error_msg = "分析报告打开失败，请稍后重试。"
            self.add_supervision_message(f"❌ {error_msg}", is_system=True)

    def update_student_status(self, behavior_num, behavior_desc, analysis_text=""):
        """更新学生实时状态数据"""
        try:
            current_time = datetime.now()
            
            # 更新当前状态
            self.current_student_status.update({
                "behavior": behavior_desc,
                "behavior_num": behavior_num,
                "last_detected_behavior": behavior_desc,
                "last_analysis_time": current_time,
                "current_activity": self._analyze_activity_from_behavior(behavior_desc)
            })
            
            # 更新注意力水平和专注度分数
            attention_level, concentration_score = self._calculate_attention_metrics(behavior_num)
            self.current_student_status["attention_level"] = attention_level
            self.current_student_status["concentration_score"] = concentration_score
            
            # 更新警告计数
            if behavior_num in ["2", "3", "4", "5", "6", "7", "8"]:
                self.current_student_status["warning_count"] += 1
            
            # 添加到行为历史
            behavior_record = {
                "time": current_time,
                "behavior": behavior_desc,
                "behavior_num": behavior_num,
                "analysis": analysis_text[:100] + "..." if len(analysis_text) > 100 else analysis_text,
                "attention_level": attention_level,
                "concentration_score": concentration_score
            }
            
            # 更新最近行为记录
            self.recent_behaviors.append(behavior_record)
            if len(self.recent_behaviors) > self.max_behavior_history:
                self.recent_behaviors = self.recent_behaviors[-self.max_behavior_history:]
                
            # 更新行为历史
            self.current_student_status["behavior_history"].append(behavior_record)
            if len(self.current_student_status["behavior_history"]) > 20:
                self.current_student_status["behavior_history"] = self.current_student_status["behavior_history"][-20:]
            
            # 新增：同步数据到学习分析系统
            try:
                self.learning_analytics.add_behavior_record(
                    behavior_num, behavior_desc, concentration_score, analysis_text
                )
            except Exception as e:
                print(f"同步数据到分析系统失败: {e}")
            
            print(f"学生状态已更新: {behavior_desc}, 注意力: {attention_level}, 专注度: {concentration_score}")
            
        except Exception as e:
            print(f"更新学生状态失败: {e}")
    
    def _analyze_activity_from_behavior(self, behavior_desc):
        """根据行为描述分析当前活动"""
        activity_mapping = {
            "正常学习": "专心学习中",
            "听讲": "认真听课",
            "低头": "可能在看书或做笔记",
            "东张西望": "注意力不集中",
            "趴桌子": "疲劳状态",
            "转头": "分心状态",
            "举手": "积极参与",
            "玩手机": "使用电子设备",
            "睡觉": "休息状态"
        }
        return activity_mapping.get(behavior_desc, "未知活动")
    
    def _calculate_attention_metrics(self, behavior_num):
        """根据行为编号计算注意力水平和专注度分数"""
        attention_mapping = {
            "1": ("高度集中", 95),  # 正常学习
            "2": ("轻微分散", 75),  # 低头
            "3": ("注意力分散", 60), # 东张西望
            "4": ("严重分散", 40),  # 趴桌子
            "5": ("极度分散", 30),  # 转头
            "6": ("积极参与", 90),  # 举手
            "7": ("注意力转移", 25), # 玩手机
            "8": ("完全分散", 10),  # 睡觉
        }
        return attention_mapping.get(behavior_num, ("未知", 50))

    def get_student_status_summary(self):
        """获取学生状态摘要，供问答系统使用"""
        try:
            status = self.current_student_status
            recent_behavior = self.recent_behaviors[-1] if self.recent_behaviors else None
            
            # 计算会话时长
            session_duration = datetime.now() - status["session_start_time"]
            duration_minutes = int(session_duration.total_seconds() / 60)
            
            # 生成状态摘要
            summary = f"""
当前学生状态摘要：
• 当前行为：{status['behavior']}
• 注意力水平：{status['attention_level']}
• 专注度分数：{status['concentration_score']}/100
• 当前活动：{status['current_activity']}
• 最后检测时间：{status['last_analysis_time'].strftime('%H:%M:%S') if status['last_analysis_time'] else '暂无'}
• 本次学习时长：{duration_minutes}分钟
• 本次警告次数：{status['warning_count']}次

最近行为记录："""
            
            # 添加最近5条行为记录
            for i, behavior in enumerate(self.recent_behaviors[-5:], 1):
                time_str = behavior['time'].strftime('%H:%M:%S')
                summary += f"\n{i}. {time_str} - {behavior['behavior']} (专注度:{behavior['concentration_score']})"
            
            if not self.recent_behaviors:
                summary += "\n暂无行为记录"
            
            # 添加专注度趋势分析
            if len(self.recent_behaviors) >= 2:
                summary += f"\n\n{self.get_attention_trend()}"
                
            return summary
            
        except Exception as e:
            print(f"获取学生状态摘要失败: {e}")
            return "无法获取学生状态信息"
    
    # === 问答聊天窗口方法 ===
    def add_qa_message(self, text, is_ai=True, placeholder_id=None):
        """添加问答消息到QA聊天区域 - 完全修复版本"""
        try:
            if not hasattr(self, 'qa_chat_container') or not self.qa_chat_container:
                print("QA聊天容器未初始化")
                return
                
            if not text or not text.strip():
                print("消息文本为空")
                return
            
            # 添加问答消息 - 完全修复的版本
            if not hasattr(self, 'qa_messages'):
                self.qa_messages = []
            
            # 计算当前行数 - 使用更安全的方法
            current_row = len(self.qa_messages)
            
            # 为了避免重叠，强制更新布局
            self.qa_chat_container.update_idletasks()
            
            # 创建外层容器 - 彻底避免重叠
            outer_container = ctk.CTkFrame(
                self.qa_chat_container,
                corner_radius=0,
                fg_color="transparent",
                border_width=0
            )
            
            # 使用更大的间距和更保守的布局
            outer_container.grid(
                row=current_row, 
                column=0, 
                sticky="ew", 
                padx=(10, 10),
                pady=(10, 25),  # 增加间距：上10px，下25px
                ipadx=5,  # 内部水平填充
                ipady=5   # 内部垂直填充
            )
            outer_container.grid_columnconfigure(0, weight=1)
            
            # 确保父容器正确配置行权重
            for i in range(current_row + 1):
                self.qa_chat_container.grid_rowconfigure(i, weight=0, minsize=50)
            
            # 根据发送者设置更美观的渐变样式
            if is_ai:
                bg_color = ("#f0f9ff", "#1e293b")  # 更柔和的蓝色渐变
                text_color = ("#0f172a", "#f1f5f9")
                border_color = ("#3b82f6", "#60a5fa")  # 现代蓝色
                sender_text = "🤖 AI学习助手"
                alignment = "w"
                padx = (15, 60)
                # AI消息使用透明阴影效果
                shadow_color = "transparent"
            else:
                bg_color = ("#ecfdf5", "#1f2937")  # 清新的绿色渐变
                text_color = ("#064e3b", "#d1fae5")
                border_color = ("#10b981", "#34d399")  # 现代绿色
                sender_text = "👤 您"
                alignment = "e"
                padx = (60, 15)
                # 用户消息使用透明阴影效果
                shadow_color = "transparent"
            
            # 创建消息容器
            msg_container = ctk.CTkFrame(
                outer_container,
                corner_radius=12,
                fg_color=bg_color,
                border_width=2,
                border_color=border_color
            )
            msg_container.grid(
                row=0,
                column=0,
                sticky="ew",
                padx=padx,
                pady=(12, 12)
            )
            
            # 配置消息容器内部网格
            msg_container.grid_columnconfigure(0, weight=1)
            msg_container.grid_rowconfigure(1, weight=1)  # 内容行可扩展
            
            # 优化的标题栏设计
            header_frame = ctk.CTkFrame(
                msg_container,
                corner_radius=8,
                fg_color="transparent"
            )
            header_frame.grid(row=0, column=0, sticky="ew", padx=15, pady=(15, 8))
            header_frame.grid_columnconfigure(0, weight=1)
            
            # 发送者标签（增强视觉效果）
            sender_label = ctk.CTkLabel(
                header_frame,
                text=sender_text,
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=border_color,
                anchor=alignment
            )
            sender_label.grid(row=0, column=0, sticky=f"{alignment}")
            
            # 时间戳（增加图标和更好的格式）
            time_str = datetime.now().strftime("🕐 %H:%M")
            time_label = ctk.CTkLabel(
                header_frame,
                text=time_str,
                font=ctk.CTkFont(size=11),
                text_color=("#6b7280", "#9ca3af"),
                anchor="e" if alignment == "w" else "w"
            )
            time_label.grid(row=0, column=1, sticky="e" if alignment == "w" else "w", padx=(15, 0) if alignment == "w" else (0, 15))
            
            # 创建内容区域背景
            content_bg = ctk.CTkFrame(
                msg_container,
                corner_radius=8,
                fg_color="transparent",
                border_width=0
            )
            content_bg.grid(row=1, column=0, sticky="ew", padx=15, pady=(0, 15))
            content_bg.grid_columnconfigure(0, weight=1)
            
            # 优化的消息内容显示
            content_label = ctk.CTkLabel(
                content_bg,
                text=text,
                font=ctk.CTkFont(size=14, weight="normal"),
                text_color=text_color,
                wraplength=380,  # 进一步减少换行长度，确保内容不溢出
                justify="left" if alignment == "w" else "right",
                anchor="nw" if alignment == "w" else "ne"  # 顶部对齐
            )
            content_label.grid(row=0, column=0, sticky="new", padx=20, pady=20)  # 增加更多内边距
            
            # 动态计算并设置高度 - 确保足够的空间
            lines = len(text.split('\n'))
            char_width = len(text)
            estimated_lines = max(lines, char_width // 40 + 1)  # 更保守的估算
            # 为欢迎消息和长文本提供更多空间
            base_height = 100 if "欢迎" in text or len(text) > 200 else 80
            dynamic_height = min(max(estimated_lines * 25 + base_height, base_height), 400)  # 增加行高和最大高度
            
            # 不设置固定高度，让容器自动调整
            # msg_container.configure(height=dynamic_height)
            
            # 存储消息数据
            message_data = {
                "role": "assistant" if is_ai else "user",
                "content": text,
                "text": text,
                "is_ai": is_ai,
                "timestamp": datetime.now(),
                "container": outer_container,
                "msg_frame": msg_container,
                "content_label": content_label,
                "row": current_row,
                "placeholder_id": placeholder_id
            }
            self.qa_messages.append(message_data)
            
            # 如果是占位符，保存引用（线程安全）
            if placeholder_id:
                with self.placeholder_lock:
                    if not hasattr(self, 'qa_placeholders'):
                        self.qa_placeholders = {}
                    msg_index = len(self.qa_messages) - 1
                    self.qa_placeholders[placeholder_id] = msg_index
                    print(f"✅ 占位符 {placeholder_id} 已创建，索引: {msg_index}")
                    print(f"📊 当前占位符数量: {len(self.qa_placeholders)}")
            
            # 更新父容器网格配置
            self.qa_chat_container.grid_columnconfigure(0, weight=1)
            
            # 强制多次更新布局确保完全渲染
            for _ in range(3):
                self.qa_chat_container.update_idletasks()
                self.update_idletasks()
            
            # 延迟滚动确保布局完成，使用更长的延迟
            self.after(300, self._enhanced_smooth_scroll_qa)
            
            # 额外的安全措施：再次延迟更新确保布局稳定
            self.after(100, lambda: self.qa_chat_container.update_idletasks())
            
            print(f"成功添加{'AI' if is_ai else '用户'}消息到第{current_row}行，高度:{dynamic_height}")
            
        except Exception as e:
            print(f"添加问答消息时出错: {e}")
            import traceback
            traceback.print_exc()
    def _enhanced_smooth_scroll_qa(self):
        """增强的平滑滚动QA聊天区域"""
        try:
            if hasattr(self, 'qa_chat_container') and self.qa_chat_container:
                # 多次更新确保布局完成
                for _ in range(3):
                    self.qa_chat_container.update_idletasks()
                    self.update_idletasks()
                
                # 获取canvas并分步滚动到底部
                canvas = self.qa_chat_container._parent_canvas
                if canvas:
                    # 平滑滚动动画
                    def smooth_scroll_step(current_step=0, total_steps=8):
                        try:
                            if current_step < total_steps:
                                progress = current_step / total_steps
                                # 使用easing函数让滚动更自然
                                eased_progress = 1 - (1 - progress) ** 3  # ease-out cubic
                                target_pos = eased_progress
                                canvas.yview_moveto(target_pos)
                                # 继续下一步
                                self.after(30, lambda: smooth_scroll_step(current_step + 1, total_steps))
                            else:
                                # 最终确保到达底部
                                canvas.yview_moveto(1.0)
                        except Exception as e:
                            print(f"滚动步骤出错: {e}")
                    
                    smooth_scroll_step()
                    
        except Exception as e:
            print(f"增强滚动出错: {e}")
    
    def update_qa_placeholder(self, placeholder_id, new_text):
        """更新问答占位符消息 - 线程安全增强版本"""
        # 使用线程锁保护占位符操作
        with self.placeholder_lock:
            try:
                print(f"🔄 尝试更新占位符: {placeholder_id}")
                print(f"📝 新文本长度: {len(new_text)} 字符")

                # 检查占位符是否存在
                if not hasattr(self, 'qa_placeholders'):
                    print(f"❌ qa_placeholders 属性不存在")
                    self._fallback_display_result(new_text)
                    return

                print(f"📊 当前占位符列表: {list(self.qa_placeholders.keys())}")

                if placeholder_id not in self.qa_placeholders:
                    print(f"❌ 占位符 {placeholder_id} 不在占位符列表中")
                    self._fallback_display_result(new_text)
                    return

                msg_index = self.qa_placeholders[placeholder_id]
                print(f"📍 占位符索引: {msg_index}")

                # 检查消息列表
                if not hasattr(self, 'qa_messages'):
                    print(f"❌ qa_messages 属性不存在")
                    self._fallback_display_result(new_text)
                    return

                print(f"📊 消息列表长度: {len(self.qa_messages)}")

                if not (0 <= msg_index < len(self.qa_messages)):
                    print(f"❌ 消息索引 {msg_index} 超出范围 [0, {len(self.qa_messages)})")
                    self._fallback_display_result(new_text)
                    return

                msg_data = self.qa_messages[msg_index]
                print(f"✅ 找到消息数据，当前文本: {msg_data.get('text', '')[:50]}...")

                # 更新文本内容
                msg_data['text'] = new_text
                msg_data['content'] = new_text

                # 更新UI标签
                if 'content_label' in msg_data and msg_data['content_label']:
                    try:
                        if msg_data['content_label'].winfo_exists():
                            msg_data['content_label'].configure(text=new_text)
                            print(f"✅ UI标签已更新")
                        else:
                            print(f"⚠️ UI标签已被销毁")
                            self._fallback_display_result(new_text, "UI标签已被销毁")
                            return
                    except Exception as ui_error:
                        print(f"❌ 更新UI标签时出错: {ui_error}")
                        self._fallback_display_result(new_text, f"UI标签更新错误: {ui_error}")
                        return
                else:
                    print(f"⚠️ 消息数据中没有content_label")
                    self._fallback_display_result(new_text)
                    return

                # 标记占位符已完成，但保留引用以支持多次更新
                msg_data['placeholder_completed'] = True
                # 注意：不删除占位符引用，允许后续更新

                print(f"✅ 占位符 {placeholder_id} 更新成功: {new_text[:50]}...")
                print(f"📊 占位符保留在列表中，支持后续更新")

            except Exception as e:
                print(f"❌ 更新占位符时出错: {e}")
                import traceback
                traceback.print_exc()
                # 使用备用显示方案
                self._fallback_display_result(new_text)

    def _safe_ui_update(self, update_func):
        """线程安全的UI更新方法"""
        try:
            import threading

            # 检查是否在主线程中
            if threading.current_thread() is threading.main_thread():
                # 在主线程中直接执行
                update_func()
            else:
                # 在后台线程中，使用after方法调度到主线程
                self.after(0, update_func)

        except Exception as ui_error:
            print(f"❌ 线程安全UI更新失败: {ui_error}")
            # 尝试直接调用
            try:
                self.after(0, update_func)
            except Exception as fallback_error:
                print(f"❌ UI更新备用方案也失败: {fallback_error}")

    def _safe_placeholder_update(self, placeholder_id, message):
        """安全的占位符更新方法 - 线程安全防止意外删除"""
        try:
            print(f"🔄 安全更新占位符: {placeholder_id}")

            # 使用健康检查验证占位符状态
            is_healthy, health_msg = self._validate_placeholder_health(placeholder_id)
            if not is_healthy:
                print(f"⚠️ 占位符 {placeholder_id} 健康检查失败: {health_msg}")
                self._fallback_display_result(message, f"健康检查失败: {health_msg}")
                return

            print(f"✅ 占位符 {placeholder_id} 健康检查通过: {health_msg}")

            # 定义更新函数（增强版本）
            def update_placeholder():
                try:
                    # 多层占位符健康检查（防止竞态条件）
                    with self.placeholder_lock:
                        # 第一层：基本存在性检查
                        if not hasattr(self, 'qa_placeholders') or placeholder_id not in self.qa_placeholders:
                            print(f"⚠️ 占位符 {placeholder_id} 在更新时已不存在，使用备用显示")
                            self._fallback_display_result(message, "占位符在更新时已不存在")
                            return

                        # 第二层：索引有效性检查
                        msg_index = self.qa_placeholders[placeholder_id]
                        if not hasattr(self, 'qa_messages') or not (0 <= msg_index < len(self.qa_messages)):
                            print(f"⚠️ 占位符 {placeholder_id} 索引无效 ({msg_index})，使用备用显示")
                            self._fallback_display_result(message, f"占位符索引无效: {msg_index}")
                            return

                        # 第三层：消息数据完整性检查
                        msg_data = self.qa_messages[msg_index]
                        if not isinstance(msg_data, dict) or msg_data.get('placeholder_id') != placeholder_id:
                            print(f"⚠️ 占位符 {placeholder_id} 数据不一致，使用备用显示")
                            self._fallback_display_result(message, "占位符数据不一致")
                            return

                    # 所有检查通过，执行更新
                    self.update_qa_placeholder(placeholder_id, message)
                    print(f"✅ 占位符 {placeholder_id} 更新成功")

                except Exception as update_error:
                    print(f"❌ 占位符更新失败: {update_error}")
                    import traceback
                    traceback.print_exc()
                    self._fallback_display_result(message)

            # 使用线程安全的方式更新
            self._safe_ui_update(update_placeholder)

        except Exception as e:
            print(f"❌ 安全占位符更新失败: {e}")
            import traceback
            traceback.print_exc()
            self._fallback_display_result(message)

    def _safe_text_encode(self, text):
        """增强的安全文本编码处理，确保中文文本正确显示"""
        if not text:
            return ""

        try:
            # 确保文本是字符串类型
            if not isinstance(text, str):
                text = str(text)

            # 处理字节序列
            if isinstance(text, bytes):
                text = text.decode('utf-8', errors='replace')

            # 清理和规范化文本
            text = text.strip()

            # 移除控制字符，但保留中文字符和emoji
            import re
            # 保留中文字符范围：\u4e00-\u9fff，emoji范围：\U0001F600-\U0001F64F等
            text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

            # 处理可能的HTML实体编码
            import html
            text = html.unescape(text)

            # 处理可能的URL编码
            import urllib.parse
            try:
                # 只有当文本看起来像URL编码时才解码
                if '%' in text and any(c in text for c in '0123456789ABCDEF'):
                    decoded = urllib.parse.unquote(text, errors='ignore')
                    # 验证解码结果是否合理
                    if len(decoded) <= len(text) * 2:  # 防止异常膨胀
                        text = decoded
            except:
                pass  # 如果URL解码失败，保持原文本

            # 确保文本是有效的UTF-8编码
            text = text.encode('utf-8', errors='replace').decode('utf-8')

            # 最终清理：移除多余的空白字符
            text = re.sub(r'\s+', ' ', text).strip()

            return text

        except Exception as e:
            print(f"⚠️ 文本编码处理失败: {e}")
            # 返回安全的备用文本
            try:
                return str(text).strip() if text else ""
            except:
                return ""

    def _get_chinese_font_family(self):
        """获取支持中文的字体族名"""
        import platform

        system = platform.system()

        if system == "Windows":
            # Windows系统的中文字体
            fonts = ["Microsoft YaHei UI", "Microsoft YaHei", "SimHei", "SimSun"]
        elif system == "Darwin":  # macOS
            # macOS系统的中文字体
            fonts = ["PingFang SC", "Hiragino Sans GB", "STHeiti", "Arial Unicode MS"]
        else:  # Linux
            # Linux系统的中文字体
            fonts = ["Noto Sans CJK SC", "WenQuanYi Micro Hei", "DejaVu Sans"]

        # 添加通用备选字体
        fonts.extend(["Arial", "Helvetica", "sans-serif"])

        # 返回第一个可用的字体（CTk会自动选择）
        return fonts[0]

    def _create_chinese_font(self, size=12, weight="normal"):
        """创建支持中文的字体对象"""
        try:
            font_family = self._get_chinese_font_family()
            return ctk.CTkFont(size=size, weight=weight, family=font_family)
        except Exception as e:
            print(f"⚠️ 创建中文字体失败: {e}")
            # 备用方案：使用默认字体
            return ctk.CTkFont(size=size, weight=weight)

    def _format_supervision_message(self, text):
        """格式化监督消息，提高可读性和结构化显示"""
        try:
            if not text:
                return ""

            import re

            # 如果消息很短，直接返回
            if len(text) <= 40:
                return text

            # 特殊处理：检测是否是系统欢迎消息（包含多个功能点）
            if "功能介绍" in text or "睿课云眸" in text:
                return self._format_welcome_message(text)

            # 特殊处理：检测是否是行为分析消息
            if any(keyword in text for keyword in ["检测到", "姿态状态", "情绪状态", "建议", "请"]):
                return self._format_behavior_message(text)

            # 通用消息格式化
            return self._format_general_message(text)

        except Exception as e:
            print(f"⚠️ 格式化监督消息失败: {e}")
            return text

    def _format_welcome_message(self, text):
        """格式化欢迎消息，优化居中对齐显示"""
        try:
            lines = text.split('\n')
            formatted_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 如果已经有emoji或符号开头，保持原样
                if re.match(r'^[🎯👀🔊📊🎭👁️💡🚀•·▪▫]', line):
                    formatted_lines.append(line)
                # 如果是功能点，添加项目符号，但为居中对齐优化格式
                elif any(keyword in line for keyword in ["自动检测", "识别", "提醒", "分析", "监控"]):
                    # 使用更适合居中显示的符号
                    formatted_lines.append(f"• {line}")
                else:
                    formatted_lines.append(line)

            return '\n'.join(formatted_lines)

        except Exception as e:
            print(f"⚠️ 格式化欢迎消息失败: {e}")
            return text

    def _format_behavior_message(self, text):
        """格式化行为分析消息，优化居中对齐显示"""
        try:
            # 检查是否包含多个信息点（降低长度要求以便更好地格式化）
            if "：" in text and len(text) > 30:
                # 按冒号分割，每个部分独立成行
                parts = text.split("：")
                if len(parts) > 1:
                    formatted_parts = []

                    for i, part in enumerate(parts):
                        if i == 0:
                            # 第一部分（通常是标题）
                            formatted_parts.append(f"{part.strip()}：")
                        else:
                            # 后续部分，为居中对齐优化格式（移除缩进）
                            clean_part = part.strip()
                            if clean_part:
                                # 如果包含建议或警告，添加特殊符号但不缩进
                                if any(keyword in clean_part for keyword in ["建议", "请", "注意", "应该"]):
                                    formatted_parts.append(f"💡 {clean_part}")
                                else:
                                    formatted_parts.append(f"▪ {clean_part}")

                    return '\n'.join(formatted_parts)

            # 如果文本较长但没有明显分割点，按句号分割
            if len(text) > 80 and "。" in text:
                sentences = text.split("。")
                formatted_sentences = []

                for sentence in sentences:
                    sentence = sentence.strip()
                    if sentence:
                        if not sentence.endswith("。"):
                            sentence += "。"
                        formatted_sentences.append(sentence)

                # 如果有多个句子，分行显示
                if len(formatted_sentences) > 1:
                    return '\n'.join(formatted_sentences)

            return text

        except Exception as e:
            print(f"⚠️ 格式化行为消息失败: {e}")
            return text

    def _format_general_message(self, text):
        """格式化通用消息"""
        try:
            # 如果文本很长，尝试智能分行
            if len(text) > 100:
                # 按标点符号分割
                import re
                sentences = re.split(r'([。！？，；])', text)

                formatted_lines = []
                current_line = ""

                for i in range(0, len(sentences), 2):
                    if i < len(sentences):
                        sentence = sentences[i]
                        punctuation = sentences[i+1] if i+1 < len(sentences) else ""

                        full_sentence = sentence + punctuation

                        # 如果当前行加上新句子太长，换行
                        if len(current_line + full_sentence) > 50 and current_line:
                            formatted_lines.append(current_line.strip())
                            current_line = full_sentence
                        else:
                            current_line += full_sentence

                # 添加最后一行
                if current_line.strip():
                    formatted_lines.append(current_line.strip())

                # 如果分割后有多行，返回分行结果
                if len(formatted_lines) > 1:
                    return '\n'.join(formatted_lines)

            return text

        except Exception as e:
            print(f"⚠️ 格式化通用消息失败: {e}")
            return text

    def _validate_placeholder_health(self, placeholder_id):
        """验证占位符的健康状态"""
        try:
            with self.placeholder_lock:
                # 检查占位符字典是否存在
                if not hasattr(self, 'qa_placeholders'):
                    return False, "qa_placeholders属性不存在"

                # 检查占位符是否在字典中
                if placeholder_id not in self.qa_placeholders:
                    return False, f"占位符{placeholder_id}不在字典中"

                # 检查索引有效性
                msg_index = self.qa_placeholders[placeholder_id]
                if not hasattr(self, 'qa_messages'):
                    return False, "qa_messages属性不存在"

                if not (0 <= msg_index < len(self.qa_messages)):
                    return False, f"索引{msg_index}超出范围[0, {len(self.qa_messages)})"

                # 检查消息数据完整性
                msg_data = self.qa_messages[msg_index]
                if not isinstance(msg_data, dict):
                    return False, "消息数据不是字典类型"

                if msg_data.get('placeholder_id') != placeholder_id:
                    return False, f"消息数据中的placeholder_id不匹配: {msg_data.get('placeholder_id')} != {placeholder_id}"

                # 检查UI组件是否存在
                if 'content_label' in msg_data and msg_data['content_label']:
                    try:
                        if not msg_data['content_label'].winfo_exists():
                            return False, "UI标签已被销毁"
                    except Exception:
                        return False, "UI标签状态异常"

                return True, "占位符健康状态良好"

        except Exception as e:
            return False, f"健康检查异常: {e}"

    def _fallback_display_result(self, result_text, reason="未知原因"):
        """智能备用结果显示方案 - 当占位符更新失败时使用"""
        try:
            print(f"🔄 使用备用显示方案，原因: {reason}")

            # 根据失败原因选择不同的备用策略
            if "健康检查失败" in reason or "不存在" in reason:
                # 占位符丢失，创建新的消息
                fallback_text = f"📋 题目解析结果：\n\n{result_text}"
                print("📝 占位符丢失，创建新消息显示结果")
            elif "UI标签" in reason:
                # UI组件问题，尝试重新创建
                fallback_text = f"🔄 重新显示结果：\n\n{result_text}"
                print("🔄 UI组件问题，重新创建显示")
            else:
                # 其他原因，使用标准备用显示
                fallback_text = f"✨ 解题完成：\n\n{result_text}"
                print("✨ 使用标准备用显示")

            # 使用线程安全的方式添加新的AI消息显示结果
            self._safe_ui_update(lambda: self.add_qa_message(fallback_text, is_ai=True))
            print("✅ 备用显示方案执行成功")

            # 记录备用显示使用情况
            self._log_fallback_usage(reason)

        except Exception as fallback_error:
            print(f"❌ 备用显示方案也失败了: {fallback_error}")
            import traceback
            traceback.print_exc()

            # 最后的备用方案：更新状态栏
            try:
                status_msg = f"题目解析完成，但显示结果时出现问题: {reason}"
                self._safe_ui_update(lambda: self.update_status(status_msg))
            except Exception as final_error:
                print(f"❌ 最终备用方案也失败: {final_error}")

    def _log_fallback_usage(self, reason):
        """记录备用显示使用情况，用于问题分析"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] 备用显示触发: {reason}\n"

            # 记录到文件
            with open("placeholder_fallback.log", "a", encoding="utf-8") as f:
                f.write(log_entry)

            # 统计备用显示使用次数
            if not hasattr(self, 'fallback_count'):
                self.fallback_count = 0
            self.fallback_count += 1

            print(f"📊 备用显示使用次数: {self.fallback_count}")

        except Exception as log_error:
            print(f"⚠️ 记录备用显示日志失败: {log_error}")

    @handle_exceptions()
    def _process_qa_reply(self, user_text):
        """处理问答AI回复 - 使用独立的DeepSeek API客户端"""
        try:
            # 初始化独立的DeepSeek客户端（如果还没有）
            if not hasattr(self, 'qa_deepseek_client'):
                self._init_qa_deepseek_client()
            
            if hasattr(self, 'qa_deepseek_client') and self.qa_deepseek_client:
                try:
                    # 构建请求消息
                    system_prompt = """你是睿课云眸AI学习助手，专门负责回答学生的学习问题。请提供准确、详细且易懂的解答。回复应简洁明了，控制在200字以内。"""
                    
                    messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_text}
                    ]
                    
                    # 使用优化的API客户端调用
                    response = self.qa_deepseek_client.chat_completion_with_timeout(
                        model="deepseek-chat",
                        messages=messages,
                        temperature=0.7,
                        max_tokens=1024,
                        stream=False
                    )
                    
                    ai_reply = response.choices[0].message.content.strip()
                    
                    if ai_reply and len(ai_reply) > 0:
                        # 在主线程中添加AI回复
                        self.after(0, lambda reply=ai_reply: self.add_qa_message(reply, is_ai=True))
                    else:
                        self.after(0, lambda: self.add_qa_message("抱歉，我现在无法回答您的问题。请稍后再试。", is_ai=True))
                        
                except TimeoutError as timeout_error:
                    print(f"DeepSeek API超时: {timeout_error}")
                    self.after(0, lambda: self.add_qa_message("请求超时，请稍后重试。", is_ai=True))
                except Exception as api_error:
                    print(f"DeepSeek API调用错误: {api_error}")
                    self.after(0, lambda: self.add_qa_message("网络连接或API服务暂时不可用，请稍后重试。", is_ai=True))
            else:
                self.after(0, lambda: self.add_qa_message("AI助手暂不可用，请检查网络连接。", is_ai=True))
                
        except Exception as e:
            print(f"处理AI回复时出错: {e}")
            error_msg = "抱歉，处理您的问题时出现错误，请重试。"
            self.after(0, lambda: self.add_qa_message(error_msg, is_ai=True))
    
    @handle_exceptions()
    def _init_qa_deepseek_client(self):
        """初始化独立的问答DeepSeek API客户端"""
        try:
            # 使用优化的API客户端
            self.qa_deepseek_client = OptimizedAPIClient(
                api_key=DEEPSEEK_NEW_CHAT_API_KEY,
                base_url=DEEPSEEK_NEW_CHAT_BASE_URL,
                timeout=API_TIMEOUT
            )
            
            # 测试API连接
            test_response = self.qa_deepseek_client.chat_completion_with_timeout(
                model="deepseek-chat",
                messages=[{"role": "user", "content": "测试连接"}],
                max_tokens=5
            )
            print("DeepSeek问答API连接测试成功")
            
            print("问答DeepSeek API客户端初始化成功")
            
        except Exception as e:
            print(f"初始化问答DeepSeek客户端失败: {e}")
            self.qa_deepseek_client = None

    def _init_question_system(self):
        """初始化题目识别系统"""
        if QUESTION_OCR_AVAILABLE:
            try:
                # 确保DeepSeek客户端已初始化
                if not hasattr(self, 'qa_deepseek_client'):
                    self._init_qa_deepseek_client()

                self.question_system = get_question_system(self.qa_deepseek_client)
                if self.question_system and self.question_system.is_available:
                    print("✅ 题目识别系统初始化成功")
                else:
                    print("⚠️ 题目识别系统初始化失败 - OCR不可用")
            except Exception as question_error:
                print(f"⚠️ 题目识别系统初始化失败: {question_error}")
                self.question_system = None
        else:
            print("⚠️ 题目识别模块不可用，请安装OCR依赖库")
    
    def _build_qa_conversation_context(self, user_text):
        """构建问答对话上下文，包含学生状态信息"""
        # 检查是否询问学生状态
        is_status_query = self._is_student_status_query(user_text)
        
        # 基础系统提示
        base_system_prompt = """你是睿课云眸AI学习助手，专门负责回答学生的学习问题。你的职责包括：

1. 回答各学科的知识问题（数学、语文、英语、物理、化学、生物、历史、地理等）
2. 提供学习方法和技巧建议
3. 帮助制定学习计划和目标
4. 解释概念、公式、定理等
5. 提供解题思路和方法
6. 回答关于学生当前学习状态、行为表现、注意力情况等监督相关问题
7. 基于学生的姿态分析提供健康坐姿建议和纠正提醒
8. 根据学生的情绪状态提供心理支持和鼓励

请用耐心、专业的语调回答问题，提供详细且易懂的解答，适合学生理解。"""

        # 如果是状态查询，添加学生状态信息
        if is_status_query:
            student_status = self.get_student_status_summary()
            system_prompt = base_system_prompt + f"\n\n当前学生状态信息：\n{student_status}\n\n请根据以上实时状态信息回答用户的问题。"
        else:
            system_prompt = base_system_prompt
        
        # 构建消息列表
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加最近的对话历史（最多8轮）
        recent_messages = self.qa_messages[-16:] if len(self.qa_messages) > 16 else self.qa_messages
        for msg in recent_messages:
            if msg.get('role') in ['user', 'assistant']:
                messages.append({"role": msg['role'], "content": msg['content']})
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_text})
        
        return messages

    def _is_student_status_query(self, user_text):
        """判断用户是否在询问学生状态相关问题"""
        # 主要状态关键词
        status_keywords = [
            "学生", "状态", "行为", "注意力", "专注", "集中", "分心", "表现", 
            "学习情况", "听课", "认真", "专心", "走神", "开小差", "玩手机",
            "睡觉", "低头", "东张西望", "举手", "监督", "检测", "分析",
            "警告", "提醒", "会话", "时长", "历史", "记录"
        ]
        
        # 状态查询常用词组合
        status_phrases = [
            "怎么样", "如何", "现在", "当前", "正在", "干什么", "做什么"
        ]
        
        # 排除关键词（非状态查询的学习问题）
        exclude_keywords = [
            "题目", "解题", "数学题", "练习", "作业", "公式", "定理", 
            "概念", "知识点", "方法", "技巧", "原理", "算法"
        ]
        
        user_text_lower = user_text.lower()
        
        # 如果包含排除关键词，则不是状态查询
        if any(keyword in user_text_lower for keyword in exclude_keywords):
            return False
        
        # 检查是否包含状态关键词
        has_status_keyword = any(keyword in user_text_lower for keyword in status_keywords)
        
        # 检查是否包含状态查询词组
        has_status_phrase = any(phrase in user_text_lower for phrase in status_phrases)
        
        # 同时包含状态关键词和查询词组，或者包含明确的状态关键词
        return has_status_keyword or (has_status_phrase and ("我" in user_text_lower or "学习" in user_text_lower))

    @handle_exceptions(default_return=True)
    def verify_status_sync_integrity(self):
        """验证状态同步系统的完整性"""
        try:
            # 检查必要的状态变量是否存在
            required_attrs = [
                'current_student_status', 'recent_behaviors', 
                'max_behavior_history', 'current_behavior_context'
            ]
            
            for attr in required_attrs:
                if not hasattr(self, attr):
                    print(f"警告：缺少状态同步属性 {attr}")
                    return False
            
            # 检查状态数据结构完整性
            required_status_keys = [
                'behavior', 'behavior_num', 'attention_level', 
                'last_detected_behavior', 'current_activity', 
                'concentration_score', 'warning_count'
            ]
            
            for key in required_status_keys:
                if key not in self.current_student_status:
                    print(f"警告：学生状态缺少关键字段 {key}")
                    # 自动修复缺失字段
                    self._repair_student_status()
                    break
            
            print("✅ 状态同步系统完整性验证通过")
            return True
            
        except Exception as e:
            print(f"状态同步完整性验证失败: {e}")
            return False

    def _repair_student_status(self):
        """修复损坏的学生状态数据"""
        try:
            default_status = {
                "behavior": "正常学习",
                "behavior_num": "1", 
                "attention_level": "集中",
                "last_detected_behavior": "",
                "last_analysis_time": None,
                "current_activity": "待检测",
                "concentration_score": 85,
                "behavior_history": [],
                "warning_count": 0,
                "session_start_time": datetime.now()
            }
            
            # 合并现有状态和默认状态
            for key, value in default_status.items():
                if key not in self.current_student_status:
                    self.current_student_status[key] = value
                    
            print("✅ 学生状态数据已修复")
            
        except Exception as e:
            print(f"修复学生状态失败: {e}")
            # 完全重置状态
            self.current_student_status = {
                "behavior": "正常学习",
                "behavior_num": "1", 
                "attention_level": "集中",
                "last_detected_behavior": "",
                "last_analysis_time": None,
                "current_activity": "待检测",
                "concentration_score": 85,
                "behavior_history": [],
                "warning_count": 0,
                "session_start_time": datetime.now()
            }
    
    # === 监督聊天窗口方法 ===
    def add_supervision_message(self, text, is_system=False):
        """添加监督消息到监督反馈区域 - 修复文本编码问题"""
        try:
            if not hasattr(self, 'supervision_chat_container') or not self.supervision_chat_container:
                print("监督聊天容器未初始化")
                return

            if not hasattr(self, 'supervision_messages'):
                self.supervision_messages = []

            # 修复文本编码问题
            if text:
                # 确保文本是字符串类型
                if not isinstance(text, str):
                    text = str(text)

                # 处理可能的编码问题
                try:
                    # 如果文本包含字节序列，尝试解码
                    if isinstance(text, bytes):
                        text = text.decode('utf-8', errors='replace')

                    # 清理和规范化文本
                    text = text.strip()

                    # 移除可能导致显示问题的控制字符
                    import re
                    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

                    # 确保文本是有效的UTF-8
                    text = text.encode('utf-8', errors='replace').decode('utf-8')

                except Exception as encoding_error:
                    print(f"⚠️ 文本编码处理失败: {encoding_error}")
                    text = str(text).strip()

            if not text:
                return
            
            # 计算当前行数
            current_row = len(self.supervision_messages)
            
            # 配置样式
            if is_system:
                frame_bg = ("#fff7ed", "#1c1917")
                border_color = ("#f97316", "#fb923c")
                text_color = ("#9a3412", "#fed7aa")
                icon = "⚡"
                sender_text = "系统通知"
            else:
                frame_bg = ("#fef3c7", "#374151")
                border_color = ("#f59e0b", "#6b7280")
                text_color = ("#92400e", "#f3f4f6")
                icon = "📢"
                sender_text = "监督提醒"
            
            # 创建外层容器防止重叠
            outer_container = ctk.CTkFrame(
                self.supervision_chat_container,
                fg_color="transparent"
            )
            outer_container.grid(
                row=current_row,
                column=0,
                sticky="ew",
                pady=(0, 6),
                padx=5
            )
            outer_container.grid_columnconfigure(0, weight=1)
            
            # 创建消息容器
            message_frame = ctk.CTkFrame(
                outer_container,
                corner_radius=12,
                fg_color=frame_bg,
                border_width=1,
                border_color=border_color
            )
            message_frame.grid(row=0, column=0, sticky="ew", padx=(10, 10), pady=3)
            message_frame.grid_columnconfigure(0, weight=1)
            
            # 标题行
            header_frame = ctk.CTkFrame(message_frame, fg_color="transparent")
            header_frame.grid(row=0, column=0, sticky="ew", padx=12, pady=(8, 4))
            header_frame.grid_columnconfigure(0, weight=1)
            
            # 图标和发送者 - 增强中文支持
            try:
                # 确保图标和发送者文本编码正确
                safe_icon = self._safe_text_encode(icon)
                safe_sender_text = self._safe_text_encode(sender_text)
                title_text = f"{safe_icon} {safe_sender_text}"

                title_label = ctk.CTkLabel(
                    header_frame,
                    text=title_text,
                    font=self._create_chinese_font(size=11, weight="bold"),  # 使用跨平台中文字体
                    text_color=border_color,
                    anchor="w"
                )
                title_label.grid(row=0, column=0, sticky="w")
            except Exception as e:
                print(f"⚠️ 创建标题标签失败: {e}")
                # 备用方案：使用简化文本
                title_label = ctk.CTkLabel(
                    header_frame,
                    text="系统消息" if is_system else "监督提醒",
                    font=ctk.CTkFont(size=11, weight="bold"),
                    text_color=border_color,
                    anchor="w"
                )
                title_label.grid(row=0, column=0, sticky="w")
            
            # 时间戳
            time_str = datetime.now().strftime("%H:%M:%S")
            time_label = ctk.CTkLabel(
                header_frame,
                text=time_str,
                font=self._create_chinese_font(size=9),  # 使用统一的字体
                text_color=("#6b7280", "#9ca3af"),
                anchor="e"
            )
            time_label.grid(row=0, column=1, sticky="e")
            
            # 消息内容 - 优化显示格式和可读性
            try:
                # 再次确保文本编码安全（双重保险）
                safe_text = self._safe_text_encode(text)

                # 结构化消息处理：将长消息拆分为多行
                formatted_text = self._format_supervision_message(safe_text)

                # 优化字体大小：增大到14-16px提高可读性
                text_length = len(formatted_text)
                if text_length > 150:
                    font_size = 14  # 长文本使用14px
                elif text_length > 80:
                    font_size = 15  # 中等文本使用15px
                else:
                    font_size = 16  # 短文本使用16px

                content_label = ctk.CTkLabel(
                    message_frame,
                    text=formatted_text,
                    font=self._create_chinese_font(size=font_size),  # 使用跨平台中文字体
                    text_color=text_color,
                    wraplength=320,  # 增加文本宽度
                    justify="center",  # 居中对齐，提升视觉效果
                    anchor="center"  # 居中锚点
                )
                content_label.grid(row=1, column=0, sticky="ew", padx=15, pady=(8, 12))  # 增加内边距

            except Exception as e:
                print(f"⚠️ 创建内容标签失败: {e}")
                # 备用方案：使用简化的标签
                content_label = ctk.CTkLabel(
                    message_frame,
                    text="消息显示异常",
                    font=self._create_chinese_font(size=15),  # 使用统一的字体和大小
                    text_color=text_color,
                    wraplength=320,  # 与主方案保持一致
                    justify="center",  # 与主方案保持一致
                    anchor="center"  # 与主方案保持一致
                )
                content_label.grid(row=1, column=0, sticky="ew", padx=15, pady=(8, 12))  # 与主方案保持一致
            
            # 存储消息数据
            message_data = {
                "content": text,
                "timestamp": datetime.now(),
                "is_system": is_system,
                "container": outer_container,
                "frame": message_frame,
                "row": current_row
            }
            self.supervision_messages.append(message_data)
            
            # 更新布局并滚动
            self.supervision_chat_container.grid_columnconfigure(0, weight=1)
            self.supervision_chat_container.update_idletasks()
            self.after(80, self._enhanced_smooth_scroll_supervision)
            
            print(f"添加监督消息: {text[:30]}...")
            
        except Exception as e:
            print(f"添加监督消息时出错: {e}")
            import traceback
            traceback.print_exc()

    def get_recent_behavior_summary(self, count=5):
        """获取最近几条行为记录的简要摘要"""
        try:
            if not self.recent_behaviors:
                return "暂无行为记录"
            
            recent = self.recent_behaviors[-count:]
            summary_lines = []
            
            for behavior in recent:
                time_str = behavior['time'].strftime('%H:%M')
                behavior_desc = behavior['behavior']
                attention = behavior['attention_level']
                summary_lines.append(f"{time_str} {behavior_desc} ({attention})")
            
            return "最近行为：\n" + "\n".join(summary_lines)
            
        except Exception as e:
            print(f"获取行为摘要失败: {e}")
            return "无法获取行为记录"

    def get_attention_trend(self):
        """分析注意力变化趋势"""
        try:
            if len(self.recent_behaviors) < 2:
                return "数据不足，无法分析趋势"
            
            recent_scores = [b['concentration_score'] for b in self.recent_behaviors[-5:]]
            avg_score = sum(recent_scores) / len(recent_scores)
            
            if len(recent_scores) >= 2:
                trend = recent_scores[-1] - recent_scores[-2]
                if trend > 10:
                    trend_desc = "显著提升"
                elif trend > 0:
                    trend_desc = "略有提升"
                elif trend < -10:
                    trend_desc = "显著下降"
                elif trend < 0:
                    trend_desc = "略有下降"
                else:
                    trend_desc = "保持稳定"
            else:
                trend_desc = "暂无趋势"
            
            return f"平均专注度：{avg_score:.1f}/100，趋势：{trend_desc}"
            
        except Exception as e:
            print(f"分析注意力趋势失败: {e}")
            return "趋势分析失败"

    def _enhanced_smooth_scroll_supervision(self):
        """增强的平滑滚动监督区域"""
        try:
            if hasattr(self, 'supervision_chat_container') and self.supervision_chat_container:
                # 多次更新确保布局完成
                for _ in range(3):
                    self.supervision_chat_container.update_idletasks()
                    self.update_idletasks()
                
                # 获取canvas并平滑滚动
                canvas = self.supervision_chat_container._parent_canvas
                if canvas:
                    def smooth_scroll_step(current_step=0, total_steps=6):
                        try:
                            if current_step < total_steps:
                                progress = current_step / total_steps
                                # 使用缓动函数
                                eased_progress = 1 - (1 - progress) ** 2  # ease-out quadratic
                                canvas.yview_moveto(eased_progress)
                                # 继续下一步
                                self.after(25, lambda: smooth_scroll_step(current_step + 1, total_steps))
                            else:
                                # 确保到达底部
                                canvas.yview_moveto(1.0)
                        except Exception as e:
                            print(f"监督区域滚动步骤出错: {e}")
                    
                    smooth_scroll_step()
                    
        except Exception as e:
            print(f"监督区域增强滚动出错: {e}")

    def _smooth_scroll_qa(self):
        """保持原有QA滚动方法的兼容性"""
        self._enhanced_smooth_scroll_qa()
        
    def _smooth_scroll_supervision(self):
        """保持原有监督滚动方法的兼容性"""
        self._enhanced_smooth_scroll_supervision()
    
    def _scroll_supervision_to_bottom(self):
        """将监督反馈区域滚动到底部"""
        try:
            if hasattr(self, 'supervision_chat_container') and self.supervision_chat_container:
                self.supervision_chat_container._parent_canvas.yview_moveto(1.0)
        except Exception as e:
            print(f"滚动监督消息时出错: {e}")
            
    def update_status(self, text):
        """更新状态显示"""
        try:
            if hasattr(self, 'status_label') and self.status_label and self.status_label.winfo_exists():
                self.status_label.configure(text=f"✅ {text}")
            # 同时更新实时状态指示器
            if hasattr(self, 'realtime_status') and self.realtime_status and self.realtime_status.winfo_exists():
                if "分析" in text:
                    self.realtime_status.configure(text="🔍 分析中")
                elif "监控" in text or "监督" in text:
                    self.realtime_status.configure(text="🎯 监控中")
                elif "播放" in text or "语音" in text:
                    self.realtime_status.configure(text="🔊 语音中")
                elif "捕获" in text or "截图" in text:
                    self.realtime_status.configure(text="📸 采集中")
                else:
                    self.realtime_status.configure(text="🎯 监控中")
        except Exception as e:
            print(f"更新状态时出错: {e}")
            
    def update_preview(self, img):
        """更新摄像头预览"""
        try:
            if img is not None and hasattr(self, 'preview_label') and self.preview_label:
                # 检查UI组件是否还存在
                if hasattr(self.preview_label, 'winfo_exists') and self.preview_label.winfo_exists():
                    # 获取预览容器的实际尺寸
                    container_width = self.preview_label.winfo_width()
                    container_height = self.preview_label.winfo_height()
                    
                    # 获取容器实际尺寸，如果容器还没有渲染，使用适中的默认值
                    if container_width <= 1 or container_height <= 1:
                        # 使用适中的默认尺寸，保持7:3左右布局比例
                        target_width, target_height = 270, 200
                    else:
                        # 使用容器实际尺寸，减去适当边距
                        target_width = max(container_width - 10, 270)
                        target_height = max(container_height - 10, 200)
                    
                    img_copy = img.copy()
                    
                    # 计算最佳缩放比例，保持宽高比的同时最大化填充容器
                    img_width, img_height = img_copy.size
                    width_ratio = target_width / img_width
                    height_ratio = target_height / img_height
                    
                    # 选择较大的缩放比例以填满容器（可能会裁剪）
                    scale_ratio = max(width_ratio, height_ratio)
                    
                    # 计算缩放后的尺寸
                    new_width = int(img_width * scale_ratio)
                    new_height = int(img_height * scale_ratio)
                    
                    # 先缩放图像
                    img_copy = img_copy.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    
                    # 如果缩放后的图像比目标尺寸大，进行居中裁剪
                    if new_width > target_width or new_height > target_height:
                        left = max(0, (new_width - target_width) // 2)
                        top = max(0, (new_height - target_height) // 2)
                        right = left + target_width
                        bottom = top + target_height
                        img_copy = img_copy.crop((left, top, right, bottom))
                    
                    # 最终尺寸就是目标尺寸
                    preview_size = (target_width, target_height)
                    
                    # 转换为CTkImage，使用目标尺寸确保填满
                    actual_size = preview_size
                    ctk_img = ctk.CTkImage(
                        light_image=img_copy, 
                        dark_image=img_copy, 
                        size=actual_size
                    )
                    
                    # 更新预览标签
                    self.preview_label.configure(image=ctk_img, text="")
                    
                    # 保存引用防止垃圾回收
                    self.preview_label.image = ctk_img
        except Exception as e:
            print(f"更新摄像头预览时出错: {e}")
            
    def add_qa_welcome_message(self):
        """添加问答区域的欢迎消息，包含状态查询功能介绍"""
        welcome_text = """欢迎使用睿课云眸AI学习助手！

我可以帮助您：
• 解答学习中的各种问题
• 提供学习方法和技巧指导
• 制定个性化学习计划
• 分析学习难点并给出建议
• 📊 实时回答您的学习状态问题

📋 状态查询示例：
"我现在的学习状态怎么样？"
"我的注意力集中吗？"
"我今天的学习表现如何？"
"检测到了什么行为？"

请随时向我提问，我会结合监督数据为您提供专业的学习指导！"""
        
        self.add_qa_message(welcome_text, is_ai=True)
        
    def add_supervision_welcome_message(self):
        """添加监督区域的欢迎消息"""
        self.add_supervision_message("监督系统已启动，开始监控学习状态", is_system=True)
        


    def add_initial_messages_safe(self):
        """安全地添加初始欢迎消息，避免布局冲突 - 只保留监督系统欢迎消息"""
        try:
            # 首先清空所有现有消息状态，确保干净的开始
            if hasattr(self, 'qa_messages'):
                self.qa_messages.clear()
            else:
                self.qa_messages = []
                
            if hasattr(self, 'qa_placeholders'):
                self.qa_placeholders.clear()
            else:
                self.qa_placeholders = {}
                
            if hasattr(self, 'supervision_messages'):
                self.supervision_messages.clear()
            else:
                self.supervision_messages = []
            
            # 只添加监督欢迎消息，避免重叠问题
            def add_supervision_welcome():
                try:
                    if hasattr(self, 'supervision_chat_container') and self.supervision_chat_container:
                        supervision_welcome = """👀 睿课云眸学习监督系统已启动

🎯 功能介绍：
• 🎭 自动检测学习姿态
• 👁️ 识别注意力分散
• 🔊 及时语音提醒
• 📊 学习行为分析
• 🎯 专注力监控

💡 左侧区域可以和AI学习助手对话，询问学习问题
🚀 右侧区域将持续监督您的学习状态..."""
                        
                        # 使用专门的监督消息方法
                        self.add_supervision_message(supervision_welcome, is_system=True)
                        print("✅ 监督欢迎消息已添加")
                except Exception as e:
                    print(f"❌ 添加监督欢迎消息时出错: {e}")
            
            # 延迟2秒后添加监督欢迎消息
            self.after(2000, add_supervision_welcome)
                
        except Exception as e:
            print(f"❌ 初始化欢迎消息时出错: {e}")
    
    def add_initial_messages(self):
        """添加初始欢迎消息 - 保持兼容性"""
        self.add_initial_messages_safe()

    # ================== 窗口自动最大化 ==================
    def _maximize_window(self):
        """跨平台最大化窗口，避免初始固定尺寸"""
        try:
            # Windows / Linux 大多支持 state("zoomed")
            self.state("zoomed")
        except Exception:
            try:
                # macOS 使用 attributes
                self.attributes("-zoomed", True)
            except Exception:
                # 回退到手动全屏尺寸
                try:
                    width = self.winfo_screenwidth()
                    height = self.winfo_screenheight()
                    self.geometry(f"{width}x{height}+0+0")
                except Exception as e:
                    print("窗口最大化失败:", e)

    # ================== 额外功能窗口 ==================
    def open_learning_plan(self):
        """打开学习计划与目标管理窗口"""
        try:
            plan_win = ctk.CTkToplevel(self)
            plan_win.title("📋 学习计划与目标管理")
            plan_win.geometry("1000x700")
            # 确保窗口背景为纯白，消除任何灰色背景
            plan_win.configure(fg_color="#ffffff")
            plan_win._set_appearance_mode("light")  # 强制浅色模式
            plan_win.resizable(True, True)

            # 优化的和谐配色方案 - 以柔和蓝色为主题
            colors = {
                'primary': "#6366f1",        # 柔和靛蓝 - 主要操作按钮
                'primary_light': "#a5b4fc",  # 浅靛蓝 - 次要元素
                'success': "#059669",        # 深绿色 - 成功状态
                'success_light': "#10b981",  # 浅绿色 - 低优先级
                'warning': "#d97706",        # 深橙色 - 警告状态
                'warning_light': "#f59e0b",  # 浅橙色 - 中优先级
                'danger': "#dc2626",         # 深红色 - 危险状态
                'danger_light': "#ef4444",   # 浅红色 - 高优先级
                'white': "#ffffff",
                'gray_50': "#f9fafb",
                'gray_100': "#f3f4f6",
                'gray_200': "#e5e7eb",
                'gray_300': "#d1d5db",       # 中性灰 - 计划中状态
                'text_primary': "#111827",
                'text_secondary': "#6b7280",
                # 和谐的卡片背景色系 - 基于柔和蓝色主题
                'card_blue': "#dbeafe",      # 主要卡片 - 保持原有柔和蓝色
                'card_blue_light': "#eff6ff", # 更浅的蓝色 - 模板选择
                'card_green': "#d1fae5",     # 保持原有绿色
                'card_neutral': "#f1f5f9",   # 中性浅灰蓝 - 替代橙色
                'card_pink': "#fce7f3",      # 保持原有粉色
                # 专用于优先级的柔和色彩
                'priority_high': "#fecaca",   # 柔和红色背景
                'priority_medium': "#fed7aa", # 柔和橙色背景
                'priority_low': "#bbf7d0",    # 柔和绿色背景
                # 专用于状态的清晰色彩
                'status_completed': "#059669", # 深绿 - 已完成
                'status_progress': "#2563eb",  # 蓝色 - 进行中
                'status_planned': "#6b7280",   # 灰色 - 计划中
                'status_cancelled': "#dc2626"  # 深红 - 已取消
            }

            # 创建主容器确保完全消除灰色背景
            main_container = ctk.CTkFrame(plan_win, corner_radius=0, fg_color="#ffffff", border_width=0)
            main_container.pack(fill="both", expand=True)

            # 标题区域
            title_frame = ctk.CTkFrame(main_container, corner_radius=16, fg_color="transparent", height=80)
            title_frame.pack(fill="x", padx=20, pady=(20, 10))
            title_frame.pack_propagate(False)
            
            title_container = ctk.CTkFrame(title_frame, fg_color="transparent")
            title_container.pack(expand=True, fill="both")
            
            title_label = ctk.CTkLabel(
                title_container,
                text="📋 学习计划与目标管理",
                font=ctk.CTkFont(size=22, weight="bold"),
                text_color=colors['text_primary']
            )
            title_label.pack(side="left", padx=25, pady=20)

            # 创建标签页 - 强制白色背景消除所有灰色
            notebook = ctk.CTkTabview(
                main_container,
                fg_color="#ffffff",  # 主背景白色
                segmented_button_fg_color="#ffffff",  # 按钮区域白色
                segmented_button_selected_color=colors['primary'],  # 选中按钮颜色
                segmented_button_selected_hover_color=colors['primary']  # 选中按钮悬停颜色
            )
            notebook.pack(fill="both", expand=True, padx=20, pady=(0, 20))

            # 添加标签页
            notebook.add("🎯 目标设定")
            notebook.add("📅 学习计划表")
            notebook.add("🏆 进度统计")

            # 强制设置每个标签页背景为白色，消除灰色
            try:
                for tab_name in ["🎯 目标设定", "📅 学习计划表", "🏆 进度统计"]:
                    tab_frame = notebook.tab(tab_name)
                    tab_frame.configure(fg_color="#ffffff")
            except Exception as tab_err:
                print("设置标签页背景失败:", tab_err)

            # 设置各个标签页
            self.setup_goal_setting_tab(notebook.tab("🎯 目标设定"), colors)
            self.setup_plan_schedule_tab(notebook.tab("📅 学习计划表"), colors)
            self.setup_progress_stats_tab(notebook.tab("🏆 进度统计"), colors)

        except Exception as e:
            print("打开学习计划窗口失败:", e)

    def setup_goal_setting_tab(self, tab_frame, colors):
        """设置目标设定标签页"""
        try:
            tab_frame.configure(fg_color="#ffffff")

            # 滚动框架 - 使用白色背景而非透明
            scroll_frame = ctk.CTkScrollableFrame(tab_frame, fg_color="#ffffff")
            scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            # 每日目标设定卡片
            daily_card = ctk.CTkFrame(
                scroll_frame,
                corner_radius=12,
                fg_color=colors['card_blue'],
                border_width=0
            )
            daily_card.pack(fill="x", pady=(0, 20))
            
            # 每日目标标题
            daily_title = ctk.CTkLabel(
                daily_card,
                text="📊 每日学习目标",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=colors['text_primary']
            )
            daily_title.pack(pady=(20, 15))
            
            # 每日目标设置框架
            daily_content = ctk.CTkFrame(daily_card, fg_color="transparent")
            daily_content.pack(fill="x", padx=30, pady=(0, 25))
            
            # 学习时长目标
            time_frame = ctk.CTkFrame(daily_content, fg_color="transparent")
            time_frame.pack(fill="x", pady=10)
            
            ctk.CTkLabel(
                time_frame,
                text="⏱️ 每日学习时长目标:",
                font=ctk.CTkFont(size=14, weight="bold"),  # 增大字体
                text_color=colors['text_primary']
            ).pack(side="left")
            
            time_entry = ctk.CTkEntry(
                time_frame,
                placeholder_text="如：120（分钟）",
                width=180,  # 增大宽度
                height=40,  # 增大高度
                font=ctk.CTkFont(size=14),  # 增大字体
                fg_color=("#dbeafe", "#1e293b")  # 使用柔和蓝色背景
            )
            time_entry.pack(side="right")

            # 专注度目标
            focus_frame = ctk.CTkFrame(daily_content, fg_color="transparent")
            focus_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                focus_frame,
                text="📈 专注度目标:",
                font=ctk.CTkFont(size=14, weight="bold"),  # 增大字体
                text_color=colors['text_primary']
            ).pack(side="left")

            focus_entry = ctk.CTkEntry(
                focus_frame,
                placeholder_text="如：85（分）",
                width=180,  # 增大宽度
                height=40,  # 增大高度
                font=ctk.CTkFont(size=14),  # 增大字体
                fg_color=("#dbeafe", "#1e293b")  # 使用柔和蓝色背景
            )
            focus_entry.pack(side="right")

            # 加载现有目标数据到输入框
            self.load_existing_goals_to_entries(time_entry, focus_entry)
            
            # 每周目标设定卡片
            weekly_card = ctk.CTkFrame(
                scroll_frame,
                corner_radius=12,
                fg_color=colors['card_green'],
                border_width=0
            )
            weekly_card.pack(fill="x", pady=(0, 20))
            
            # 每周目标标题
            weekly_title = ctk.CTkLabel(
                weekly_card,
                text="📅 每周学习目标",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=colors['text_primary']
            )
            weekly_title.pack(pady=(20, 15))
            
            # 每周目标设置框架
            weekly_content = ctk.CTkFrame(weekly_card, fg_color="transparent")
            weekly_content.pack(fill="x", padx=30, pady=(0, 25))
            
            # 学习天数目标
            days_frame = ctk.CTkFrame(weekly_content, fg_color="transparent")
            days_frame.pack(fill="x", pady=10)
            
            ctk.CTkLabel(
                days_frame, 
                text="📚 每周学习天数:", 
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=colors['text_primary']
            ).pack(side="left")
            
            days_entry = ctk.CTkEntry(
                days_frame,
                placeholder_text="如：5（天）",
                width=180,  # 增大宽度
                height=40,  # 增大高度
                font=ctk.CTkFont(size=14),  # 增大字体
                fg_color=("#dbeafe", "#1e293b")  # 使用柔和蓝色背景
            )
            days_entry.pack(side="right")
            
            # 总学习时长目标
            total_time_frame = ctk.CTkFrame(weekly_content, fg_color="transparent")
            total_time_frame.pack(fill="x", pady=10)
            
            ctk.CTkLabel(
                total_time_frame, 
                text="⏰ 每周总学习时长:", 
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=colors['text_primary']
            ).pack(side="left")
            
            total_time_entry = ctk.CTkEntry(
                total_time_frame,
                placeholder_text="如：600（分钟）",
                width=180,  # 增大宽度
                height=40,  # 增大高度
                font=ctk.CTkFont(size=14),  # 增大字体
                fg_color=("#dbeafe", "#1e293b")  # 使用柔和蓝色背景
            )
            total_time_entry.pack(side="right")

            # 加载现有目标数据到每周目标输入框
            self.load_existing_goals_to_weekly_entries(days_entry, total_time_entry)

            # 保存目标按钮
            save_button = ctk.CTkButton(
                scroll_frame,
                text="💾 保存目标设定",
                width=200,
                height=40,
                font=ctk.CTkFont(size=14, weight="bold"),
                command=lambda: self.save_learning_goals(time_entry, focus_entry, days_entry, total_time_entry),
                fg_color=colors['success'],
                hover_color=colors['success_light'],  # 使用和谐的悬停色
                corner_radius=12
            )
            save_button.pack(pady=20)
            
        except Exception as e:
            print(f"设置目标设定页面失败: {e}")

    def setup_plan_schedule_tab(self, tab_frame, colors):
        """设置交互式学习计划表标签页"""
        try:
            tab_frame.configure(fg_color="#ffffff")

            # 存储颜色配置供后续使用
            self.plan_colors = colors

            # 滚动框架 - 使用白色背景而非透明
            scroll_frame = ctk.CTkScrollableFrame(tab_frame, fg_color="#ffffff")
            scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # 工具栏区域
            toolbar_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent", height=60)
            toolbar_frame.pack(fill="x", pady=(0, 20))
            toolbar_frame.pack_propagate(False)

            # 工具栏标题
            toolbar_title = ctk.CTkLabel(
                toolbar_frame,
                text="📅 交互式学习计划管理",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=colors['text_primary']
            )
            toolbar_title.pack(side="left", padx=(10, 20), pady=15)

            # 工具栏按钮
            button_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
            button_frame.pack(side="right", padx=10, pady=10)

            # 添加新计划按钮 - 使用和谐的成功色
            add_button = ctk.CTkButton(
                button_frame,
                text="➕ 添加计划",
                width=100,
                height=35,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=self.add_new_plan,
                fg_color=colors['success'],
                hover_color=colors['success_light'],
                corner_radius=8
            )
            add_button.pack(side="left", padx=(0, 10))

            # 模板按钮 - 使用柔和的主色调
            template_button = ctk.CTkButton(
                button_frame,
                text="📋 模板",
                width=80,
                height=35,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=self.show_templates,
                fg_color=colors['primary'],
                hover_color=colors['primary_light'],
                corner_radius=8
            )
            template_button.pack(side="left", padx=(0, 10))

            # 保存按钮 - 使用深橙色保持区分度
            save_button = ctk.CTkButton(
                button_frame,
                text="💾 保存",
                width=80,
                height=35,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=self.save_learning_plans,
                fg_color=colors['warning'],
                hover_color=colors['warning_light'],
                corner_radius=8
            )
            save_button.pack(side="left")

            # 学习计划展示卡片 - 使用柔和蓝色主题
            plan_card = ctk.CTkFrame(
                scroll_frame,
                corner_radius=12,
                fg_color=colors['card_blue'],  # 保持柔和蓝色主题
                border_width=0
            )
            plan_card.pack(fill="x", pady=(0, 20))

            # 计划表标题
            plan_title = ctk.CTkLabel(
                plan_card,
                text="📅 本周学习计划表",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=colors['text_primary']
            )
            plan_title.pack(pady=(20, 15))

            # 计划表内容容器
            self.plan_content_frame = ctk.CTkFrame(plan_card, fg_color="transparent")
            self.plan_content_frame.pack(fill="x", padx=30, pady=(0, 25))

            # 加载并显示学习计划
            self.refresh_plan_display()

        except Exception as e:
            print(f"设置学习计划表页面失败: {e}")

    def refresh_plan_display(self):
        """刷新学习计划显示"""
        try:
            # 清空现有内容
            for widget in self.plan_content_frame.winfo_children():
                widget.destroy()

            if not self.learning_plan_manager:
                # 如果管理器未初始化，显示错误信息
                error_label = ctk.CTkLabel(
                    self.plan_content_frame,
                    text="⚠️ 学习计划管理器未初始化",
                    font=ctk.CTkFont(size=12),
                    text_color=self.plan_colors['danger']
                )
                error_label.pack(pady=20)
                return

            # 获取学习计划数据
            plans = self.learning_plan_manager.current_plans.get("weekly_schedule", [])

            if not plans:
                # 如果没有计划，显示提示
                empty_label = ctk.CTkLabel(
                    self.plan_content_frame,
                    text="📝 暂无学习计划，点击'添加计划'开始创建",
                    font=ctk.CTkFont(size=12),
                    text_color=self.plan_colors['text_secondary']
                )
                empty_label.pack(pady=20)
                return

            # 显示计划列表
            for plan in plans:
                self.create_plan_item(plan)

        except Exception as e:
            print(f"刷新计划显示失败: {e}")

    def create_plan_item(self, plan):
        """创建单个计划项目的显示"""
        try:
            # 主容器
            item_frame = ctk.CTkFrame(
                self.plan_content_frame,
                fg_color="transparent",
                height=50
            )
            item_frame.pack(fill="x", pady=3)
            item_frame.pack_propagate(False)

            # 日期标签
            day_label = ctk.CTkLabel(
                item_frame,
                text=plan.get("day", ""),
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary'],
                width=60
            )
            day_label.pack(side="left", padx=(0, 15))

            # 时间标签
            time_text = f"{plan.get('start_time', '')}-{plan.get('end_time', '')}"
            time_label = ctk.CTkLabel(
                item_frame,
                text=time_text,
                font=ctk.CTkFont(size=11),
                text_color=self.plan_colors['text_secondary'],
                width=90
            )
            time_label.pack(side="left", padx=(0, 15))

            # 学习内容
            content_text = f"{plan.get('subject', '')} - {plan.get('topic', '')}"
            content_label = ctk.CTkLabel(
                item_frame,
                text=content_text,
                font=ctk.CTkFont(size=11),
                text_color=self.plan_colors['text_primary'],
                anchor="w"
            )
            content_label.pack(side="left", fill="x", expand=True, padx=(0, 15))

            # 优先级标签 - 使用专用的柔和优先级色彩
            priority = plan.get("priority", "中")
            priority_color = {
                "高": self.plan_colors['danger'],      # 深红色 - 高优先级
                "中": self.plan_colors['warning'],     # 深橙色 - 中优先级
                "低": self.plan_colors['success']      # 深绿色 - 低优先级
            }.get(priority, self.plan_colors['text_secondary'])

            priority_label = ctk.CTkLabel(
                item_frame,
                text=f"优先级: {priority}",
                font=ctk.CTkFont(size=10),
                text_color=priority_color,
                width=70
            )
            priority_label.pack(side="left", padx=(0, 10))

            # 状态标签 - 使用专用的清晰状态色彩
            status = plan.get("status", "计划中")
            status_colors = {
                "已完成": self.plan_colors['status_completed'],  # 深绿色 - 已完成
                "进行中": self.plan_colors['status_progress'],   # 蓝色 - 进行中
                "计划中": self.plan_colors['status_planned'],    # 灰色 - 计划中
                "已取消": self.plan_colors['status_cancelled']   # 深红色 - 已取消
            }
            status_color = status_colors.get(status, self.plan_colors['text_secondary'])

            status_icons = {
                "已完成": "✅",
                "进行中": "🔄",
                "计划中": "📋",
                "已取消": "❌"
            }
            status_icon = status_icons.get(status, "📋")

            status_label = ctk.CTkLabel(
                item_frame,
                text=f"{status_icon} {status}",
                font=ctk.CTkFont(size=10),
                text_color=status_color,
                width=80
            )
            status_label.pack(side="left", padx=(0, 10))

            # 操作按钮
            button_frame = ctk.CTkFrame(item_frame, fg_color="transparent")
            button_frame.pack(side="right")

            # 编辑按钮 - 使用柔和的主色调
            edit_button = ctk.CTkButton(
                button_frame,
                text="✏️",
                width=30,
                height=25,
                font=ctk.CTkFont(size=10),
                command=lambda p=plan: self.edit_plan(p),
                fg_color=self.plan_colors['primary'],
                hover_color=self.plan_colors['primary_light'],
                corner_radius=5
            )
            edit_button.pack(side="left", padx=(0, 5))

            # 删除按钮 - 使用深红色保持警示性
            delete_button = ctk.CTkButton(
                button_frame,
                text="🗑️",
                width=30,
                height=25,
                font=ctk.CTkFont(size=10),
                command=lambda p=plan: self.delete_plan(p),
                fg_color=self.plan_colors['danger'],
                hover_color=self.plan_colors['danger_light'],
                corner_radius=5
            )
            delete_button.pack(side="left")

        except Exception as e:
            print(f"创建计划项目失败: {e}")

    def add_new_plan(self):
        """添加新的学习计划"""
        try:
            self.show_plan_editor()
        except Exception as e:
            print(f"添加新计划失败: {e}")

    def edit_plan(self, plan):
        """编辑现有学习计划"""
        try:
            self.show_plan_editor(plan)
        except Exception as e:
            print(f"编辑计划失败: {e}")

    def delete_plan(self, plan):
        """删除学习计划"""
        try:
            # 创建确认对话框
            dialog = ctk.CTkToplevel(self)
            dialog.title("确认删除")
            dialog.geometry("400x200")
            dialog.configure(fg_color="#ffffff")
            dialog.transient(self)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (dialog.winfo_screenheight() // 2) - (200 // 2)
            dialog.geometry(f"400x200+{x}+{y}")

            # 确认消息
            message_label = ctk.CTkLabel(
                dialog,
                text=f"确定要删除以下学习计划吗？\n\n{plan.get('day', '')} {plan.get('start_time', '')}-{plan.get('end_time', '')}\n{plan.get('subject', '')} - {plan.get('topic', '')}",
                font=ctk.CTkFont(size=12),
                text_color=self.plan_colors['text_primary'],
                justify="center"
            )
            message_label.pack(pady=30)

            # 按钮框架
            button_frame = ctk.CTkFrame(dialog, fg_color="transparent")
            button_frame.pack(pady=20)

            # 确认删除按钮 - 使用深红色保持警示性
            confirm_button = ctk.CTkButton(
                button_frame,
                text="确认删除",
                width=100,
                height=35,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=lambda: self.confirm_delete_plan(plan, dialog),
                fg_color=self.plan_colors['danger'],
                hover_color=self.plan_colors['danger_light'],
                corner_radius=8
            )
            confirm_button.pack(side="left", padx=(0, 20))

            # 取消按钮 - 使用中性灰色
            cancel_button = ctk.CTkButton(
                button_frame,
                text="取消",
                width=100,
                height=35,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=dialog.destroy,
                fg_color=self.plan_colors['gray_300'],
                hover_color=self.plan_colors['text_secondary'],
                corner_radius=8
            )
            cancel_button.pack(side="left")

        except Exception as e:
            print(f"删除计划对话框失败: {e}")

    def confirm_delete_plan(self, plan, dialog):
        """确认删除计划"""
        try:
            if self.learning_plan_manager:
                # 从计划列表中移除
                plans = self.learning_plan_manager.current_plans.get("weekly_schedule", [])
                plans = [p for p in plans if p.get("id") != plan.get("id")]
                self.learning_plan_manager.current_plans["weekly_schedule"] = plans

                # 刷新显示
                self.refresh_plan_display()

                print(f"✅ 已删除计划: {plan.get('day', '')} {plan.get('subject', '')}")

            dialog.destroy()

        except Exception as e:
            print(f"确认删除计划失败: {e}")
            dialog.destroy()

    def save_learning_plans(self):
        """保存学习计划"""
        try:
            if self.learning_plan_manager:
                success = self.learning_plan_manager.save_plans()
                if success:
                    # 显示保存成功消息
                    self.show_success_message("💾 学习计划保存成功！")
                else:
                    self.show_error_message("❌ 保存失败，请重试")
            else:
                self.show_error_message("❌ 学习计划管理器未初始化")

        except Exception as e:
            print(f"保存学习计划失败: {e}")
            self.show_error_message(f"❌ 保存失败: {e}")

    def show_plan_editor(self, plan=None):
        """显示学习计划编辑器"""
        try:
            # 创建编辑器窗口
            editor = ctk.CTkToplevel(self)
            editor.title("📝 学习计划编辑器" if plan else "➕ 添加新计划")
            editor.geometry("600x700")
            editor.configure(fg_color="#ffffff")
            editor.transient(self)
            editor.grab_set()

            # 居中显示
            editor.update_idletasks()
            x = (editor.winfo_screenwidth() // 2) - (600 // 2)
            y = (editor.winfo_screenheight() // 2) - (700 // 2)
            editor.geometry(f"600x700+{x}+{y}")

            # 滚动框架
            scroll_frame = ctk.CTkScrollableFrame(editor, fg_color="#ffffff")
            scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # 标题
            title_label = ctk.CTkLabel(
                scroll_frame,
                text="📝 学习计划编辑器" if plan else "➕ 添加新学习计划",
                font=ctk.CTkFont(size=18, weight="bold"),
                text_color=self.plan_colors['text_primary']
            )
            title_label.pack(pady=(0, 20))

            # 表单字段
            fields = {}

            # 日期选择
            day_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            day_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                day_frame,
                text="📅 日期:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(side="left", padx=(0, 10))

            fields['day'] = ctk.CTkOptionMenu(
                day_frame,
                values=["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                width=150,
                font=ctk.CTkFont(size=11)
            )
            fields['day'].pack(side="left")

            # 时间设置
            time_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            time_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                time_frame,
                text="⏰ 时间:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(side="left", padx=(0, 10))

            fields['start_time'] = ctk.CTkEntry(
                time_frame,
                placeholder_text="开始时间 (如: 08:00)",
                width=120,
                font=ctk.CTkFont(size=11)
            )
            fields['start_time'].pack(side="left", padx=(0, 10))

            ctk.CTkLabel(
                time_frame,
                text="至",
                font=ctk.CTkFont(size=11),
                text_color=self.plan_colors['text_secondary']
            ).pack(side="left", padx=(0, 10))

            fields['end_time'] = ctk.CTkEntry(
                time_frame,
                placeholder_text="结束时间 (如: 10:00)",
                width=120,
                font=ctk.CTkFont(size=11)
            )
            fields['end_time'].pack(side="left")

            # 学科设置
            subject_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            subject_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                subject_frame,
                text="📚 学科:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(side="left", padx=(0, 10))

            fields['subject'] = ctk.CTkEntry(
                subject_frame,
                placeholder_text="如: 数学、语文、英语",
                width=200,
                font=ctk.CTkFont(size=11)
            )
            fields['subject'].pack(side="left")

            # 主题设置
            topic_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            topic_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                topic_frame,
                text="📖 主题:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(side="left", padx=(0, 10))

            fields['topic'] = ctk.CTkEntry(
                topic_frame,
                placeholder_text="如: 代数基础、阅读理解",
                width=250,
                font=ctk.CTkFont(size=11)
            )
            fields['topic'].pack(side="left")

            # 学习目标
            goals_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            goals_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                goals_frame,
                text="🎯 目标:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(anchor="nw", padx=(0, 10))

            fields['goals'] = ctk.CTkTextbox(
                goals_frame,
                height=80,
                width=400,
                font=ctk.CTkFont(size=11)
            )
            fields['goals'].pack(fill="x", expand=True)

            # 优先级和难度
            priority_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            priority_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                priority_frame,
                text="⭐ 优先级:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(side="left", padx=(0, 10))

            fields['priority'] = ctk.CTkOptionMenu(
                priority_frame,
                values=["高", "中", "低"],
                width=100,
                font=ctk.CTkFont(size=11)
            )
            fields['priority'].pack(side="left", padx=(0, 20))

            ctk.CTkLabel(
                priority_frame,
                text="📊 难度:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(side="left", padx=(0, 10))

            fields['difficulty'] = ctk.CTkOptionMenu(
                priority_frame,
                values=["简单", "中等", "困难"],
                width=100,
                font=ctk.CTkFont(size=11)
            )
            fields['difficulty'].pack(side="left")

            # 状态设置
            status_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            status_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                status_frame,
                text="📋 状态:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(side="left", padx=(0, 10))

            fields['status'] = ctk.CTkOptionMenu(
                status_frame,
                values=["计划中", "进行中", "已完成", "已取消"],
                width=120,
                font=ctk.CTkFont(size=11)
            )
            fields['status'].pack(side="left")

            # 备注
            notes_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            notes_frame.pack(fill="x", pady=10)

            ctk.CTkLabel(
                notes_frame,
                text="📝 备注:",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=self.plan_colors['text_primary']
            ).pack(anchor="nw", padx=(0, 10))

            fields['notes'] = ctk.CTkTextbox(
                notes_frame,
                height=60,
                width=400,
                font=ctk.CTkFont(size=11)
            )
            fields['notes'].pack(fill="x", expand=True)

            # 如果是编辑模式，填充现有数据
            if plan:
                self.populate_editor_fields(fields, plan)

            # 按钮区域
            button_frame = ctk.CTkFrame(scroll_frame, fg_color="transparent")
            button_frame.pack(fill="x", pady=20)

            # 保存按钮 - 使用和谐的成功色
            save_button = ctk.CTkButton(
                button_frame,
                text="💾 保存计划",
                width=120,
                height=40,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=lambda: self.save_plan_from_editor(fields, plan, editor),
                fg_color=self.plan_colors['success'],
                hover_color=self.plan_colors['success_light'],
                corner_radius=8
            )
            save_button.pack(side="left", padx=(0, 20))

            # 取消按钮 - 使用中性灰色
            cancel_button = ctk.CTkButton(
                button_frame,
                text="❌ 取消",
                width=100,
                height=40,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=editor.destroy,
                fg_color=self.plan_colors['gray_300'],
                hover_color=self.plan_colors['text_secondary'],
                corner_radius=8
            )
            cancel_button.pack(side="left")

        except Exception as e:
            print(f"显示计划编辑器失败: {e}")

    def populate_editor_fields(self, fields, plan):
        """填充编辑器字段"""
        try:
            fields['day'].set(plan.get('day', '周一'))
            fields['start_time'].insert(0, plan.get('start_time', ''))
            fields['end_time'].insert(0, plan.get('end_time', ''))
            fields['subject'].insert(0, plan.get('subject', ''))
            fields['topic'].insert(0, plan.get('topic', ''))
            fields['goals'].insert("1.0", plan.get('goals', ''))
            fields['priority'].set(plan.get('priority', '中'))
            fields['difficulty'].set(plan.get('difficulty', '中等'))
            fields['status'].set(plan.get('status', '计划中'))
            fields['notes'].insert("1.0", plan.get('notes', ''))
        except Exception as e:
            print(f"填充编辑器字段失败: {e}")

    def save_plan_from_editor(self, fields, original_plan, editor):
        """从编辑器保存计划"""
        try:
            # 验证输入
            validation_result = self.validate_plan_input(fields)
            if not validation_result['valid']:
                self.show_error_message(validation_result['message'])
                return

            # 创建计划数据
            plan_data = {
                'id': original_plan.get('id') if original_plan else f"plan_{int(time.time())}",
                'day': fields['day'].get(),
                'day_index': ["周一", "周二", "周三", "周四", "周五", "周六", "周日"].index(fields['day'].get()),
                'start_time': fields['start_time'].get(),
                'end_time': fields['end_time'].get(),
                'duration': self.calculate_duration(fields['start_time'].get(), fields['end_time'].get()),
                'subject': fields['subject'].get(),
                'topic': fields['topic'].get(),
                'goals': fields['goals'].get("1.0", "end-1c"),
                'priority': fields['priority'].get(),
                'difficulty': fields['difficulty'].get(),
                'status': fields['status'].get(),
                'notes': fields['notes'].get("1.0", "end-1c"),
                'updated_at': datetime.now().isoformat()
            }

            if not original_plan:
                plan_data['created_at'] = datetime.now().isoformat()
            else:
                plan_data['created_at'] = original_plan.get('created_at', datetime.now().isoformat())

            # 冲突检测
            if self.learning_plan_manager.current_plans.get("settings", {}).get("conflict_detection", True):
                conflict = self.check_schedule_conflict(plan_data, original_plan)
                if conflict:
                    self.show_error_message(f"⚠️ 时间冲突: {conflict}")
                    return

            # 保存到数据
            if self.learning_plan_manager:
                plans = self.learning_plan_manager.current_plans.get("weekly_schedule", [])

                if original_plan:
                    # 更新现有计划
                    for i, p in enumerate(plans):
                        if p.get('id') == original_plan.get('id'):
                            plans[i] = plan_data
                            break
                else:
                    # 添加新计划
                    plans.append(plan_data)

                self.learning_plan_manager.current_plans["weekly_schedule"] = plans

                # 刷新显示
                self.refresh_plan_display()

                # 显示成功消息
                action = "更新" if original_plan else "添加"
                self.show_success_message(f"✅ 学习计划{action}成功！")

                editor.destroy()

        except Exception as e:
            print(f"保存计划失败: {e}")
            self.show_error_message(f"❌ 保存失败: {e}")

    def validate_plan_input(self, fields):
        """验证计划输入"""
        try:
            # 检查必填字段
            if not fields['start_time'].get().strip():
                return {'valid': False, 'message': '请输入开始时间'}

            if not fields['end_time'].get().strip():
                return {'valid': False, 'message': '请输入结束时间'}

            if not fields['subject'].get().strip():
                return {'valid': False, 'message': '请输入学科名称'}

            # 验证时间格式
            start_time = fields['start_time'].get().strip()
            end_time = fields['end_time'].get().strip()

            if not self.validate_time_format(start_time):
                return {'valid': False, 'message': '开始时间格式错误，请使用 HH:MM 格式'}

            if not self.validate_time_format(end_time):
                return {'valid': False, 'message': '结束时间格式错误，请使用 HH:MM 格式'}

            # 验证时间逻辑
            if start_time >= end_time:
                return {'valid': False, 'message': '结束时间必须晚于开始时间'}

            return {'valid': True, 'message': '验证通过'}

        except Exception as e:
            return {'valid': False, 'message': f'验证失败: {e}'}

    def validate_time_format(self, time_str):
        """验证时间格式"""
        try:
            import re
            pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
            return bool(re.match(pattern, time_str))
        except:
            return False

    def calculate_duration(self, start_time, end_time):
        """计算学习时长（分钟）"""
        try:
            start_hour, start_min = map(int, start_time.split(':'))
            end_hour, end_min = map(int, end_time.split(':'))

            start_total_min = start_hour * 60 + start_min
            end_total_min = end_hour * 60 + end_min

            return end_total_min - start_total_min
        except:
            return 0

    def check_schedule_conflict(self, new_plan, original_plan=None):
        """检查时间冲突"""
        try:
            if not self.learning_plan_manager:
                return None

            plans = self.learning_plan_manager.current_plans.get("weekly_schedule", [])
            new_day = new_plan.get('day')
            new_start = new_plan.get('start_time')
            new_end = new_plan.get('end_time')

            for plan in plans:
                # 跳过自己（编辑模式）
                if original_plan and plan.get('id') == original_plan.get('id'):
                    continue

                # 只检查同一天的计划
                if plan.get('day') != new_day:
                    continue

                plan_start = plan.get('start_time')
                plan_end = plan.get('end_time')

                # 检查时间重叠
                if (new_start < plan_end and new_end > plan_start):
                    return f"与 {plan.get('subject', '')} ({plan_start}-{plan_end}) 时间冲突"

            return None

        except Exception as e:
            print(f"冲突检测失败: {e}")
            return None

    def show_templates(self):
        """显示学习计划模板"""
        try:
            # 创建模板选择窗口
            template_win = ctk.CTkToplevel(self)
            template_win.title("📋 学习计划模板")
            template_win.geometry("500x400")
            template_win.configure(fg_color="#ffffff")
            template_win.transient(self)
            template_win.grab_set()

            # 居中显示
            template_win.update_idletasks()
            x = (template_win.winfo_screenwidth() // 2) - (500 // 2)
            y = (template_win.winfo_screenheight() // 2) - (400 // 2)
            template_win.geometry(f"500x400+{x}+{y}")

            # 标题
            title_label = ctk.CTkLabel(
                template_win,
                text="📋 选择学习计划模板",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=self.plan_colors['text_primary']
            )
            title_label.pack(pady=20)

            # 模板列表
            if self.learning_plan_manager:
                templates = self.learning_plan_manager.current_plans.get("templates", {})

                for template_key, template_data in templates.items():
                    template_frame = ctk.CTkFrame(
                        template_win,
                        corner_radius=8,
                        fg_color=self.plan_colors['card_blue_light'],  # 使用更浅的蓝色
                        border_width=1,
                        border_color=self.plan_colors['gray_200']
                    )
                    template_frame.pack(fill="x", padx=20, pady=10)

                    # 模板名称
                    name_label = ctk.CTkLabel(
                        template_frame,
                        text=template_data.get("name", ""),
                        font=ctk.CTkFont(size=14, weight="bold"),
                        text_color=self.plan_colors['text_primary']
                    )
                    name_label.pack(pady=(15, 5))

                    # 模板描述
                    sessions = template_data.get("sessions", [])
                    desc_text = f"包含 {len(sessions)} 个学习时段"
                    desc_label = ctk.CTkLabel(
                        template_frame,
                        text=desc_text,
                        font=ctk.CTkFont(size=11),
                        text_color=self.plan_colors['text_secondary']
                    )
                    desc_label.pack(pady=(0, 10))

                    # 应用按钮 - 使用柔和的主色调
                    apply_button = ctk.CTkButton(
                        template_frame,
                        text="应用此模板",
                        width=120,
                        height=30,
                        font=ctk.CTkFont(size=11, weight="bold"),
                        command=lambda tk=template_key: self.apply_template(tk, template_win),
                        fg_color=self.plan_colors['primary'],
                        hover_color=self.plan_colors['primary_light'],
                        corner_radius=6
                    )
                    apply_button.pack(pady=(0, 15))

            # 关闭按钮 - 使用中性灰色
            close_button = ctk.CTkButton(
                template_win,
                text="关闭",
                width=100,
                height=35,
                font=ctk.CTkFont(size=12, weight="bold"),
                command=template_win.destroy,
                fg_color=self.plan_colors['gray_300'],
                hover_color=self.plan_colors['text_secondary'],
                corner_radius=8
            )
            close_button.pack(pady=20)

        except Exception as e:
            print(f"显示模板失败: {e}")

    def apply_template(self, template_key, template_win):
        """应用学习计划模板"""
        try:
            if not self.learning_plan_manager:
                return

            templates = self.learning_plan_manager.current_plans.get("templates", {})
            template = templates.get(template_key)

            if not template:
                self.show_error_message("❌ 模板不存在")
                return

            # 清空现有计划
            self.learning_plan_manager.current_plans["weekly_schedule"] = []

            # 应用模板
            sessions = template.get("sessions", [])
            days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

            for i, day in enumerate(days):
                if i < len(sessions):
                    session = sessions[i % len(sessions)]
                    time_range = session.get("time", "08:00-10:00")
                    start_time, end_time = time_range.split("-")

                    plan_data = {
                        'id': f"template_plan_{i}",
                        'day': day,
                        'day_index': i,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': self.calculate_duration(start_time, end_time),
                        'subject': session.get("subject", ""),
                        'topic': session.get("type", ""),
                        'goals': f"完成{session.get('subject', '')}的{session.get('type', '')}学习",
                        'priority': "中",
                        'difficulty': "中等",
                        'status': "计划中",
                        'notes': f"基于{template.get('name', '')}模板创建",
                        'created_at': datetime.now().isoformat(),
                        'updated_at': datetime.now().isoformat()
                    }

                    self.learning_plan_manager.current_plans["weekly_schedule"].append(plan_data)

            # 刷新显示
            self.refresh_plan_display()

            # 显示成功消息
            self.show_success_message(f"✅ 已应用模板: {template.get('name', '')}")

            template_win.destroy()

        except Exception as e:
            print(f"应用模板失败: {e}")
            self.show_error_message(f"❌ 应用模板失败: {e}")

    def show_success_message(self, message):
        """显示成功消息"""
        try:
            if hasattr(self, 'add_supervision_message'):
                self.add_supervision_message(message, is_system=True)
            else:
                print(message)
        except Exception as e:
            print(f"显示成功消息失败: {e}")

    def show_error_message(self, message):
        """显示错误消息"""
        try:
            if hasattr(self, 'add_supervision_message'):
                self.add_supervision_message(message, is_system=True)
            else:
                print(message)
        except Exception as e:
            print(f"显示错误消息失败: {e}")

    def setup_progress_stats_tab(self, tab_frame, colors):
        """设置进度统计标签页"""
        try:
            tab_frame.configure(fg_color="#ffffff")

            # 滚动框架 - 使用白色背景而非透明
            scroll_frame = ctk.CTkScrollableFrame(tab_frame, fg_color="#ffffff")
            scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            # 进度概览卡片
            progress_card = ctk.CTkFrame(
                scroll_frame,
                corner_radius=12,
                fg_color=colors['card_pink'],
                border_width=0
            )
            progress_card.pack(fill="x", pady=(0, 20))
            
            # 进度标题
            progress_title = ctk.CTkLabel(
                progress_card,
                text="📊 学习进度统计",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=colors['text_primary']
            )
            progress_title.pack(pady=(20, 15))
            
            # 进度指标网格
            stats_container = ctk.CTkFrame(progress_card, fg_color="transparent")
            stats_container.pack(fill="x", padx=30, pady=(0, 25))
            
            # 配置网格
            stats_container.grid_columnconfigure(0, weight=1)
            stats_container.grid_columnconfigure(1, weight=1)
            
            # 今日完成度
            today_frame = ctk.CTkFrame(stats_container, fg_color="transparent", corner_radius=8)
            today_frame.grid(row=0, column=0, sticky="ew", padx=(0, 10), pady=5)
            
            ctk.CTkLabel(
                today_frame,
                text="📅 今日完成度",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=colors['text_primary']
            ).pack(pady=(10, 5))
            
            ctk.CTkLabel(
                today_frame,
                text="75%",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=colors['success']
            ).pack(pady=(0, 10))
            
            # 本周完成度
            week_frame = ctk.CTkFrame(stats_container, fg_color="transparent", corner_radius=8)
            week_frame.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=5)
            
            ctk.CTkLabel(
                week_frame,
                text="📈 本周完成度",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=colors['text_primary']
            ).pack(pady=(10, 5))
            
            ctk.CTkLabel(
                week_frame,
                text="60%",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=colors['warning']
            ).pack(pady=(0, 10))
            
            # 奖励机制卡片
            reward_card = ctk.CTkFrame(
                scroll_frame,
                corner_radius=12,
                fg_color=colors['card_green'],
                border_width=0
            )
            reward_card.pack(fill="x", pady=(0, 20))
            
            # 奖励标题
            reward_title = ctk.CTkLabel(
                reward_card,
                text="🏆 奖励成就",
                font=ctk.CTkFont(size=16, weight="bold"),
                text_color=colors['text_primary']
            )
            reward_title.pack(pady=(20, 15))
            
            # 奖励内容
            reward_content = ctk.CTkFrame(reward_card, fg_color="transparent")
            reward_content.pack(fill="x", padx=30, pady=(0, 25))
            
            rewards = [
                ("🌟", "连续学习3天", "已获得"),
                ("🔥", "单日专注度85+", "已获得"),
                ("💎", "完成周目标", "进行中"),
                ("👑", "月度学霸", "未获得")
            ]
            
            for icon, title, status in rewards:
                reward_item = ctk.CTkFrame(reward_content, fg_color="transparent", height=35)
                reward_item.pack(fill="x", pady=3)
                reward_item.pack_propagate(False)
                
                # 图标
                icon_label = ctk.CTkLabel(
                    reward_item,
                    text=icon,
                    font=ctk.CTkFont(size=16),
                    width=30
                )
                icon_label.pack(side="left")
                
                # 标题
                title_label = ctk.CTkLabel(
                    reward_item,
                    text=title,
                    font=ctk.CTkFont(size=12, weight="bold"),
                    text_color=colors['text_primary']
                )
                title_label.pack(side="left", padx=(10, 0), fill="x", expand=True)
                
                # 状态
                status_color = colors['success'] if status == "已获得" else colors['warning'] if status == "进行中" else colors['text_secondary']
                status_label = ctk.CTkLabel(
                    reward_item,
                    text=status,
                    font=ctk.CTkFont(size=10),
                    text_color=status_color
                )
                status_label.pack(side="right")
            
        except Exception as e:
            print(f"设置进度统计页面失败: {e}")

    def save_learning_goals(self, time_entry, focus_entry, days_entry, total_time_entry):
        """保存学习目标到学习计划管理器"""
        try:
            daily_time = time_entry.get().strip()
            focus_goal = focus_entry.get().strip()
            weekly_days = days_entry.get().strip()
            weekly_time = total_time_entry.get().strip()

            # 验证输入数据
            if not all([daily_time, focus_goal, weekly_days, weekly_time]):
                self.show_error_message("⚠️ 请填写完整的学习目标信息")
                return

            # 保存到学习计划管理器
            if self.learning_plan_manager:
                # 确保goals字段存在
                if "goals" not in self.learning_plan_manager.current_plans:
                    self.learning_plan_manager.current_plans["goals"] = {}

                # 保存目标数据
                self.learning_plan_manager.current_plans["goals"] = {
                    "daily_time": daily_time,
                    "focus_goal": focus_goal,
                    "weekly_days": weekly_days,
                    "weekly_time": weekly_time,
                    "updated_at": datetime.now().isoformat()
                }

                # 保存到文件
                if self.learning_plan_manager.save_plans():
                    print(f"✅ 学习目标已保存：每日{daily_time}分钟，专注度{focus_goal}分，每周{weekly_days}天，总计{weekly_time}分钟")

                    # 更新主界面目标显示
                    self.update_goal_display()

                    # 显示成功消息
                    if hasattr(self, 'add_supervision_message'):
                        self.add_supervision_message("✅ 学习目标已保存成功！", is_system=True)
                else:
                    self.show_error_message("❌ 学习目标保存失败")
            else:
                self.show_error_message("❌ 学习计划管理器未初始化")

        except Exception as e:
            print(f"保存学习目标失败: {e}")
            self.show_error_message(f"❌ 保存失败: {str(e)}")

    def update_goal_display(self):
        """更新主界面标题栏的学习目标显示"""
        try:
            if not hasattr(self, 'goal_display_label') or not self.goal_display_label:
                return

            if not self.learning_plan_manager:
                self.goal_display_label.configure(text="📋 学习计划管理器未初始化")
                return

            # 获取学习目标数据
            goals = self.learning_plan_manager.current_plans.get("goals", {})

            if not goals:
                self.goal_display_label.configure(text="📋 暂无学习目标")
                return

            # 构建目标显示文本
            daily_time = goals.get("daily_time", "")
            focus_goal = goals.get("focus_goal", "")
            weekly_days = goals.get("weekly_days", "")
            weekly_time = goals.get("weekly_time", "")

            # 创建优化的双行目标显示格式
            if daily_time or focus_goal or (weekly_days and weekly_time):
                # 第一行：今日目标
                line1_parts = []
                if daily_time:
                    line1_parts.append(f"今日目标: {daily_time}分钟")
                if focus_goal:
                    line1_parts.append(f"专注度: {focus_goal}分")

                # 第二行：本周目标
                line2_parts = []
                if weekly_days and weekly_time:
                    line2_parts.append(f"本周: {weekly_days}天/{weekly_time}分钟")

                # 组合双行文本
                lines = []
                if line1_parts:
                    lines.append(" | ".join(line1_parts))
                if line2_parts:
                    lines.append(" | ".join(line2_parts))

                if lines:
                    goal_text = f"🎯 {chr(10).join(lines)}"  # 使用换行符分隔
                else:
                    goal_text = "📋 暂无学习目标"
            else:
                goal_text = "📋 暂无学习目标"

            # 更新显示
            self.goal_display_label.configure(text=goal_text)
            # 动态调整显示框宽度
            self.update_goal_frame_width()
            print(f"✅ 学习目标显示已更新: {goal_text}")

        except Exception as e:
            print(f"更新学习目标显示失败: {e}")
            if hasattr(self, 'goal_display_label') and self.goal_display_label:
                self.goal_display_label.configure(text="📋 目标加载失败")

    def get_learning_goals(self):
        """获取当前学习目标数据"""
        try:
            if not self.learning_plan_manager:
                return {}

            return self.learning_plan_manager.current_plans.get("goals", {})

        except Exception as e:
            print(f"获取学习目标失败: {e}")
            return {}

    def load_existing_goals_to_entries(self, time_entry, focus_entry):
        """加载现有学习目标数据到每日目标输入框"""
        try:
            goals = self.get_learning_goals()

            if goals:
                # 加载每日学习时长目标
                daily_time = goals.get("daily_time", "")
                if daily_time:
                    time_entry.delete(0, "end")
                    time_entry.insert(0, daily_time)

                # 加载专注度目标
                focus_goal = goals.get("focus_goal", "")
                if focus_goal:
                    focus_entry.delete(0, "end")
                    focus_entry.insert(0, focus_goal)

                print(f"✅ 已加载现有每日目标: 时长={daily_time}, 专注度={focus_goal}")

        except Exception as e:
            print(f"加载现有每日目标失败: {e}")

    def load_existing_goals_to_weekly_entries(self, days_entry, total_time_entry):
        """加载现有学习目标数据到每周目标输入框"""
        try:
            goals = self.get_learning_goals()

            if goals:
                # 加载每周学习天数
                weekly_days = goals.get("weekly_days", "")
                if weekly_days:
                    days_entry.delete(0, "end")
                    days_entry.insert(0, weekly_days)

                # 加载每周总学习时长
                weekly_time = goals.get("weekly_time", "")
                if weekly_time:
                    total_time_entry.delete(0, "end")
                    total_time_entry.insert(0, weekly_time)

                print(f"✅ 已加载现有每周目标: 天数={weekly_days}, 时长={weekly_time}")

        except Exception as e:
            print(f"加载现有每周目标失败: {e}")

    def generate_study_plan(self):
        """智能生成学习计划"""
        try:
            print("正在智能生成学习计划...")
            
            # 显示生成消息
            if hasattr(self, 'add_supervision_message'):
                self.add_supervision_message("🤖 AI正在为您生成个性化学习计划...", is_system=True)
                
        except Exception as e:
            print(f"生成学习计划失败: {e}")

    def open_qa_history(self):
        """显示现代化问答历史记录窗口"""
        try:
            history_win = ctk.CTkToplevel(self)
            history_win.title("📜 问答历史记录")
            history_win.geometry("800x600")
            history_win.configure(fg_color="#ffffff")

            # 现代化配色方案
            colors = {
                'primary': "#4f46e5",
                'success': "#10b981", 
                'white': "#ffffff",
                'gray_50': "#f9fafb",
                'gray_100': "#f3f4f6",
                'gray_200': "#e5e7eb",
                'text_primary': "#111827",
                'text_secondary': "#6b7280",
                'user_bg': "#eff6ff",
                'ai_bg': "#f0fdf4"
            }

            # 标题区域
            title_frame = ctk.CTkFrame(history_win, corner_radius=16, fg_color=colors['gray_50'], height=80)
            title_frame.pack(fill="x", padx=20, pady=(20, 10))
            title_frame.pack_propagate(False)
            
            title_container = ctk.CTkFrame(title_frame, fg_color="transparent")
            title_container.pack(expand=True, fill="both")
            
            title_label = ctk.CTkLabel(
                title_container,
                text="📜 问答历史记录",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=colors['text_primary']
            )
            title_label.pack(side="left", padx=25, pady=20)
            
            # 消息统计
            if hasattr(self, 'qa_messages') and self.qa_messages:
                count_label = ctk.CTkLabel(
                    title_container,
                    text=f"共 {len(self.qa_messages)} 条对话",
                    font=ctk.CTkFont(size=12),
                    text_color=colors['text_secondary']
                )
                count_label.pack(side="right", padx=25, pady=20)

            # 滚动容器
            container = ctk.CTkScrollableFrame(history_win, fg_color=colors['white'])
            container.pack(fill="both", expand=True, padx=20, pady=(0, 20))

            # 收集最近 QA 消息（包含用户问题和 AI 回复）
            if hasattr(self, 'qa_messages') and self.qa_messages:
                for i, msg in enumerate(self.qa_messages[-50:]):  # 最多展示50条
                    is_ai = msg.get('is_ai', True)
                    content = msg.get('content', '')
                    timestamp = msg.get('timestamp', '')

                    # 创建消息卡片
                    msg_card = ctk.CTkFrame(
                        container,
                        corner_radius=12,
                        fg_color=colors['ai_bg'] if is_ai else colors['user_bg'],
                        border_width=1,
                        border_color=colors['gray_200']
                    )
                    msg_card.pack(fill="x", pady=8, padx=10)
                    
                    # 消息头部
                    header_frame = ctk.CTkFrame(msg_card, fg_color="transparent", height=40)
                    header_frame.pack(fill="x", padx=20, pady=(15, 5))
                    header_frame.pack_propagate(False)
                    
                    # 头像和角色
                    avatar_text = "🤖" if is_ai else "👤"
                    role_text = "AI助手" if is_ai else "您"
                    
                    avatar_label = ctk.CTkLabel(
                        header_frame,
                        text=avatar_text,
                        font=ctk.CTkFont(size=16),
                        width=30,
                        height=30,
                        fg_color=colors['white'],
                        corner_radius=15
                    )
                    avatar_label.pack(side="left")
                    
                    role_label = ctk.CTkLabel(
                        header_frame,
                        text=role_text,
                        font=ctk.CTkFont(size=12, weight="bold"),
                        text_color=colors['text_primary']
                    )
                    role_label.pack(side="left", padx=(10, 0))
                    
                    # 时间戳（如果有）
                    if timestamp:
                        time_label = ctk.CTkLabel(
                            header_frame,
                            text=timestamp,
                            font=ctk.CTkFont(size=10),
                            text_color=colors['text_secondary']
                        )
                        time_label.pack(side="right")
                    
                    # 消息内容
                    content_label = ctk.CTkLabel(
                        msg_card,
                        text=content,
                        anchor="w",
                        justify="left",
                        wraplength=720,
                        font=ctk.CTkFont(size=12),
                        text_color=colors['text_primary']
                    )
                    content_label.pack(fill="x", padx=20, pady=(0, 15), anchor="w")
            else:
                # 空状态卡片
                empty_card = ctk.CTkFrame(
                    container,
                    corner_radius=12,
                    fg_color=colors['gray_50'],
                    border_width=1,
                    border_color=colors['gray_200'],
                    height=200
                )
                empty_card.pack(fill="x", pady=20, padx=10)
                empty_card.pack_propagate(False)
                
                empty_icon = ctk.CTkLabel(
                    empty_card,
                    text="💬",
                    font=ctk.CTkFont(size=48)
                )
                empty_icon.pack(pady=(50, 10))
                
                empty_text = ctk.CTkLabel(
                    empty_card,
                    text="暂无历史记录",
                    font=ctk.CTkFont(size=16, weight="bold"),
                    text_color=colors['text_secondary']
                )
                empty_text.pack()
                
                empty_desc = ctk.CTkLabel(
                    empty_card,
                    text="开始与AI助手对话，您的历史记录将在这里显示",
                    font=ctk.CTkFont(size=12),
                    text_color=colors['text_secondary']
                )
                empty_desc.pack(pady=(5, 0))
                
        except Exception as e:
            print("打开问答历史窗口失败:", e)

    def open_login_page(self):
        """打开更美观的登录窗口，登录成功后显示头像"""
        try:
            login_win = ctk.CTkToplevel(self)
            login_win.title("🔑 用户登录")
            login_win.geometry("440x600")
            login_win.configure(fg_color="#ffffff")

            # 彩色顶部栏
            header = ctk.CTkFrame(login_win, height=200, corner_radius=0, fg_color="#6366f1")
            header.pack(fill="x", side="top")
            header.pack_propagate(False)

            # 顶部标题
            ctk.CTkLabel(
                header,
                text="欢迎登录",
                font=ctk.CTkFont(size=24, weight="bold"),
                text_color="#ffffff"
            ).pack(pady=(20,6))

            # 中间圆形头像占位
            avatar_frame = ctk.CTkFrame(
                header,
                width=120,
                height=120,
                corner_radius=60,
                fg_color="#eef2ff"
            )
            avatar_frame.pack(pady=(0, 15))
            avatar_frame.pack_propagate(False)
            ctk.CTkLabel(
                avatar_frame,
                text="🧑‍🎓",
                font=ctk.CTkFont(size=48)
            ).pack(expand=True)

            # 主内容卡片
            card = ctk.CTkFrame(login_win, corner_radius=12, fg_color="#ffffff", border_width=0)
            card.pack(fill="both", expand=True, padx=40, pady=(20,20))

            ctk.CTkLabel(card, text="用户名", anchor="w", font=ctk.CTkFont(size=16, weight="bold")).pack(fill="x", pady=(10,6))  # 增大标签字体
            username_entry = ctk.CTkEntry(
                card,
                placeholder_text="请输入用户名",
                font=ctk.CTkFont(size=16),  # 增大字体
                height=56,  # 增大高度
                corner_radius=8,
                border_width=0,
                fg_color=("#dbeafe", "#1e293b")  # 使用柔和蓝色背景
            )
            username_entry.pack(fill="x", pady=(0,12))

            ctk.CTkLabel(card, text="密码", anchor="w", font=ctk.CTkFont(size=16, weight="bold")).pack(fill="x", pady=(10,6))  # 增大标签字体
            password_entry = ctk.CTkEntry(
                card,
                show="*",
                placeholder_text="请输入密码",
                font=ctk.CTkFont(size=16),  # 增大字体
                height=56,  # 增大高度
                corner_radius=8,
                border_width=0,
                fg_color=("#dbeafe", "#1e293b")  # 使用柔和蓝色背景
            )
            password_entry.pack(fill="x", pady=(0,20))

            info_label = ctk.CTkLabel(card, text="", text_color="#dc2626")
            info_label.pack(pady=(0,10))

            def _do_login():
                username = username_entry.get().strip()
                pwd = password_entry.get().strip()
                if not username or not pwd:
                    info_label.configure(text="请输入完整的用户名和密码")
                    return

                # 简易验证 - 预设账号
                if username == "demo" and pwd == "123456":
                    login_win.destroy()
                    self._on_login_success(username)
                else:
                    info_label.configure(text="账号或密码错误")

            login_btn = ctk.CTkButton(
                card,
                text="立即登录 ➜",
                command=_do_login,
                fg_color=("#6366f1", "#4f46e5"),
                hover_color=("#4f46e5", "#4338ca"),
                corner_radius=12,
                font=ctk.CTkFont(size=16, weight="bold"),
                height=54
            )
            login_btn.pack(fill="x", pady=(10, 20))
        except Exception as e:
            print("打开登录窗口失败:", e)

    def _on_login_success(self, username: str):
        """登录成功后在状态栏显示圆形头像"""
        try:
            # 如果已存在头像，不重复创建
            if hasattr(self, "user_avatar_label") and self.user_avatar_label:
                return

            # 获取右侧状态栏容器（在 setup_status_bar 中创建）
            status_right = self.status_bar.winfo_children()[0].winfo_children()[-1]

            # 插入最左侧头像
            status_right.grid_columnconfigure(0, weight=0)

            self.user_avatar_label = ctk.CTkLabel(
                status_right,
                text="🧑‍🎓",
                width=36,
                height=36,
                font=ctk.CTkFont(size=18),
                fg_color="#e0e7ff",
                corner_radius=18,
                text_color="#4f46e5",
            )
            self.user_avatar_label.grid(row=0, column=0, padx=(0, 18))

            # 将现有控件列索引整体右移 1
            for child in status_right.winfo_children():
                info = child.grid_info()
                col = info.get("column")
                if col is not None:
                    col_int = int(col)
                    if child is not self.user_avatar_label and col_int >= 0:
                        child.grid(column=col_int + 1)

            # 更新列配置数量
            total_cols = len(status_right.grid_slaves(row=0))
            for i in range(total_cols):
                status_right.grid_columnconfigure(i, weight=0)
        except Exception as e:
            print("显示头像失败:", e)

def main():
    """主函数 - 添加安全防护避免segmentation fault"""
    try:
        print("🚀 正在启动睿课云眸 AI学习监督系统...")

        # 设置CustomTkinter主题 - 添加错误处理
        try:
            ctk.set_appearance_mode("light")  # 强制使用light模式避免系统主题冲突
            ctk.set_default_color_theme("blue")
            print("✅ CustomTkinter主题设置成功")
        except Exception as theme_error:
            print(f"⚠️ 主题设置失败，使用默认设置: {theme_error}")

        # 注册自定义颜色主题，增强UI美感
        custom_theme = {
            "color": {
                "button": ["#3a7bd5", "#5b9de3"],
                "button_hover": ["#2c6fc7", "#4a8dd3"],
                "button_border": ["#2a67b8", "#3f7ac0"],
                "checkbox_border": ["#3a7bd5", "#5b9de3"],
                "checkmark": ["#ffffff", "#ffffff"],
                "entry": ["#f0f2f5", "#2c3035"],
                "entry_border": ["#c5c9d1", "#48525f"],
                "entry_placeholder_text": ["#8e95a3", "#5e6776"],
                "frame_border": ["#e0e0e0", "#3f4551"],
                "frame_low": ["#f0f2f5", "#2c3035"],
                "frame_high": ["#e8eef7", "#2a303a"],
                "label": [("#505050", "#d0d0d0")],
                "text": ["#000000", "#ffffff"],
                "text_disabled": ["#8e95a3", "#5e6776"],
                "text_button_disabled": ["#8e95a3", "#5e6776"],
                "progressbar": ["#3a7bd5", "#5b9de3"],
                "progressbar_progress": ["#2a67b8", "#3f7ac0"],
                "progressbar_border": ["#c5c9d1", "#48525f"],
                "slider": ["#3a7bd5", "#5b9de3"],
                "slider_progress": ["#2c6fc7", "#4a8dd3"],
                "slider_button": ["#3a7bd5", "#5b9de3"],
                "slider_button_hover": ["#2a67b8", "#3f7ac0"],
                "switch": ["#c5c9d1", "#48525f"],
                "switch_progress": ["#3a7bd5", "#5b9de3"],
                "switch_button": ["#ffffff", "#ffffff"],
                "switch_button_hover": ["#f0f0f0", "#e0e0e0"],
                "dropdown": ["#f0f2f5", "#2c3035"],
                "dropdown_hover": ["#e8eef7", "#2a303a"],
                "dropdown_text": ["#505050", "#d0d0d0"],
                "scrollbar_button": ["#c0c4c9", "#4d5666"],
                "scrollbar_button_hover": ["#a8adb6", "#606a7d"]
            }
        }

        print("🔧 正在初始化应用程序...")

        # 创建并启动应用 - 添加错误处理
        try:
            app = MultimediaAssistantApp()
            print("✅ 应用程序初始化成功")
        except Exception as app_error:
            print(f"❌ 应用程序初始化失败: {app_error}")
            import traceback
            traceback.print_exc()
            return

        # 设置窗口属性 - 添加错误处理
        try:
            app.title("睿课云眸")
            app.geometry("1250x800")
            app.minsize(1000, 700)  # 设置最小窗口大小
            print("✅ 窗口属性设置成功")
        except Exception as window_error:
            print(f"⚠️ 窗口属性设置失败: {window_error}")

        print("🎯 启动主事件循环...")

        # 运行应用 - 添加错误处理
        try:
            app.mainloop()
        except Exception as mainloop_error:
            print(f"❌ 主事件循环错误: {mainloop_error}")
            import traceback
            traceback.print_exc()
        finally:
            print("🔚 应用程序已退出")

    except Exception as main_error:
        print(f"❌ 主函数发生严重错误: {main_error}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def quit_app(app):
    """Clean up and quit application"""
    try:
        print("正在安全退出应用程序...")
        
        # 停止摄像头
        if hasattr(app, 'webcam_handler') and app.webcam_handler:
            app.webcam_handler.stop()
        
        # 停止音频播放器和所有线程
        if hasattr(app, 'audio_player') and app.audio_player:
            app.audio_player.stop()
        
        # 停止监听
        if hasattr(app, 'voice_detector') and app.voice_detector:
            app.voice_detector.stop_monitoring()
        
        # 停止DeepSeek聊天模块
        if hasattr(app, 'qa_deepseek_client') and app.qa_deepseek_client:
            pass  # DeepSeek客户端不需要显式停止
            print("DeepSeek聊天模块已停止")
        
        # 销毁相机窗口
        if hasattr(app.webcam_handler, 'camera_window') and app.webcam_handler.camera_window:
            app.webcam_handler.camera_window.destroy()
        
        # 执行最后一次内存清理
        perform_memory_cleanup()
        
        # 销毁主窗口
        app.quit()
        
        print("应用程序已安全退出")
    except Exception as e:
        print(f"退出时发生错误: {e}")
        # 强制退出
        app.quit()


    def _scroll_qa_to_bottom(self):
        """滚动问答区域到底部"""
        try:
            if hasattr(self, 'qa_scroll_frame'):
                self.qa_scroll_frame._parent_canvas.yview_moveto(1.0)
        except Exception as e:
            print(f"滚动到底部时出错: {e}")
    
    def clear_qa_messages(self):
        """清空问答消息"""
        try:
            if hasattr(self, 'qa_messages'):
                for msg in self.qa_messages:
                    if msg['frame'].winfo_exists():
                        msg['frame'].destroy()
                self.qa_messages.clear()
                print("问答消息已清空")
        except Exception as e:
            print(f"清空问答消息时出错: {e}")
    
    def resize_qa_input(self, event=None):
        """自动调整输入框高度"""
        try:
            content = self.qa_input.get()
            lines = content.count('\n') + 1
            new_height = min(max(lines * 20, 40), 120)  # 最小40px，最大120px
            self.qa_input.configure(height=new_height)
        except Exception as e:
            print(f"调整输入框高度时出错: {e}")

if __name__ == "__main__":
    main()


# 注意：语音提示功能已启用
# 在MultimediaAssistantApp.__init__中已初始化AudioPlayer
# 并在process_image_analysis方法中添加了语音播放代码
