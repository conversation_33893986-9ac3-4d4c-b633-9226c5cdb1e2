#!/bin/bash
# 睿课云眸 AI学习监督系统 - Linux启动脚本
# 执行权限: chmod +x start_linux.sh
# 运行方式: ./start_linux.sh

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 打印启动横幅
echo -e "${CYAN}╔═══════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║               睿课云眸 AI学习监督系统               ║${NC}"
echo -e "${CYAN}║               Linux 专用启动脚本                  ║${NC}"
echo -e "${CYAN}╚═══════════════════════════════════════════════╝${NC}"
echo ""

# 切换到脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"
echo -e "${BLUE}📍 当前目录: $(pwd)${NC}"

# Linux 特定环境设置
export DISPLAY=${DISPLAY:-:0}
export GTK_THEME=${GTK_THEME:-""}

# 检查X11或Wayland显示服务器
if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
    echo -e "${YELLOW}⚠️  警告: 未检测到图形界面环境，可能需要配置DISPLAY变量${NC}"
fi

# 检查Python
echo -e "${YELLOW}🐍 检查Python环境...${NC}"
PYTHON_CMD=""

# 尝试不同的Python命令
for cmd in python3.11 python3.10 python3.9 python3.8 python3 python; do
    if command -v $cmd &> /dev/null; then
        VERSION=$($cmd --version 2>&1)
        # 检查版本是否满足要求 (3.8+)
        if $cmd -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)" 2>/dev/null; then
            PYTHON_CMD=$cmd
            echo -e "${GREEN}✅ 找到合适的Python: $VERSION ($cmd)${NC}"
            break
        else
            echo -e "${YELLOW}⚠️  $cmd 版本过低: $VERSION${NC}"
        fi
    fi
done

if [ -z "$PYTHON_CMD" ]; then
    echo -e "${RED}❌ 未找到Python 3.8+，请先安装${NC}"
    echo "Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
    echo "CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "Fedora: sudo dnf install python3 python3-pip"
    echo "Arch: sudo pacman -S python python-pip"
    echo ""
    read -p "按回车键退出..." -r
    exit 1
fi

# 检查系统依赖库
echo -e "${YELLOW}📦 检查系统依赖...${NC}"

# 检查tkinter (GUI库)
if ! $PYTHON_CMD -c "import tkinter" 2>/dev/null; then
    echo -e "${RED}❌ 缺少tkinter库，正在尝试安装...${NC}"

    # 检测Linux发行版并安装tkinter
    if command -v apt &> /dev/null; then
        echo "检测到Debian/Ubuntu系统，使用apt安装..."
        sudo apt update && sudo apt install -y python3-tk
    elif command -v yum &> /dev/null; then
        echo "检测到CentOS/RHEL系统，使用yum安装..."
        sudo yum install -y tkinter
    elif command -v dnf &> /dev/null; then
        echo "检测到Fedora系统，使用dnf安装..."
        sudo dnf install -y python3-tkinter
    elif command -v pacman &> /dev/null; then
        echo "检测到Arch系统，使用pacman安装..."
        sudo pacman -S tk
    else
        echo -e "${YELLOW}⚠️  无法自动安装tkinter，请手动安装${NC}"
        echo "Ubuntu/Debian: sudo apt install python3-tk"
        echo "CentOS/RHEL: sudo yum install tkinter"
        echo "Fedora: sudo dnf install python3-tkinter"
        echo "Arch: sudo pacman -S tk"
    fi
else
    echo -e "${GREEN}✅ tkinter库正常${NC}"
fi

# 检查主程序文件
echo -e "${YELLOW}📝 检查主程序文件...${NC}"
if [ -f "ruike_yunmou.py" ]; then
    echo -e "${GREEN}✅ 主程序文件存在: ruike_yunmou.py${NC}"
else
    echo -e "${RED}❌ 未找到主程序文件 ruike_yunmou.py${NC}"
    echo "请确保在正确的目录中运行此脚本"
    read -p "按回车键退出..." -r
    exit 1
fi

# 检查配置文件
echo -e "${YELLOW}📝 检查配置文件...${NC}"
if [ -f "config.py" ]; then
    echo -e "${GREEN}✅ 配置文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  警告: 未找到config.py配置文件${NC}"
fi

if [ -f "requirements.txt" ]; then
    echo -e "${GREEN}✅ 依赖文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  警告: 未找到requirements.txt依赖文件${NC}"
fi

# 检查和安装Python依赖
echo -e "${YELLOW}📦 检查Python依赖库...${NC}"
echo "正在检查关键依赖库是否已安装..."

# 首先尝试使用requirements.txt批量安装
if [ -f "requirements.txt" ]; then
    echo -e "${BLUE}🔄 使用requirements.txt安装依赖...${NC}"
    if $PYTHON_CMD -m pip install -r requirements.txt --quiet 2>/dev/null; then
        echo -e "${GREEN}✅ 批量安装完成${NC}"
    elif $PYTHON_CMD -m pip install -r requirements.txt --user --quiet 2>/dev/null; then
        echo -e "${GREEN}✅ 批量安装完成 (用户模式)${NC}"
    else
        echo -e "${YELLOW}⚠️  批量安装失败，将逐个检查依赖${NC}"
    fi
else
    echo -e "${YELLOW}📝 逐个检查和安装关键依赖...${NC}"

    # 检查关键依赖
    install_package() {
        local package=$1
        local import_name=$2
        if ! $PYTHON_CMD -c "import $import_name" 2>/dev/null; then
            echo -e "${RED}❌ 缺少$import_name库，正在安装...${NC}"
            if $PYTHON_CMD -m pip install $package --quiet 2>/dev/null; then
                echo -e "${GREEN}✅ $package 安装成功${NC}"
            elif $PYTHON_CMD -m pip install $package --user --quiet 2>/dev/null; then
                echo -e "${GREEN}✅ $package 安装成功 (用户模式)${NC}"
            else
                echo -e "${RED}❌ $package 安装失败${NC}"
            fi
        fi
    }

    install_package "customtkinter" "customtkinter"
    install_package "opencv-python" "cv2"
    install_package "Pillow" "PIL"
    install_package "requests" "requests"
    install_package "pygame" "pygame"
fi

# 检查可选依赖
if ! $PYTHON_CMD -c "import paddleocr" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  可选: PaddleOCR未安装，题目识别功能将不可用${NC}"
    echo "   如需使用请运行: pip install paddleocr"
fi

if ! $PYTHON_CMD -c "import matplotlib" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  可选: matplotlib未安装，图表功能将不可用${NC}"
    echo "   如需使用请运行: pip install matplotlib"
fi

echo -e "${GREEN}✅ 依赖检查完成${NC}"

# 启动程序
echo ""
echo -e "${GREEN}🚀 正在启动睿课云眸AI学习监督系统...${NC}"
echo -e "${PURPLE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

echo -e "${BLUE}启动主程序: ruike_yunmou.py${NC}"
$PYTHON_CMD ruike_yunmou.py

# 检查退出状态
EXIT_CODE=$?
echo ""
echo -e "${PURPLE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ 程序正常退出${NC}"
else
    echo -e "${RED}❌ 程序异常退出 (退出码: $EXIT_CODE)${NC}"
    echo -e "${YELLOW}如果遇到问题，请检查:${NC}"
    echo "1. Python环境是否正确安装"
    echo "2. 依赖库是否完整安装 (运行: pip install -r requirements.txt)"
    echo "3. 摄像头权限是否允许"
    echo "4. 网络连接是否正常"
    echo "5. 图形界面环境是否正常 (DISPLAY变量)"
fi

echo ""
read -p "按回车键关闭终端..." -r