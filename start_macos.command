#!/bin/bash
# 睿课云眸 AI学习监督系统 - macOS启动脚本
# 双击运行或在终端中执行: ./start_macos.command

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 打印启动横幅
echo -e "${GREEN}╔═══════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║               睿课云眸 AI学习监督系统               ║${NC}"
echo -e "${GREEN}║              macOS 专用启动脚本                   ║${NC}"
echo -e "${GREEN}╚═══════════════════════════════════════════════╝${NC}"
echo ""

# 切换到脚本所在目录
cd "$(dirname "$0")"
echo -e "${BLUE}📍 当前目录: $(pwd)${NC}"

# macOS 特定环境设置
export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
export OMP_NUM_THREADS=1
export TK_SILENCE_DEPRECATION=1

# 检查Python
echo -e "${YELLOW}🐍 检查Python环境...${NC}"
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo -e "${GREEN}✅ 找到Python: $PYTHON_VERSION${NC}"
    PYTHON_CMD=python3
elif command -v python &> /dev/null; then
    PYTHON_VERSION=$(python --version)
    echo -e "${GREEN}✅ 找到Python: $PYTHON_VERSION${NC}"
    PYTHON_CMD=python
else
    echo -e "${RED}❌ 未找到Python，请先安装Python 3.8+${NC}"
    echo "请访问 https://www.python.org/downloads/ 下载安装"
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

# 检查主程序文件
echo -e "${YELLOW}📝 检查主程序文件...${NC}"
if [ -f "ruike_yunmou.py" ]; then
    echo -e "${GREEN}✅ 主程序文件存在: ruike_yunmou.py${NC}"
else
    echo -e "${RED}❌ 未找到主程序文件 ruike_yunmou.py${NC}"
    echo "请确保在正确的目录中运行此脚本"
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

# 检查配置文件
echo -e "${YELLOW}📝 检查配置文件...${NC}"
if [ -f "config.py" ]; then
    echo -e "${GREEN}✅ 配置文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  警告: 未找到config.py配置文件${NC}"
fi

if [ -f "requirements.txt" ]; then
    echo -e "${GREEN}✅ 依赖文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  警告: 未找到requirements.txt依赖文件${NC}"
fi

# 检查和安装依赖
echo -e "${YELLOW}📦 检查Python依赖库...${NC}"
echo "正在检查关键依赖库是否已安装..."

# 检测conda环境
CONDA_ENV=""
if command -v conda &> /dev/null; then
    CONDA_ENV=$(conda info --envs | grep '*' | awk '{print $1}')
    echo -e "${BLUE}🐍 检测到Conda环境: $CONDA_ENV${NC}"
fi

# 首先尝试使用requirements.txt批量安装
if [ -f "requirements.txt" ]; then
    echo -e "${BLUE}🔄 使用requirements.txt安装依赖...${NC}"

    # 为conda环境优化安装策略
    if [ ! -z "$CONDA_ENV" ]; then
        echo -e "${BLUE}📦 使用conda优先安装策略...${NC}"
        # 尝试conda安装常见包
        conda install -y numpy opencv pillow requests pygame -c conda-forge 2>/dev/null || true
    fi

    # 尝试不同的pip安装方式
    if $PYTHON_CMD -m pip install -r requirements.txt --quiet --no-warn-script-location 2>/dev/null; then
        echo -e "${GREEN}✅ 批量安装完成${NC}"
    elif $PYTHON_CMD -m pip install -r requirements.txt --user --quiet --no-warn-script-location 2>/dev/null; then
        echo -e "${GREEN}✅ 批量安装完成 (用户模式)${NC}"
    elif $PYTHON_CMD -m pip install -r requirements.txt --break-system-packages --quiet --no-warn-script-location 2>/dev/null; then
        echo -e "${YELLOW}✅ 批量安装完成 (系统包模式)${NC}"
    else
        echo -e "${YELLOW}⚠️  批量安装失败，将逐个检查依赖${NC}"
        echo -e "${BLUE}💡 这通常是由于某些包的版本冲突导致的${NC}"
    fi
else
    echo -e "${YELLOW}📝 逐个检查和安装关键依赖...${NC}"

    # 检查关键依赖
    install_package() {
        local package=$1
        local import_name=$2
        local conda_name=${3:-$package}  # conda包名，默认与pip包名相同

        if ! $PYTHON_CMD -c "import $import_name" 2>/dev/null; then
            echo -e "${RED}❌ 缺少$import_name库，正在安装...${NC}"

            # 如果在conda环境中，优先尝试conda安装
            if [ ! -z "$CONDA_ENV" ] && command -v conda &> /dev/null; then
                if conda install -y $conda_name -c conda-forge 2>/dev/null; then
                    echo -e "${GREEN}✅ $package 安装成功 (conda)${NC}"
                    return
                fi
            fi

            # 尝试pip安装的多种方式
            if $PYTHON_CMD -m pip install $package --quiet --no-warn-script-location 2>/dev/null; then
                echo -e "${GREEN}✅ $package 安装成功${NC}"
            elif $PYTHON_CMD -m pip install $package --user --quiet --no-warn-script-location 2>/dev/null; then
                echo -e "${GREEN}✅ $package 安装成功 (用户模式)${NC}"
            elif $PYTHON_CMD -m pip install $package --break-system-packages --quiet --no-warn-script-location 2>/dev/null; then
                echo -e "${YELLOW}✅ $package 安装成功 (系统包模式)${NC}"
            else
                echo -e "${RED}❌ $package 安装失败，请手动安装: pip install $package${NC}"
            fi
        else
            echo -e "${GREEN}✅ $import_name 已安装${NC}"
        fi
    }

    echo -e "${BLUE}🔍 检查核心依赖库...${NC}"
    install_package "customtkinter" "customtkinter" "customtkinter"
    install_package "opencv-python" "cv2" "opencv"
    install_package "Pillow" "PIL" "pillow"
    install_package "requests" "requests" "requests"
    install_package "pygame" "pygame" "pygame"
    install_package "numpy" "numpy" "numpy"
fi

# 检查可选依赖
echo -e "${BLUE}🔍 检查可选功能依赖...${NC}"

# PaddleOCR安装和优化
if ! $PYTHON_CMD -c "import paddleocr" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  PaddleOCR未安装，题目识别功能将不可用${NC}"
    echo -e "${BLUE}💡 是否安装PaddleOCR以启用题目识别功能？(y/N)${NC}"
    read -t 10 -n 1 install_paddle
    echo ""
    if [[ $install_paddle =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🔄 正在安装PaddleOCR...${NC}"
        echo -e "${YELLOW}📥 首次安装将下载约500MB的模型文件，请耐心等待...${NC}"

        # 设置PaddleOCR优化环境变量
        export PADDLE_DISABLE_SIGNAL_HANDLER=1
        export FLAGS_allocator_strategy=naive_best_fit

        if $PYTHON_CMD -m pip install paddleocr --quiet --no-warn-script-location 2>/dev/null; then
            echo -e "${GREEN}✅ PaddleOCR安装成功${NC}"
        else
            echo -e "${RED}❌ PaddleOCR安装失败，可稍后手动安装: pip install paddleocr${NC}"
        fi
    else
        echo -e "${YELLOW}⏭️  跳过PaddleOCR安装${NC}"
    fi
else
    echo -e "${GREEN}✅ PaddleOCR已安装${NC}"
    # 设置PaddleOCR运行时优化环境变量
    export PADDLE_DISABLE_SIGNAL_HANDLER=1
    export FLAGS_allocator_strategy=naive_best_fit
    export OMP_NUM_THREADS=1
fi

# matplotlib检查
if ! $PYTHON_CMD -c "import matplotlib" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  matplotlib未安装，图表功能将使用文本模式${NC}"
    echo "   如需图表功能请运行: pip install matplotlib"
else
    echo -e "${GREEN}✅ matplotlib已安装${NC}"
fi

echo -e "${GREEN}✅ 依赖检查完成${NC}"

# 启动程序
echo ""
echo -e "${GREEN}🚀 正在启动睿课云眸AI学习监督系统...${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo ""

echo -e "${BLUE}启动主程序: ruike_yunmou.py${NC}"
$PYTHON_CMD ruike_yunmou.py

# 检查退出状态
EXIT_CODE=$?
echo ""
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ 程序正常退出${NC}"
else
    echo -e "${RED}❌ 程序异常退出 (退出码: $EXIT_CODE)${NC}"
    echo -e "${YELLOW}如果遇到问题，请检查:${NC}"
    echo "1. Python环境是否正确安装"
    echo "2. 依赖库是否完整安装"
    echo "3. 摄像头权限是否允许"
    echo "4. 网络连接是否正常"
fi

echo ""
echo "按任意键关闭此窗口..."
read -n 1