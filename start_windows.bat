@echo off
chcp 65001 >nul
REM 睿课云眸 AI学习监督系统 - Windows启动脚本
REM 双击运行

title 睿课云眸 AI学习监督系统

echo ╔═══════════════════════════════════════════════╗
echo ║               睿课云眸 AI学习监督系统               ║
echo ║              Windows 专用启动脚本                 ║
echo ╚═══════════════════════════════════════════════╝
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"
echo 📍 当前目录: %CD%

REM 检查Python
echo 🐍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到Python
    set PYTHON_CMD=python
    goto :check_files
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到Python3
    set PYTHON_CMD=python3
    goto :check_files
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到Python (py启动器)
    set PYTHON_CMD=py
    goto :check_files
)

echo ❌ 未找到Python，请先安装Python 3.8+
echo 请访问 https://www.python.org/downloads/ 下载安装
pause
exit /b 1

:check_files
REM 检查主程序文件
echo 📝 检查主程序文件...
if exist "ruike_yunmou.py" (
    echo ✅ 主程序文件存在: ruike_yunmou.py
) else (
    echo ❌ 未找到主程序文件 ruike_yunmou.py
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 检查配置文件
echo 📝 检查配置文件...
if exist "config.py" (
    echo ✅ 配置文件存在
) else (
    echo ⚠️  警告: 未找到config.py配置文件
)

if exist "requirements.txt" (
    echo ✅ 依赖文件存在
) else (
    echo ⚠️  警告: 未找到requirements.txt依赖文件
)

REM 检查和安装依赖
echo 📦 检查Python依赖库...
echo 正在检查关键依赖库是否已安装...

REM 首先尝试使用requirements.txt批量安装
if exist "requirements.txt" (
    echo 🔄 使用requirements.txt安装依赖...
    %PYTHON_CMD% -m pip install -r requirements.txt --quiet >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 批量安装完成
    ) else (
        echo ⚠️  批量安装失败，将逐个检查依赖
        goto :individual_check
    )
) else (
    :individual_check
    echo 📝 逐个检查和安装关键依赖...

    REM 检查关键依赖
    %PYTHON_CMD% -c "import customtkinter" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 缺少customtkinter库，正在安装...
        %PYTHON_CMD% -m pip install customtkinter
    )

    %PYTHON_CMD% -c "import cv2" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 缺少opencv库，正在安装...
        %PYTHON_CMD% -m pip install opencv-python
    )

    %PYTHON_CMD% -c "import PIL" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 缺少PIL库，正在安装...
        %PYTHON_CMD% -m pip install Pillow
    )

    %PYTHON_CMD% -c "import requests" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 缺少requests库，正在安装...
        %PYTHON_CMD% -m pip install requests
    )

    %PYTHON_CMD% -c "import pygame" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 缺少pygame库，正在安装...
        %PYTHON_CMD% -m pip install pygame
    )
)

REM 检查可选依赖
%PYTHON_CMD% -c "import paddleocr" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  可选: PaddleOCR未安装，题目识别功能将不可用
    echo    如需使用请运行: pip install paddleocr
)

%PYTHON_CMD% -c "import matplotlib" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  可选: matplotlib未安装，图表功能将不可用
    echo    如需使用请运行: pip install matplotlib
)

echo ✅ 依赖检查完成

REM 启动程序
echo.
echo 🚀 正在启动睿课云眸AI学习监督系统...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

echo 启动主程序: ruike_yunmou.py
%PYTHON_CMD% ruike_yunmou.py

REM 检查退出状态
echo.
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
if %errorlevel% equ 0 (
    echo ✅ 程序正常退出
) else (
    echo ❌ 程序异常退出 (错误代码: %errorlevel%)
    echo 如果遇到问题，请检查:
    echo 1. Python环境是否正确安装
    echo 2. 依赖库是否完整安装
    echo 3. 摄像头权限是否允许
    echo 4. 网络连接是否正常
)

echo.
echo 按任意键关闭窗口...
pause >nul