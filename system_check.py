#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统环境检查脚本
==============
检查运行睿课云眸系统所需的环境和依赖
"""

import sys
import platform
import subprocess
import importlib
from pathlib import Path

class SystemChecker:
    """系统检查器"""
    
    def __init__(self):
        self.issues = []
        self.warnings = []
        
    def print_header(self):
        """打印检查开始信息"""
        print("=" * 60)
        print("🔍 睿课云眸系统环境检查")
        print("=" * 60)
        print()
        
    def check_python_version(self):
        """检查Python版本"""
        print("🐍 检查Python版本...")
        version = sys.version_info
        
        if version.major < 3:
            self.issues.append("Python版本过低，需要Python 3.8+")
            print(f"❌ Python {version.major}.{version.minor}.{version.micro}")
            return False
        elif version.major == 3 and version.minor < 8:
            self.issues.append("Python版本过低，需要Python 3.8+")
            print(f"❌ Python {version.major}.{version.minor}.{version.micro}")
            return False
        else:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
            return True
    
    def check_platform(self):
        """检查操作系统"""
        print("\n💻 检查操作系统...")
        system = platform.system()
        version = platform.version()
        
        print(f"✅ {system} {version}")
        
        if system not in ["Windows", "Darwin", "Linux"]:
            self.warnings.append(f"未测试的操作系统: {system}")
        
        return True
    
    def check_required_modules(self):
        """检查必需的Python模块"""
        print("\n📦 检查Python模块...")
        
        required_modules = {
            'tkinter': '标准库 - GUI界面',
            'threading': '标准库 - 多线程',
            'queue': '标准库 - 队列',
            'json': '标准库 - JSON处理',
            'os': '标准库 - 操作系统接口',
            'sys': '标准库 - 系统参数',
            'time': '标准库 - 时间处理',
            'datetime': '标准库 - 日期时间',
            'pathlib': '标准库 - 路径处理',
            'subprocess': '标准库 - 子进程',
            'logging': '标准库 - 日志'
        }
        
        for module, description in required_modules.items():
            try:
                importlib.import_module(module)
                print(f"✅ {module:<15} - {description}")
            except ImportError:
                print(f"❌ {module:<15} - {description}")
                self.issues.append(f"缺少标准库模块: {module}")
        
        return len(self.issues) == 0
    
    def check_optional_modules(self):
        """检查可选模块"""
        print("\n🔧 检查扩展模块...")
        
        optional_modules = {
            'customtkinter': '现代化UI界面',
            'cv2': 'OpenCV - 计算机视觉',
            'PIL': 'Pillow - 图像处理',
            'requests': 'HTTP请求库',
            'pygame': '音频播放',
            'numpy': '数值计算',
            'oss2': '阿里云对象存储',
            'edge_tts': 'Edge语音合成'
        }
        
        missing_modules = []
        for module, description in optional_modules.items():
            try:
                if module == 'cv2':
                    import cv2
                elif module == 'PIL':
                    from PIL import Image
                elif module == 'edge_tts':
                    import edge_tts
                else:
                    importlib.import_module(module)
                print(f"✅ {module:<15} - {description}")
            except ImportError:
                print(f"❌ {module:<15} - {description}")
                missing_modules.append(module)
        
        if missing_modules:
            self.warnings.append(f"缺少扩展模块: {', '.join(missing_modules)}")
            self.warnings.append("运行 'python install_dependencies.py' 安装依赖")
        
        return len(missing_modules) == 0
    
    def check_camera(self):
        """检查摄像头"""
        print("\n📹 检查摄像头...")
        try:
            import cv2
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print("✅ 摄像头可用")
                    cap.release()
                    return True
                else:
                    print("⚠️  摄像头无法读取图像")
                    self.warnings.append("摄像头无法读取图像")
            else:
                print("❌ 无法打开摄像头")
                self.issues.append("无法打开摄像头")
            cap.release()
        except ImportError:
            print("⚠️  OpenCV未安装，无法检查摄像头")
            self.warnings.append("OpenCV未安装，无法检查摄像头")
        except Exception as e:
            print(f"❌ 摄像头检查失败: {e}")
            self.issues.append(f"摄像头检查失败: {e}")
        
        return False
    
    def check_audio(self):
        """检查音频系统"""
        print("\n🔊 检查音频系统...")
        try:
            import pygame
            pygame.mixer.init()
            print("✅ 音频系统可用")
            pygame.mixer.quit()
            return True
        except ImportError:
            print("⚠️  Pygame未安装，无法检查音频")
            self.warnings.append("Pygame未安装，无法检查音频")
        except Exception as e:
            print(f"❌ 音频系统检查失败: {e}")
            self.warnings.append(f"音频系统检查失败: {e}")
        
        return False
    
    def check_network(self):
        """检查网络连接"""
        print("\n🌐 检查网络连接...")
        try:
            import requests
            response = requests.get("https://www.baidu.com", timeout=5)
            if response.status_code == 200:
                print("✅ 网络连接正常")
                return True
            else:
                print("⚠️  网络连接异常")
                self.warnings.append("网络连接可能有问题")
        except ImportError:
            print("⚠️  Requests未安装，无法检查网络")
            self.warnings.append("Requests未安装，无法检查网络")
        except Exception as e:
            print(f"⚠️  网络检查失败: {e}")
            self.warnings.append("网络连接可能有问题")
        
        return False
    
    def check_files(self):
        """检查必要文件"""
        print("\n📁 检查项目文件...")
        
        required_files = [
            "main.py",
            "ruike_yunmou.py",
            "config.py",
            "requirements.txt"
        ]
        
        missing_files = []
        for file in required_files:
            if Path(file).exists():
                print(f"✅ {file}")
            else:
                print(f"❌ {file}")
                missing_files.append(file)
        
        if missing_files:
            self.issues.append(f"缺少必要文件: {', '.join(missing_files)}")
        
        return len(missing_files) == 0
    
    def print_summary(self):
        """打印检查总结"""
        print("\n" + "=" * 60)
        print("📋 检查总结")
        print("=" * 60)
        
        if not self.issues and not self.warnings:
            print("🎉 所有检查通过！系统环境完全满足要求。")
            print("现在可以运行睿课云眸系统了。")
        elif not self.issues:
            print("✅ 基本检查通过，但有一些警告:")
            for warning in self.warnings:
                print(f"⚠️  {warning}")
            print("\n可以尝试运行系统，如有问题请解决警告项。")
        else:
            print("❌ 发现严重问题，需要解决后才能运行:")
            for issue in self.issues:
                print(f"❌ {issue}")
            
            if self.warnings:
                print("\n警告项:")
                for warning in self.warnings:
                    print(f"⚠️  {warning}")
        
        print("\n" + "=" * 60)
    
    def run_all_checks(self):
        """运行所有检查"""
        self.print_header()
        
        checks = [
            self.check_python_version,
            self.check_platform, 
            self.check_required_modules,
            self.check_optional_modules,
            self.check_camera,
            self.check_audio,
            self.check_network,
            self.check_files
        ]
        
        for check in checks:
            try:
                check()
            except Exception as e:
                print(f"❌ 检查过程出错: {e}")
                self.issues.append(f"检查过程出错: {e}")
        
        self.print_summary()
        
        return len(self.issues) == 0

def main():
    """主函数"""
    checker = SystemChecker()
    success = checker.run_all_checks()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main() 