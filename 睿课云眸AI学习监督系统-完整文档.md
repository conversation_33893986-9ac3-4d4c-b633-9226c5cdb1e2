# 睿课云眸AI学习监督系统 - 完整文档

## 项目简介

睿课云眸AI学习监督系统是基于算能BM1684X TPU微服务器（32TOPS@INT8）构建的边缘计算框架，通过"视觉特征提取-语义理解-行为决策"三级多模态融合技术，实现对学习行为的精准识别与智能反馈。系统采用标准、轻量、资源受限三种部署模式，适应城乡差异化教育场景，在弱网（100Kbps）环境下仍能稳定运行。

## 核心技术特点

### 多模态动态融合技术
系统核心采用创新的多模态动态融合算法，通过三级分析架构深度整合姿态（40%）、表情（30%）及物品交互（30%）特征。这种融合方式使系统能够同时监测学习者的注意力状态、坐姿健康和情绪变化，形成全方位的学习状态感知能力。相比单模态识别，准确率提升14.7%。

### 三维智能监督系统
基于多模态融合基础，系统实现了三维智能监督功能：注意力监督精准识别11种学习行为状态，识别准确率达92.3%；坐姿监督实时检测6种不良坐姿问题，预防学习健康风险；情绪监督分析10种情绪状态，提供情绪关怀和学习状态优化建议。

### AI拍照解题创新架构
系统集成业内领先的AI拍照解题双引擎架构，融合PaddleOCR（中文识别率95%+）和EasyOCR双引擎OCR系统，支持复杂数学公式识别（准确率90%+）。采用DeepSeek和通义千问双模型协作机制，实现从图像捕获到分步讲解的完整闭环，处理延迟控制在12-20秒。

### 番茄钟时间管理与学习分析
创新集成番茄钟时间管理模块，采用状态机设计模式实现25分钟专注+5分钟休息的科学时间配置，支持1-120分钟个性化时长调整。结合实时学习状态分析系统，通过滑动窗口算法（最近1000条记录）和专注度评估算法，实现学习效率的量化评估和趋势预测。

## 系统性能指标

### 识别准确率
- 行为识别：专注学习96%、玩手机分心94%、疲劳状态89%
- 坐姿监测：驼背检测88%、头部前倾85%、肩膀不平82%
- 情绪分析：基础情绪80%+、复杂情绪75%+
- OCR识别：中文95%+、英文98%+、数学公式90%+

### 系统响应性能
- 视觉模型推理延迟：50ms
- 大模型推理延迟：700ms
- 图像捕获时间：<1秒
- AI解答生成时间：8-15秒
- 语音合成延迟：2-3秒
- 界面响应时间：<100ms

### 资源占用与稳定性
- 内存使用：200-400MB（含自动清理机制）
- CPU占用：15-25%（分析期间峰值）
- 峰值功耗：25W（适合长期部署）
- 72小时内存增长：245MB（稳定性测试）
- 服务可用率：99.5%+

## 快速启动指南

### 环境要求
- Python 3.8+
- 支持的操作系统：Windows 10+、macOS 10.15+、Ubuntu 18.04+
- 摄像头设备（内置或外接）
- 网络连接（可选，离线模式下功能受限）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 睿课云眸AI学习监督系统
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动系统**
   ```bash
   python main.py
   ```

### 启动脚本说明
- `main.py` - 主启动脚本，推荐使用
- `install_dependencies.py` - 依赖安装脚本
- `system_check.py` - 系统环境检查
- `start_windows.bat` - Windows批处理启动
- `start_macos.command` - macOS启动脚本
- `start_linux.sh` - Linux启动脚本

## 功能模块详解

### 智能学习监督
- **行为识别**：识别11种学习行为状态，包括专注学习、注意力分散、与他人交流等
- **坐姿监测**：实时检测6种不良坐姿问题，如驼背、歪头、趴桌等
- **情绪分析**：分析10种情绪状态，包括专注、疲惫、焦虑等
- **语音提醒**：基于Edge TTS的温和鼓励性语音提示

### AI拍照解题
- **一键拍照**：高质量摄像头画面捕获
- **OCR识别**：PaddleOCR和EasyOCR双引擎文字和数学公式识别
- **AI解答**：DeepSeek和通义千问双引擎协作
- **分步讲解**：教学化表达的智能解题思路生成

### 番茄钟时间管理
- **标准计时器**：25分钟专注+5分钟休息的经典番茄工作法
- **自定义时长**：支持1-120分钟范围的个性化时长配置
- **实时显示**：标题栏状态显示和视觉反馈系统
- **智能提醒**：开始、暂停、完成的上下文适应性语音提示

### 学习状态分析
- **实时监控**：多维度数据源的持续学习状态分析
- **数据统计**：基础和高级指标的学习时长和专注度统计
- **报告生成**：卡片式布局的详细学习分析报告
- **趋势分析**：基于移动平均算法的学习效果长期跟踪和预测

## 配置说明

### 基础配置
系统配置文件位于 `config.py`，包含以下主要配置项：
- 摄像头设备选择
- AI模型配置
- 语音引擎设置
- 网络连接参数

### PaddleOCR配置
OCR配置文件位于 `paddleocr_config.py`，支持：
- 多版本兼容性检测
- 自动参数适配
- 安全初始化机制
- 降级处理策略

### TTS语音配置
语音合成配置位于 `enhanced_tts.py`，特性包括：
- Edge TTS和系统TTS双引擎
- 网络连接优化
- SSL警告抑制
- 离线模式支持

## 部署方案

### 城市学校标准部署
采用标准部署模式，充分发挥多模态融合技术优势，支持多终端协同与深度数据分析。完整功能包括三维智能监督、AI拍照解题、番茄钟时间管理和学习状态分析等全方位功能，助力个性化教学方案制定。

### 农村学校轻量部署
采用轻量化部署保留核心监督功能，通过本地缓存与增量同步差分更新技术降低对网络依赖。优先保障注意力监督、坐姿健康监测、基础情绪识别和本地化语音提醒等关键功能，确保在资源受限环境下仍能提供有效的学习监督。

### 个人学习场景部署
支持家庭学习环境部署，提供完整的自主学习管理工具。通过多模态融合技术实现个性化学习习惯培养，结合AI拍照解题提供即时学习支持，配合智能时间管理和长期数据分析。

## 故障排除

### 常见问题
1. **摄像头无法启动**：检查摄像头权限和驱动程序
2. **AI模型加载失败**：确认网络连接和模型文件完整性
3. **语音合成无声音**：检查音频设备和系统音量设置
4. **OCR识别错误**：确认图像质量和光线条件

### 性能优化
- 关闭不必要的后台程序
- 确保充足的内存空间（建议8GB+）
- 使用SSD硬盘提升I/O性能
- 保持稳定的网络连接

## 技术支持

### 系统要求
- 最低配置：4GB RAM，双核CPU，集成显卡
- 推荐配置：8GB+ RAM，四核CPU，独立显卡
- 网络要求：100Kbps+（离线模式下可选）

### 联系方式
如需技术支持，请提供：
- 系统启动日志
- 错误信息截图
- 系统环境信息
- 具体操作步骤

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。在贡献代码前，请确保：
- 代码符合项目规范
- 添加必要的测试
- 更新相关文档
- 通过所有测试用例
